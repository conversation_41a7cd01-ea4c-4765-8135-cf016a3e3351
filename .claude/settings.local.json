{"permissions": {"allow": ["WebFetch(domain:docs.sentry.io)", "Bash(yarn workspaces foreach:*)", "Bash(grep:*)", "<PERSON><PERSON>(jq:*)", "<PERSON><PERSON>(python3:*)", "Bash(node:*)", "Bash(databricks:*)", "WebFetch(domain:www.pulumi.com)", "WebSearch", "Bash(yarn lint:*)", "Bash(yarn workspace wallet tsx:*)", "Bash(yarn workspace wallet run-ts:*)", "Bash(yarn tsx:*)", "Bash(yarn build-coingecko-verified-currency-ids:*)", "Bash(yarn build-currency-matrix-json:*)", "WebFetch(domain:github.com)", "Bash(yarn workspace wallet test:*)", "WebFetch(domain:eips.ethereum.org)", "WebFetch(domain:www.erc4337.io)", "WebFetch(domain:docs.erc4337.io)", "WebFetch(domain:docs.safe.global)", "Bash(find:*)", "Bash(yarn test:*)", "<PERSON><PERSON>(git stash show:*)", "Bash(for i in {0..20})", "Bash(do echo \"=== Stash $i ===\")", "Bash(done)", "Bash(git log:*)", "<PERSON><PERSON>(git show:*)"], "deny": []}}