import { processFetchFailure } from '@zeal/api/interceptors'

import { joinURL } from '@zeal/toolkit/URL/joinURL'

export const MONERIUM_BASE_URL = 'https://api.monerium.app'

export type Paths = {
    post: {
        '/auth': {
            body: object
        }
        '/auth/token': {
            body: object
        }
    }
}

type MoneriumResponse = {
    body: string
    headers: Headers
}

// TODO @resetko-zeal check why we need this separate API for backend, is that just legacy?
export const post = async <T extends keyof Paths['post']>(
    path: T,
    params: {
        body: Paths['post'][T]['body']
    },
    signal?: AbortSignal
): Promise<MoneriumResponse> => {
    const url = joinURL(MONERIUM_BASE_URL, path)

    // eslint-disable-next-line no-restricted-globals
    return fetch(url, {
        method: 'POST',
        body: new URLSearchParams(params.body as Record<string, string>),
        redirect: 'manual',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        signal,
    })
        .then(async (res) => {
            const body = await res.text()

            return { body, headers: res.headers }
        })
        .catch((error: unknown) =>
            processFetchFailure({
                error,
                info: {
                    url,
                    method: 'POST',
                    requestBody: params.body,
                },
            })
        )
}
