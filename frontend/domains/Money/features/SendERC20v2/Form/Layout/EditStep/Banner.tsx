import { FormattedMessage } from 'react-intl'

import { BannerSolid } from '@zeal/uikit/BannerSolid'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { AppErrorBanner } from '@zeal/domains/Error/components/AppErrorBanner'

import { BannerErrors } from './validate'

type Props = {
    installationId: string
    errors?: BannerErrors
    onMsg: (msg: Msg) => void
}

type Msg = MsgOf<typeof AppErrorBanner>

export const Banner = ({ errors, installationId, onMsg }: Props) => {
    if (!errors) {
        return null
    }
    switch (errors.type) {
        case 'pollable_errored':
            return (
                <AppErrorBanner
                    error={errors.appError}
                    onMsg={onMsg}
                    installationId={installationId}
                />
            )
        case 'from_wallet_not_supported_network':
            return (
                <BannerSolid
                    rounded
                    variant="warning"
                    title={
                        <FormattedMessage
                            id="send.banner.walletNotSupportedNetwork.title"
                            defaultMessage="Token network not supported"
                        />
                    }
                    subtitle={
                        <FormattedMessage
                            id="send.banner.walletNotSupportedNetwork.subtitle"
                            defaultMessage="Smart wallets can’t make transactions on {network}. Change to a supported token."
                            values={{
                                network: errors.network.name,
                            }}
                        />
                    }
                />
            )
        case 'to_adress_not_supported_network':
            return (
                <BannerSolid
                    rounded
                    variant="warning"
                    title={
                        <FormattedMessage
                            id="send.banner.toAddressNotSupportedNetwork.title"
                            defaultMessage="Network not supported for recipient"
                        />
                    }
                    subtitle={
                        <FormattedMessage
                            id="send.banner.toAddressNotSupportedNetwork.subtitle"
                            defaultMessage="The recipient’s wallet does not support {network}. Change to a supported token."
                            values={{
                                network: errors.network.name,
                            }}
                        />
                    }
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(errors)
    }
}
