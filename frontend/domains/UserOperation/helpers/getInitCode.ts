import { notReachable } from '@zeal/toolkit'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'

import { SafeInstance } from '@zeal/domains/KeyStore/helpers/fetchSafe4337Instance'

export const getInitCode = (
    safeInstance: SafeInstance
): Hexadecimal.Hexadecimal | null => {
    switch (safeInstance.type) {
        case 'not_deployed':
            return safeInstance.deploymentInitCode
        case 'deployed':
            return null
        default:
            return notReachable(safeInstance)
    }
}
