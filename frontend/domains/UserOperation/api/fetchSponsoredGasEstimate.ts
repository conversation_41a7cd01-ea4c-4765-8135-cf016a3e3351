import { Safe4337 } from '@zeal/domains/KeyStore'
import { SafeInstance } from '@zeal/domains/KeyStore/helpers/fetchSafe4337Instance'
import { ActionSource } from '@zeal/domains/Main'
import { Network } from '@zeal/domains/Network'
import { MetaTransactionData } from '@zeal/domains/UserOperation'
import { fetchPimlicoGasEstimates } from '@zeal/domains/UserOperation/api/fetchGasEstimates'
import { PIMLICO_BOOSTED_USER_OP_PAYMASTER_AND_DATA } from '@zeal/domains/UserOperation/constants'
import { getCallData } from '@zeal/domains/UserOperation/helpers/getCallData'
import { getDummySignature } from '@zeal/domains/UserOperation/helpers/getDummySignature'
import { getInitCode } from '@zeal/domains/UserOperation/helpers/getInitCode'
import { getSignatureVGLBuffer } from '@zeal/domains/UserOperation/helpers/getSignatureVGLBuffer'

type GasEstimationParams = {
    safeInstance: SafeInstance
    keyStore: Safe4337
    entrypointNonce: bigint
    transactions: MetaTransactionData[]
    network: Network
    callGasLimitBuffer: bigint
    actionSource: ActionSource
    signal?: AbortSignal
}

export const fetchSponsoredGasEstimate = async ({
    safeInstance,
    keyStore,
    entrypointNonce,
    transactions,
    network,
    callGasLimitBuffer,
    actionSource,
    signal,
}: GasEstimationParams) =>
    fetchPimlicoGasEstimates({
        entrypoint: safeInstance.entrypoint,
        network,
        initialUserOperation: {
            callData: getCallData({
                safeInstance,
                keyStore,
                transactions,
            }),
            sender: keyStore.address,
            nonce: entrypointNonce,
            initCode: getInitCode(safeInstance),
            paymasterAndData: PIMLICO_BOOSTED_USER_OP_PAYMASTER_AND_DATA,
            signature: getDummySignature({
                safeInstance,
                keyStore,
            }),
        },
        verificationGasLimitBuffer: getSignatureVGLBuffer({
            safeInstance,
            keystore: keyStore,
        }),
        callGasLimitBuffer,
        actionSource,
        signal,
    })
