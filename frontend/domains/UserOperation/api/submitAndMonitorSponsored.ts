import { Safe4337 } from '@zeal/domains/KeyStore'
import { fetchSafeInstanceAndNonce } from '@zeal/domains/KeyStore/helpers/fetchSafe4337Instance'
import { ActionSource } from '@zeal/domains/Main'
import { Network, NetworkRPCMap } from '@zeal/domains/Network'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'
import {
    SubmittedUserOperationCompleted,
    SubmittedUserOperationFailed,
    SubmittedUserOperationRejected,
} from '@zeal/domains/TransactionRequest/domains/SubmittedUserOperation'
import { sign as signUserOperation } from '@zeal/domains/UserOperation/api/sign'
import { getInitCode } from '@zeal/domains/UserOperation/helpers/getInitCode'

import { fetchPimlicoGasEstimates } from './fetchGasEstimates'
import { monitorSubmittedUserOperation } from './monitorSubmittedUserOperation'
import { submit } from './submit'

import { PIMLICO_BOOSTED_USER_OP_PAYMASTER_AND_DATA } from '../constants'
import { ethSendTransactionToMetaTransactionData } from '../helpers/ethSendTransactionToMetaTransactionData'
import { getCallData } from '../helpers/getCallData'
import { getDummySignature } from '../helpers/getDummySignature'
import { getSignatureVGLBuffer } from '../helpers/getSignatureVGLBuffer'

type Params = {
    rpcRequestsToBundle: EthSendTransaction[]
    network: Network
    keyStore: Safe4337
    networkRPCMap: NetworkRPCMap
    sessionPassword: string
    installationId: string
    actionSource: ActionSource
    signal?: AbortSignal
}

export const submitAndMonitorSponsored = async ({
    installationId,
    keyStore,
    network,
    networkRPCMap,
    rpcRequestsToBundle,
    sessionPassword,
    actionSource,
    signal,
}: Params): Promise<
    | SubmittedUserOperationCompleted
    | SubmittedUserOperationRejected
    | SubmittedUserOperationFailed
> => {
    const { entrypointNonce, safeInstance } = await fetchSafeInstanceAndNonce({
        keyStore,
        network,
        networkRPCMap,
        signal,
    })

    const metaTransactionDatas = rpcRequestsToBundle.map(
        ethSendTransactionToMetaTransactionData
    )

    const sponsoredGasEstimate = await fetchPimlicoGasEstimates({
        entrypoint: safeInstance.entrypoint,
        network,
        initialUserOperation: {
            callData: getCallData({
                safeInstance,
                keyStore,
                transactions: metaTransactionDatas,
            }),
            sender: keyStore.address,
            nonce: entrypointNonce,
            initCode: getInitCode(safeInstance),
            paymasterAndData: PIMLICO_BOOSTED_USER_OP_PAYMASTER_AND_DATA,
            signature: getDummySignature({
                safeInstance,
                keyStore,
            }),
        },
        verificationGasLimitBuffer: getSignatureVGLBuffer({
            safeInstance,
            keystore: keyStore,
        }),
        callGasLimitBuffer: 0n,
        actionSource,
        signal,
    })

    const userOperationWithSignature = await signUserOperation({
        fee: {
            type: 'sponsored_user_operation_fee',
            gasEstimate: sponsoredGasEstimate,
        },
        keyStore,
        metaTransactionDatas,
        network,
        entrypointNonce,
        safeInstance,
        sessionPassword,
        signal,
    })

    const submittedUserOperation = await submit({
        userOperationWithSignature,
        network,
        networkRPCMap,
        actionSource,
    })

    return monitorSubmittedUserOperation({
        installationId,
        network,
        networkRPCMap,
        submittedUserOperation,
        signal,
    })
}
