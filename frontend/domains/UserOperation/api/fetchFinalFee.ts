import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { values } from '@zeal/toolkit/Object'

import { fetchCryptoCurrency2 } from '@zeal/domains/Currency/api/fetchCryptoCurrency2'
import { fetchRatesForDefaultCurrency } from '@zeal/domains/FXRate/api/fetchRate'
import { applyRate2 } from '@zeal/domains/FXRate/helpers/applyRate'
import { CryptoMoney, FiatMoney } from '@zeal/domains/Money'
import {
    NetworkMap,
    NetworkRPCMap,
    PredefinedNetwork,
    TestNetwork,
} from '@zeal/domains/Network'
import { ERC20TransferLog } from '@zeal/domains/RPCRequest'
import { fetchRPCBatch2WithRetry } from '@zeal/domains/RPCRequest/api/fetchRPCResponse'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { createGetTransactionReceiptRequest } from '@zeal/domains/TransactionRequest/domains/SubmitedTransaction/api/fetchTransactionReceiptWithRetry'
import {
    SubmittedUserOperationCompleted,
    SubmittedUserOperationFailed,
} from '@zeal/domains/TransactionRequest/domains/SubmittedUserOperation'

import { PAYMASTERS_FEE_RECEIVERS } from '../constants'
import { getUserOperationRelatedLogs } from '../helpers/getUserOperationRelatedLogs'

const PAYMASTERS_FEE_RECEIVERS_SET = new Set(values(PAYMASTERS_FEE_RECEIVERS))

type FeeResponse = {
    feeInTokenCurrency: CryptoMoney
    feeInDefaultCurrency: FiatMoney | null
}

// TODO @resetko-zeal this can be reunited with fetch transactionResult2 as it uses same onchain data
export const fetchFinalFee = async ({
    network,
    submittedUserOperation,
    networkRPCMap,
    networkMap,
    defaultCurrencyConfig,
    signal,
}: {
    submittedUserOperation:
        | SubmittedUserOperationCompleted
        | SubmittedUserOperationFailed
    network: PredefinedNetwork | TestNetwork
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    signal?: AbortSignal
}): Promise<FeeResponse> => {
    const { bundleTransactionHash, userOperationHash, sender } =
        submittedUserOperation

    const [bundleReceipt] = await fetchRPCBatch2WithRetry(
        [
            createGetTransactionReceiptRequest({
                network,
                transactionHash: bundleTransactionHash,
            }),
        ],
        {
            network,
            networkRPCMap,
            signal,
        }
    )

    switch (bundleReceipt.status) {
        case 'failed':
            throw new ImperativeError(`Can't get fee on failed bundle`, {
                bundleTransactionHash,
                userOperationHash,
            })
        case 'success': {
            const { userOpRelatedLogs, userOpLog } =
                getUserOperationRelatedLogs({
                    logs: bundleReceipt.logs,
                    userOperationHash,
                })

            const paymasterTransferEvent = userOpRelatedLogs.find(
                (log): log is ERC20TransferLog => {
                    switch (log.type) {
                        case 'erc20_transfer':
                            return (
                                log.from === sender &&
                                PAYMASTERS_FEE_RECEIVERS_SET.has(log.to)
                            )
                        case 'account_deployed':
                        case 'added_owner':
                        case 'approval':
                        case 'disable_module':
                        case 'enable_module':
                        case 'safe_module_transaction':
                        case 'safe_module_transaction_for_native_fee_payment':
                        case 'safe_received':
                        case 'set_allowance':
                        case 'threshold_updated':
                        case 'unknown':
                        case 'user_operation_event':
                        case 'user_operation_revert_reason':
                        case 'native_wrapper_deposit':
                        case 'native_wrapper_withdraw':
                            return false
                        /* istanbul ignore next */
                        default:
                            return notReachable(log)
                    }
                }
            )

            if (paymasterTransferEvent) {
                const currencyId = paymasterTransferEvent.currencyId

                const currency = (
                    await fetchCryptoCurrency2({
                        currencies: [currencyId],
                        networkRPCMap,
                        signal,
                    })
                )[currencyId]

                // TODO @resetko-zeal we can do that in parallel but we need to have invent function with accepts currencyId returns everything (currency, rate, 24hmove)

                const rate = (
                    await fetchRatesForDefaultCurrency({
                        cryptoCurrencies: [currency],
                        networkMap,
                        networkRPCMap,
                        defaultCurrencyConfig,
                        signal,
                    })
                )[currencyId]

                const feeInTokenCurrency: CryptoMoney = {
                    amount: paymasterTransferEvent.amount,
                    currency,
                }

                return {
                    feeInTokenCurrency,
                    feeInDefaultCurrency: rate
                        ? applyRate2({
                              baseAmount: feeInTokenCurrency,
                              rate,
                          })
                        : null,
                }
            }

            const { actualGasCost } = userOpLog

            const nativeRate = (
                await fetchRatesForDefaultCurrency({
                    cryptoCurrencies: [network.nativeCurrency],
                    networkMap,
                    networkRPCMap,
                    defaultCurrencyConfig,
                    signal,
                })
            )[network.nativeCurrency.id]

            const feeInTokenCurrency: CryptoMoney = {
                amount: actualGasCost,
                currency: network.nativeCurrency,
            }

            return {
                feeInTokenCurrency,
                feeInDefaultCurrency: nativeRate
                    ? applyRate2({
                          baseAmount: feeInTokenCurrency,
                          rate: nativeRate,
                      })
                    : null,
            }
        }
        /* istanbul ignore next */
        default:
            return notReachable(bundleReceipt)
    }
}
