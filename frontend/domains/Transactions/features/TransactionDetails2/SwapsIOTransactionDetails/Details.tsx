import { ReactNode } from 'react'
import { FormattedMessage } from 'react-intl'

import { Column } from '@zeal/uikit/Column'
import { Group } from '@zeal/uikit/Group'
import { ExternalLink } from '@zeal/uikit/Icon/ExternalLink'
import { IconButton } from '@zeal/uikit/IconButton'
import { Row } from '@zeal/uikit/Row'
import { Spacer } from '@zeal/uikit/Spacer'
import { Spinner } from '@zeal/uikit/Spinner'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { useDateFormat } from '@zeal/toolkit/Date/useDateFormat'
import { openExternalURL } from '@zeal/toolkit/Window'

import { NetworkMap } from '@zeal/domains/Network'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import {
    SwapsIOTransactionActivityCompleted,
    SwapsIOTransactionActivityPending,
} from '@zeal/domains/Transactions'
import { TransactionRate } from '@zeal/domains/Transactions/components/TransactionRate'
import { getExplorerLink } from '@zeal/domains/Transactions/domains/TransactionHash/helpers/getExplorerLink'

type Props = {
    transaction:
        | SwapsIOTransactionActivityCompleted
        | SwapsIOTransactionActivityPending
    networkMap: NetworkMap
}

export const DetailItem = ({
    label,
    value,
    explorerLink,
}: {
    label: ReactNode
    value: ReactNode
    explorerLink?: string
}) => (
    <Row spacing={2}>
        <Text variant="paragraph" weight="regular" color="textPrimary">
            {label}
        </Text>
        <Spacer />
        <Text variant="paragraph" weight="regular" color="textPrimary">
            {value}
        </Text>
        {explorerLink && (
            <IconButton
                variant="on_light"
                size="small"
                onClick={() => openExternalURL(explorerLink)}
            >
                {({ color }) => <ExternalLink size={16} color={color} />}
            </IconButton>
        )}
    </Row>
)

export const Details = ({ transaction, networkMap }: Props) => {
    const { formatDate } = useDateFormat()
    const fromNetwork = findNetworkByHexChainId(
        transaction.swapsIOSwapRequest.from.currency.networkHexChainId,
        networkMap
    )
    const toNetwork = findNetworkByHexChainId(
        transaction.swapsIOSwapRequest.to.currency.networkHexChainId,
        networkMap
    )
    return (
        <>
            <Group variant="tile">
                <Column spacing={12}>
                    {(() => {
                        switch (transaction.state) {
                            case 'completed':
                                return (
                                    <>
                                        {transaction.swapsIOSwapRequest
                                            .fromTransaction && (
                                            <DetailItem
                                                label={
                                                    <FormattedMessage
                                                        id="swaps-io-details.transaction.state.started-transaction"
                                                        defaultMessage="Started Transaction"
                                                    />
                                                }
                                                value={formatDate({
                                                    timestampMs:
                                                        transaction.swapsIOSwapRequest.fromTransaction.createdAt.getTime(),
                                                    variant:
                                                        'day_month_with_time',
                                                })}
                                                explorerLink={
                                                    getExplorerLink(
                                                        {
                                                            transactionHash:
                                                                transaction
                                                                    .swapsIOSwapRequest
                                                                    .fromTransaction
                                                                    .hash,
                                                        },
                                                        fromNetwork
                                                    ) || undefined
                                                }
                                            />
                                        )}
                                        <DetailItem
                                            label={
                                                <FormattedMessage
                                                    id="swaps-io-details.transaction.state.completed-transaction"
                                                    defaultMessage="Completed Transaction"
                                                />
                                            }
                                            value={formatDate({
                                                timestampMs:
                                                    transaction.swapsIOSwapRequest.toTransactionCreatedAt.getTime(),
                                                variant: 'day_month_with_time',
                                            })}
                                            explorerLink={
                                                getExplorerLink(
                                                    {
                                                        transactionHash:
                                                            transaction
                                                                .swapsIOSwapRequest
                                                                .toTransactionHash,
                                                    },
                                                    toNetwork
                                                ) || undefined
                                            }
                                        />
                                        <DetailItem
                                            label={
                                                <FormattedMessage
                                                    id="swaps-io-details.transaction.networkFees"
                                                    defaultMessage="Network fees"
                                                />
                                            }
                                            value="N/A" // FIXME :: @negriienko - add network fees
                                        />
                                        <DetailItem
                                            label={
                                                <FormattedMessage
                                                    id="swaps-io-details.bank.serviceProvider"
                                                    defaultMessage="Service provider"
                                                />
                                            }
                                            value="Swaps.io"
                                        />
                                        <DetailItem
                                            label={
                                                <FormattedMessage
                                                    id="swaps-io-details.rate"
                                                    defaultMessage="Rate"
                                                />
                                            }
                                            value={
                                                <TransactionRate
                                                    transaction={transaction}
                                                />
                                            }
                                        />
                                    </>
                                )
                            case 'pending':
                                switch (transaction.swapsIOSwapRequest.state) {
                                    case 'cancelled_awaiting_slash':
                                        return <></> // FIXME :: @negriienko - add cancelled awaiting slash transaction details
                                    case 'awaiting_liq_send':
                                    case 'awaiting_send':
                                        return (
                                            <>
                                                <DetailItem
                                                    label={
                                                        <FormattedMessage
                                                            id="swaps-io-details.transaction.state.started-transaction"
                                                            defaultMessage="Started Transaction"
                                                        />
                                                    }
                                                    value={formatDate({
                                                        timestampMs:
                                                            transaction.swapsIOSwapRequest.fromTransactionCreatedAt.getTime(),
                                                        variant:
                                                            'day_month_with_time',
                                                    })}
                                                    explorerLink={
                                                        getExplorerLink(
                                                            {
                                                                transactionHash:
                                                                    transaction
                                                                        .swapsIOSwapRequest
                                                                        .fromTransactionHash,
                                                            },
                                                            fromNetwork
                                                        ) || undefined
                                                    }
                                                />
                                                <DetailItem
                                                    label={
                                                        <FormattedMessage
                                                            id="swaps-io-details.transaction.state.completed-transaction"
                                                            defaultMessage="Completed Transaction"
                                                        />
                                                    }
                                                    value={
                                                        <>
                                                            <Row spacing={4}>
                                                                <Spinner
                                                                    variant="regular"
                                                                    size={16}
                                                                    color="blue30"
                                                                />
                                                                <Text
                                                                    variant="paragraph"
                                                                    weight="regular"
                                                                    color="blue30"
                                                                >
                                                                    <FormattedMessage
                                                                        id="swaps-io-details.details.processing"
                                                                        defaultMessage="Processing"
                                                                    />
                                                                </Text>
                                                            </Row>
                                                        </>
                                                    }
                                                    explorerLink={undefined}
                                                />
                                                <DetailItem
                                                    label={
                                                        <FormattedMessage
                                                            id="swaps-io-details.transaction.networkFees"
                                                            defaultMessage="Network fees"
                                                        />
                                                    }
                                                    value="N/A" // FIXME :: @negriienko - add network fees
                                                />
                                                <DetailItem
                                                    label={
                                                        <FormattedMessage
                                                            id="swaps-io-details.serviceProvider"
                                                            defaultMessage="Service provider"
                                                        />
                                                    }
                                                    value="Swaps.io"
                                                />
                                                <DetailItem
                                                    label={
                                                        <FormattedMessage
                                                            id="swaps-io-details.rate"
                                                            defaultMessage="Rate"
                                                        />
                                                    }
                                                    value={
                                                        <>
                                                            <Row spacing={4}>
                                                                <Spinner
                                                                    variant="regular"
                                                                    size={16}
                                                                    color="blue30"
                                                                />
                                                                <Text
                                                                    variant="paragraph"
                                                                    weight="regular"
                                                                    color="blue30"
                                                                >
                                                                    <FormattedMessage
                                                                        id="swaps-io-details.details.processing"
                                                                        defaultMessage="Processing"
                                                                    />
                                                                </Text>
                                                            </Row>
                                                        </>
                                                    }
                                                />
                                            </>
                                        )
                                    case 'awaiting_receive':
                                        return (
                                            <>
                                                <DetailItem
                                                    label={
                                                        <FormattedMessage
                                                            id="swaps-io-details.transaction.from.processing"
                                                            defaultMessage="Started Transaction"
                                                        />
                                                    }
                                                    value={
                                                        <>
                                                            <Row spacing={4}>
                                                                <Spinner
                                                                    variant="regular"
                                                                    size={16}
                                                                    color="blue30"
                                                                />
                                                                <Text
                                                                    variant="paragraph"
                                                                    weight="regular"
                                                                    color="blue30"
                                                                >
                                                                    <FormattedMessage
                                                                        id="swaps-io-details.details.processing"
                                                                        defaultMessage="Processing"
                                                                    />
                                                                </Text>
                                                            </Row>
                                                        </>
                                                    }
                                                    explorerLink={undefined}
                                                />
                                                <DetailItem
                                                    label={
                                                        <FormattedMessage
                                                            id="swaps-io-details.transaction.to.processing"
                                                            defaultMessage="Completed Transaction"
                                                        />
                                                    }
                                                    value={
                                                        <FormattedMessage
                                                            id="swaps-io-details.pending"
                                                            defaultMessage="Pending"
                                                        />
                                                    }
                                                    explorerLink={undefined}
                                                />
                                                <DetailItem
                                                    label={
                                                        <FormattedMessage
                                                            id="swaps-io-details.transaction.networkFees"
                                                            defaultMessage="Network fees"
                                                        />
                                                    }
                                                    value="N/A" // TODO :: @kat - add network fees
                                                />
                                                <DetailItem
                                                    label={
                                                        <FormattedMessage
                                                            id="swaps-io-details.bank.serviceProvider"
                                                            defaultMessage="Service provider"
                                                        />
                                                    }
                                                    value="Swaps.io"
                                                />
                                                <DetailItem
                                                    label={
                                                        <FormattedMessage
                                                            id="swaps-io-details.rate"
                                                            defaultMessage="Rate"
                                                        />
                                                    }
                                                    value={
                                                        <FormattedMessage
                                                            id="swaps-io-details.pending"
                                                            defaultMessage="Pending"
                                                        />
                                                    }
                                                />
                                            </>
                                        )
                                    case 'awaiting_signature':
                                        break // throw not supposed to happen
                                    default:
                                        return notReachable(
                                            transaction.swapsIOSwapRequest
                                        )
                                }
                                break
                            default:
                                return notReachable(transaction)
                        }
                    })()}
                </Column>
            </Group>
        </>
    )
}
