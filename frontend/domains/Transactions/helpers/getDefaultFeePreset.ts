import { isEOARecommendedGasEnabled } from '@zeal/domains/ABTest'
import { NetworkHexId } from '@zeal/domains/Network'
import {
    FeePresetMap,
    PredefinedPreset,
} from '@zeal/domains/Transactions/domains/FeeForecast'
import { DEFAULT_FEE_PRESET } from '@zeal/domains/Transactions/domains/FeeForecast/constants'

export const getDefaultFeePreset = ({
    networkHexId,
    feePresetMap,
}: {
    feePresetMap: FeePresetMap
    networkHexId: NetworkHexId
}): PredefinedPreset =>
    isEOARecommendedGasEnabled()
        ? { type: 'Fast' }
        : feePresetMap[networkHexId] || DEFAULT_FEE_PRESET
