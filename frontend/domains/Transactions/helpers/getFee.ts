import { notReachable } from '@zeal/toolkit'

import { CryptoCurrency } from '@zeal/domains/Currency'
import { CryptoMoney } from '@zeal/domains/Money'
import { GasInfo } from '@zeal/domains/TransactionRequest/domains/SubmitedTransaction'

export const getFee = ({
    currency,
    gasInfo,
}: {
    gasInfo: GasInfo
    currency: CryptoCurrency
}): CryptoMoney => {
    switch (gasInfo.type) {
        case 'generic':
            return {
                amount: gasInfo.gasUsed * gasInfo.effectiveGasPrice,
                currency,
            }

        case 'l2_rollup':
            return {
                amount: gasInfo.l1Fee + gasInfo.gasUsed * gasInfo.l2GasPrice,
                currency,
            }

        /* istanbul ignore next */
        default:
            return notReachable(gasInfo)
    }
}
