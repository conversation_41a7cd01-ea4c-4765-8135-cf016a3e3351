import { notReachable } from '@zeal/toolkit'
import { excludeNullValues } from '@zeal/toolkit/Array/helpers/excludeNullValues'
import { ImperativeError } from '@zeal/toolkit/Error'
import { Hexadecimal, toBigInt } from '@zeal/toolkit/Hexadecimal'
import { NonEmptyArray } from '@zeal/toolkit/NonEmptyArray'
import {
    failure,
    groupByType,
    match,
    nonEmptyArray,
    oneOf,
    Result,
    shape,
    success,
} from '@zeal/toolkit/Result'
import * as address from '@zeal/toolkit/Web3/address'

import {
    CardConfig,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import {
    CASHBACK_CURRENCY,
    GNOSIS_PAY_CASHBACK_DISTRIBUTION_ADDRESS,
    GNOSIS_PAY_SPENDER,
} from '@zeal/domains/Card/domains/Cashback/constants'
import { REWARDS_CONTRACT_ADDRESS } from '@zeal/domains/Card/domains/Reward/constants'
import { KnownCryptoCurrencies } from '@zeal/domains/Currency'
import {
    MONERIUM_GNOSIS_V1_TO_V2_MAP,
    MONERIUM_V1_TOKENS,
    MONERIUM_V2_TOKENS,
} from '@zeal/domains/Currency/constants'
import { SOCKET_GATEWAY_CONTRACTS } from '@zeal/domains/Currency/domains/Bridge/api/fetchBridgeRoutes'
import { convertStableCoinCurrencyToFiatCurrency } from '@zeal/domains/Currency/helpers/convertStableCoinCurrencyToFiatCurrency'
import { getCryptoCurrency } from '@zeal/domains/Currency/helpers/getCryptoCurrency'
import {
    DeployedTaker,
    Earn,
    HistoricalTakerUserCurrencyRateMap,
} from '@zeal/domains/Earn'
import { EARN_NETWORK } from '@zeal/domains/Earn/constants'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { applyRate2 } from '@zeal/domains/FXRate/helpers/applyRate'
import { CryptoMoney } from '@zeal/domains/Money'
import { PredefinedNetwork } from '@zeal/domains/Network'
import { GNOSIS } from '@zeal/domains/Network/constants'
import {
    ApprovalLog,
    ERC20TransferLog,
    SafeModuleTransactionLog,
    SafeReceivedLog,
    SetAllowanceLog,
    ThresholdUpdatedLog,
    UserOperationEventLog,
} from '@zeal/domains/RPCRequest'

import {
    ArbitrarySmartContractTransactionActivity,
    ArbitrarySmartContractTransactionType,
    BRewardClaimTransactionActivity,
    CardOwnersUpdatedTransactionActivity,
    CardSpendLimitUpdatedTransactionActivity,
    CashBackDepositTransaction,
    CashBackRewardTransaction,
    CashBackWithdrawTransaction,
    DeployedSmartWalletGnosisTransactionActivity,
    DepositFromBankTransactionActivity,
    DepositIntoCardTransactionActivity,
    DepositIntoEarnTransactionActivity,
    FailedTransactionActivity,
    IncomingBridgeTransactionActivity,
    IndexedTransaction,
    OutgoingBridgeTransactionActivity,
    ReceiveTransactionActivity,
    RechargeDisabledTransactionActivity,
    RechargeTargetSetTransactionActivity,
    RecoveredSmartWalletGnosisTransactionActivity,
    RegularTransactionActivity,
    SendToBankTransactionActivity,
    SendTransactionActivity,
    SwapTransactionActivity,
    TokenApprovalRevokedTransactionActivity,
    TokenApprovalTransactionActivity,
    WithdrawFromCardTransactionActivity,
    WithdrawFromEarnTransactionActivity,
} from '..'

const UNLIMITED_APPROVAL_AMOUNT =
    115792089237316190183760311394656140241344763643041023198016376741560320n

const NULL_ADDRESS = address.staticFromString(
    '******************************************'
)

const COW_SWAP_SETTLEMENT_PROTOCOL = address.staticFromString(
    '******************************************'
)

export const parseIndexedActivityTransaction = ({
    input,
    accountAddress,
    knownCurrencies,
    cardConfig,
    earn,
    network,
    historicalTakerUserCurrencyRateMap,
}: {
    input: IndexedTransaction
    accountAddress: address.Address
    knownCurrencies: KnownCryptoCurrencies
    cardConfig: CardConfig
    earn: Earn
    network: PredefinedNetwork
    historicalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
}): Result<unknown, RegularTransactionActivity> => {
    return oneOf(input, [
        parseFailedTransactionActivity(input),
        parseCardOwnerUpdatedTransactionActivity(input, cardConfig),
        parseCardSpendLimitUpdatedTransactionActivity(input, cardConfig),
        parseCashbackDepositTransactionActivity(input, cardConfig),
        parseCashbackWithdrawTransactionActivity(input, cardConfig),
        parseCashbackRewardTransactionActivity(input, cardConfig),
        parseSmartWalletGnosisTransactionActivity(input),
        parseRechargeTargetSetTransactionActivity(input, earn, cardConfig),
        parseTokenApprovelTransactionActivity({
            input,
            accountAddress,
            knownCurrencies,
        }),
        parseIndexedTransferTransaction({
            input,
            knownCurrencies,
            network,
        }).andThen((transfer) =>
            oneOf(transfer, [
                parseDepositIntoCardTransactionActivity({
                    input: transfer,
                    cardConfig,
                    accountAddress,
                    earn,
                    historicalTakerUserCurrencyRateMap,
                }),
                parseWithdrawFromCardTransactionActivity({
                    input: transfer,
                    cardConfig,
                    accountAddress,
                }),
                parseDepositIntoEarnTransactionActivity({
                    input: transfer,
                    earn,
                    historicalTakerUserCurrencyRateMap,
                    accountAddress,
                }),
                parseBRewardClaimTransactionActivity({
                    earn,
                    historicalTakerUserCurrencyRateMap,
                    input: transfer,
                }),
                parseWithdrawFromEarnTransactionActivity({
                    input: transfer,
                    earn,
                    historicalTakerUserCurrencyRateMap,
                    accountAddress,
                    cardConfig,
                }),
                parseSwapTransactionActivity({
                    input: transfer,
                    accountAddress,
                }),
                parseSendTransactionActivity({
                    input: transfer,
                    accountAddress,
                }).andThen((send) =>
                    oneOf(send, [
                        parseSendToBankTransactionActivity(send),
                        parseBridgeTransactionOutgoingActivity(send),
                        success(send),
                    ])
                ),
                parseReceiveTransactionActivity({
                    input: transfer,
                    accountAddress,
                }).andThen((receive) =>
                    oneOf(receive, [
                        parseDepositFromBankTransactionActivity(receive),
                        parseBridgeTransactionInComingActivity(receive),
                        success(receive),
                    ])
                ),
                parseArbitrarySmartContractTransactionActivity({
                    input,
                    inputTransfers: transfer,
                    accountAddress,
                }),
            ])
        ),
    ])
}

const parseFailedTransactionActivity = (
    input: IndexedTransaction
): Result<unknown, FailedTransactionActivity> => {
    const userOperationEventLog = input.logs.find(
        (log): log is UserOperationEventLog => {
            switch (log.type) {
                case 'unknown':
                case 'approval':
                case 'erc20_transfer':
                case 'threshold_updated':
                case 'set_allowance':
                case 'account_deployed':
                case 'safe_module_transaction':
                case 'safe_module_transaction_for_native_fee_payment':
                case 'safe_received':
                case 'added_owner':
                case 'enable_module':
                case 'disable_module':
                case 'user_operation_revert_reason':
                case 'native_wrapper_deposit':
                case 'native_wrapper_withdraw':
                    return false
                case 'user_operation_event':
                    return true
                /* istanbul ignore next */
                default:
                    return notReachable(log)
            }
        }
    )
    if (userOperationEventLog && !userOperationEventLog.success) {
        return success({
            type: 'failed_transaction',
            hash: input.hash,
            functionSignature: null,
            smartContract: null,
            networkHexId: EARN_NETWORK.hexChainId,
            timestamp: new Date(input.timestamp),
            paidFee: null,
        })
    }
    return failure({ type: 'not_failed_transaction' })
}

export const parseIndexedScamActivityTransaction = ({
    input,
    accountAddress,
    knownCurrencies,
    network,
}: {
    input: IndexedTransaction
    accountAddress: address.Address
    knownCurrencies: KnownCryptoCurrencies
    network: PredefinedNetwork
}): Result<unknown, RegularTransactionActivity> => {
    return parseIndexedTransferTransaction({
        input,
        knownCurrencies,
        network,
    }).andThen((transfer) =>
        oneOf(transfer, [
            parseSwapTransactionActivity({
                input: transfer,
                accountAddress,
            }),
            parseSendTransactionActivity({
                input: transfer,
                accountAddress,
            }),
            parseReceiveTransactionActivity({
                input: transfer,
                accountAddress,
            }),
        ])
    )
}

const parseSendTransactionActivity = ({
    input,
    accountAddress,
}: {
    input: IndexedTransferTransaction
    accountAddress: address.Address
}): Result<unknown, SendTransactionActivity> => {
    const outgoingTransfers = input.transfers.filter((transfer) => {
        return transfer.from === accountAddress
    })

    if (outgoingTransfers.length !== 1 || input.transfers.length > 1) {
        return failure({
            type: 'not_send_transaction',
        })
    }

    const outgoingTransfer = outgoingTransfers[0]
    return success({
        type: 'send',
        hash: input.hash,
        receiver: outgoingTransfer.to,
        amount: outgoingTransfer.money,
        networkHexId: outgoingTransfer.money.currency.networkHexChainId,
        timestamp: new Date(input.timestamp),
        paidFee: null,
        amountInDefaultCurrency: null,
    })
}

const parseReceiveTransactionActivity = ({
    input,
    accountAddress,
}: {
    input: IndexedTransferTransaction
    accountAddress: address.Address
}): Result<unknown, ReceiveTransactionActivity> => {
    const ingoingTransfers = input.transfers.filter((transfer) => {
        return transfer.to === accountAddress
    })
    if (ingoingTransfers.length !== 1 || input.transfers.length > 1) {
        return failure({
            type: 'not_receive_transaction',
        })
    }
    const ingoingTransfer = ingoingTransfers[0]

    return success({
        type: 'receive',
        hash: input.hash,
        sender: ingoingTransfer.from,
        amount: ingoingTransfer.money,
        networkHexId: ingoingTransfer.money.currency.networkHexChainId,
        timestamp: new Date(input.timestamp),
        paidFee: null,
        amountInDefaultCurrency: null,
    })
}

const parseDepositFromBankTransactionActivity = (
    input: ReceiveTransactionActivity
): Result<unknown, DepositFromBankTransactionActivity> => {
    if (
        MONERIUM_V2_TOKENS.has(input.amount.currency.id) &&
        input.sender === NULL_ADDRESS
    ) {
        return success({
            type: 'deposit_from_bank',
            hash: input.hash,
            sender: input.sender,
            fromAmount: {
                amount: input.amount.amount,
                currency: convertStableCoinCurrencyToFiatCurrency({
                    cryptoCurrency: input.amount.currency,
                }),
            },
            toAmount: input.amount,
            networkHexId: input.networkHexId,
            timestamp: new Date(input.timestamp),
            paidFee: null,
            amountInDefaultCurrency: null,
        })
    }

    return failure({
        type: 'not_deposit_from_bank_transaction',
    })
}

const parseSendToBankTransactionActivity = (
    input: SendTransactionActivity
): Result<unknown, SendToBankTransactionActivity> => {
    if (
        MONERIUM_V2_TOKENS.has(input.amount.currency.id) &&
        input.receiver === NULL_ADDRESS
    ) {
        return success({
            type: 'send_to_bank',
            hash: input.hash,
            receiver: input.receiver,
            fromAmount: input.amount,
            toAmount: {
                amount: input.amount.amount,
                currency: convertStableCoinCurrencyToFiatCurrency({
                    cryptoCurrency: input.amount.currency,
                }),
            },
            networkHexId: input.networkHexId,
            timestamp: new Date(input.timestamp),
            paidFee: null,
            priceInDefaultCurrency: null,
        })
    }

    return failure({
        type: 'not_send_to_bank_transaction',
    })
}

const parseCardTransactions = ({
    transfers,
    cardConfig,
}: {
    transfers: IndexedTransfer[]
    cardConfig: CardConfig
}): Result<
    unknown,
    {
        transfer: IndexedTransfer
        config: ReadonlySignerSelectedOnboardedCardConfig
    }
> => {
    switch (cardConfig.type) {
        case 'card_readonly_signer_address_is_not_selected':
        case 'card_readonly_signer_address_is_selected':
            return failure({
                type: 'not_card_transaction',
            })
        case 'card_readonly_signer_address_is_selected_fully_onboarded': {
            const cardTransfer = transfers.find(
                (transfer) =>
                    (transfer.to === cardConfig.lastSeenSafeAddress ||
                        transfer.from === cardConfig.lastSeenSafeAddress) &&
                    MONERIUM_V2_TOKENS.has(transfer.money.currency.id)
            )

            if (!cardTransfer) {
                return failure({
                    type: 'not_card_transaction',
                })
            }

            return success({ transfer: cardTransfer, config: cardConfig })
        }
        /* istanbul ignore next */
        default:
            return notReachable(cardConfig)
    }
}

const parseDeployedTakers = ({
    earn,
}: {
    earn: Earn
}): Result<unknown, NonEmptyArray<DeployedTaker>> => {
    switch (earn.type) {
        case 'not_configured':
            return failure({
                type: 'earn_not_configured',
            })
        case 'configured':
            return nonEmptyArray(
                earn.takers.filter((taker): taker is DeployedTaker => {
                    switch (taker.state) {
                        case 'deployed':
                            return true
                        case 'not_deployed':
                            return false
                        /* istanbul ignore next */
                        default:
                            return notReachable(taker)
                    }
                })
            )
        /* istanbul ignore next */
        default:
            return notReachable(earn)
    }
}

// TODO @resetko-zeal change to parserFromTaker
const parseTakerTransfer = ({
    transfers,
    earn,
}: {
    transfers: IndexedTransfer[]
    earn: Earn
}): Result<unknown, { transfer: IndexedTransfer; taker: DeployedTaker }> => {
    switch (earn.type) {
        case 'not_configured':
            return failure({
                type: 'earn_not_configured',
            })
        case 'configured': {
            for (const transfer of transfers) {
                const deployedTaker = earn.takers.find(
                    (taker): taker is DeployedTaker => {
                        switch (taker.state) {
                            case 'deployed':
                                return (
                                    // aGnoEure is not rebalancing token,
                                    // hence we want to exclude earn yeild comming from NULL_ADDRESS
                                    transfer.from !== NULL_ADDRESS &&
                                    (taker.address === transfer.from ||
                                        taker.address === transfer.to)
                                )
                            case 'not_deployed':
                                return false
                            /* istanbul ignore next */
                            default:
                                return notReachable(taker)
                        }
                    }
                )
                if (deployedTaker) {
                    return success({
                        transfer,
                        taker: deployedTaker,
                    })
                }
            }
            return failure({
                type: 'taker_not_found',
            })
        }
        /* istanbul ignore next */
        default:
            return notReachable(earn)
    }
}

const parseDepositIntoCardTransactionActivity = ({
    input,
    cardConfig,
    accountAddress,
    earn,
    historicalTakerUserCurrencyRateMap,
}: {
    input: IndexedTransferTransaction
    cardConfig: CardConfig
    accountAddress: address.Address
    earn: Earn
    historicalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
}): Result<unknown, DepositIntoCardTransactionActivity> => {
    return shape({
        card: parseCardTransactions({
            transfers: input.transfers,
            cardConfig,
        }),
        fromTakerTransfer: oneOf({ transfers: input.transfers, earn: earn }, [
            parseTakerTransfer({ transfers: input.transfers, earn }),
            success(null),
        ]),
        fromAccountSwapTransfer: oneOf(input.transfers, [
            success(input.transfers.find((t) => t.from === accountAddress)),
            success(null),
        ]),
        fromBankTransfer: oneOf(input.transfers, [
            success(
                input.transfers.find(
                    (t) =>
                        t.from === NULL_ADDRESS &&
                        MONERIUM_V2_TOKENS.has(t.money.currency.id)
                )
            ),
            success(null),
        ]),
    }).andThen(
        ({
            card,
            fromTakerTransfer,
            fromAccountSwapTransfer,
            fromBankTransfer,
        }): Result<{ type: string }, DepositIntoCardTransactionActivity> => {
            if (!card || card.transfer.to !== card.config.lastSeenSafeAddress) {
                return failure({ type: 'not_deposit_into_card_transaction' })
            }
            if (
                card.transfer.from === COW_SWAP_SETTLEMENT_PROTOCOL &&
                fromTakerTransfer
            ) {
                return failure({ type: 'earn_recharge' })
            }
            const { transfer, config } = card
            if (fromTakerTransfer) {
                return success({
                    type: 'deposit_into_card',
                    from: 'earn',
                    taker: fromTakerTransfer.taker,
                    toAmount: card.transfer.money,
                    fromAmountInUserCurrency: applyRate2({
                        baseAmount: fromTakerTransfer.transfer.money,
                        rate: historicalTakerUserCurrencyRateMap[
                            fromTakerTransfer.taker.type
                        ][Number(input.blockNumber)],
                    }),
                    hash: input.hash,
                    paidFee: null,
                    networkHexId: config.currency.networkHexChainId,
                    timestamp: new Date(input.timestamp),
                } as const)
            }
            if (fromBankTransfer) {
                return success({
                    type: 'deposit_into_card',
                    from: 'bank',
                    fromAmount: {
                        amount: fromBankTransfer.money.amount,
                        currency: convertStableCoinCurrencyToFiatCurrency({
                            cryptoCurrency: card.config.currency,
                        }),
                    },
                    toAmount: transfer.money,
                    hash: input.hash,
                    paidFee: null,
                    networkHexId: config.currency.networkHexChainId,
                    timestamp: new Date(input.timestamp),
                } as const)
            }
            return success({
                type: 'deposit_into_card',
                from: 'wallet',
                fromAmount: fromAccountSwapTransfer?.money ?? transfer.money,
                toAmount: transfer.money,
                hash: input.hash,
                paidFee: null,
                networkHexId: config.currency.networkHexChainId,
                timestamp: new Date(input.timestamp),
            } as const)
        }
    )
}

const parseWithdrawFromCardTransactionActivity = ({
    input,
    accountAddress,
    cardConfig,
}: {
    input: IndexedTransferTransaction
    accountAddress: address.Address
    cardConfig: CardConfig
}): Result<unknown, WithdrawFromCardTransactionActivity> => {
    return parseCardTransactions({
        transfers: input.transfers,
        cardConfig,
    }).andThen(({ transfer, config }) => {
        if (
            transfer.from !== config.lastSeenSafeAddress ||
            transfer.to !== accountAddress
        ) {
            return failure({
                type: 'not_withdraw_from_card_transaction',
            })
        }

        return success({
            type: 'withdraw_from_card',
            token: transfer.money,
            hash: input.hash,
            paidFee: null,
            networkHexId: config.currency.networkHexChainId,
            timestamp: new Date(input.timestamp),
        })
    })
}

const parseCardOwnerUpdatedTransactionActivity = (
    input: IndexedTransaction,
    cardConfig: CardConfig
): Result<unknown, CardOwnersUpdatedTransactionActivity> => {
    const ownerUpdatedLogs = input.logs.find((log) => {
        switch (log.type) {
            case 'unknown':
            case 'approval':
            case 'erc20_transfer':
            case 'threshold_updated':
            case 'set_allowance':
            case 'account_deployed':
            case 'safe_module_transaction':
            case 'safe_received':
            case 'added_owner':
            case 'user_operation_event':
            case 'user_operation_revert_reason':
            case 'safe_module_transaction_for_native_fee_payment':
            case 'native_wrapper_deposit':
            case 'native_wrapper_withdraw':
                return false
            case 'enable_module':
            case 'disable_module':
                return true
            /* istanbul ignore next */
            default:
                return notReachable(log)
        }
    })

    if (!ownerUpdatedLogs)
        return failure({
            type: 'not_card_owners_updated_transaction',
        })

    switch (cardConfig.type) {
        case 'card_readonly_signer_address_is_not_selected':
            return failure({
                type: 'card_not_configured',
            })
        case 'card_readonly_signer_address_is_selected':
            return failure({
                type: 'card_not_onboarded',
            })
        case 'card_readonly_signer_address_is_selected_fully_onboarded':
            return success({
                type: 'card_owners_updated',
                hash: input.hash,
                networkHexId: cardConfig.currency.networkHexChainId,
                timestamp: new Date(input.timestamp),
                paidFee: null,
            })
        /* istanbul ignore next */
        default:
            return notReachable(cardConfig)
    }
}

const parseCardSpendLimitUpdatedTransactionActivity = (
    input: IndexedTransaction,
    cardConfig: CardConfig
): Result<unknown, CardSpendLimitUpdatedTransactionActivity> => {
    const setAllowanceLog = input.logs.find((log): log is SetAllowanceLog => {
        switch (log.type) {
            case 'unknown':
            case 'added_owner':
            case 'approval':
            case 'account_deployed':
            case 'threshold_updated':
            case 'enable_module':
            case 'disable_module':
            case 'erc20_transfer':
            case 'safe_module_transaction':
            case 'safe_received':
            case 'user_operation_event':
            case 'user_operation_revert_reason':
            case 'safe_module_transaction_for_native_fee_payment':
            case 'native_wrapper_deposit':
            case 'native_wrapper_withdraw':
                return false
            case 'set_allowance':
                return true
            /* istanbul ignore next */
            default:
                return notReachable(log)
        }
    })

    if (!setAllowanceLog) {
        return failure({
            type: 'not_card_spend_limit_updated_transaction',
        })
    }

    switch (cardConfig.type) {
        case 'card_readonly_signer_address_is_not_selected':
            return failure({
                type: 'card_not_configured',
            })
        case 'card_readonly_signer_address_is_selected':
            return failure({
                type: 'card_not_onboarded',
            })
        case 'card_readonly_signer_address_is_selected_fully_onboarded':
            return success({
                type: 'card_spend_limit_updated',
                spendLimit: {
                    amount: setAllowanceLog.balance,
                    currency: cardConfig.currency,
                },
                hash: input.hash,
                networkHexId: cardConfig.currency.networkHexChainId,
                timestamp: new Date(input.timestamp),
                paidFee: null,
            })
        /* istanbul ignore next */
        default:
            return notReachable(cardConfig)
    }
}

const parseCashbackDepositTransactionActivity = (
    input: IndexedTransaction,
    cardConfig: CardConfig
): Result<unknown, CashBackDepositTransaction> => {
    switch (cardConfig.type) {
        case 'card_readonly_signer_address_is_not_selected':
            return failure({ type: 'card_not_configured' })
        case 'card_readonly_signer_address_is_selected':
            return failure({ type: 'card_not_onboarded' })
        case 'card_readonly_signer_address_is_selected_fully_onboarded': {
            const transferLog = input.logs.find(
                (log): log is ERC20TransferLog => {
                    switch (log.type) {
                        case 'unknown':
                        case 'added_owner':
                        case 'approval':
                        case 'account_deployed':
                        case 'threshold_updated':
                        case 'set_allowance':
                        case 'enable_module':
                        case 'disable_module':
                        case 'safe_module_transaction':
                        case 'safe_received':
                        case 'user_operation_event':
                        case 'user_operation_revert_reason':
                        case 'safe_module_transaction_for_native_fee_payment':
                        case 'native_wrapper_deposit':
                        case 'native_wrapper_withdraw':
                            return false
                        case 'erc20_transfer':
                            return (
                                log.to === cardConfig.lastSeenSafeAddress &&
                                log.from !==
                                    GNOSIS_PAY_CASHBACK_DISTRIBUTION_ADDRESS &&
                                log.currencyId === CASHBACK_CURRENCY.id
                            )
                        /* istanbul ignore next */
                        default:
                            return notReachable(log)
                    }
                }
            )

            if (!transferLog) {
                return failure({ type: 'not_cashback_deposit_transaction' })
            }

            return success({
                type: 'cashback_deposit',
                fromAddress: transferLog.from,
                amount: {
                    amount: transferLog.amount,
                    currency: CASHBACK_CURRENCY,
                },
                hash: input.hash,
                networkHexId: CASHBACK_CURRENCY.networkHexChainId,
                timestamp: new Date(input.timestamp),
                paidFee: null,
                priceInDefaultCurrency: null,
            })
        }
        /* istanbul ignore next */
        default:
            return notReachable(cardConfig)
    }
}

const parseCashbackWithdrawTransactionActivity = (
    input: IndexedTransaction,
    cardConfig: CardConfig
): Result<unknown, CashBackWithdrawTransaction> => {
    switch (cardConfig.type) {
        case 'card_readonly_signer_address_is_not_selected':
            return failure({ type: 'card_not_configured' })
        case 'card_readonly_signer_address_is_selected':
            return failure({ type: 'card_not_onboarded' })
        case 'card_readonly_signer_address_is_selected_fully_onboarded': {
            const transferLog = input.logs.find(
                (log): log is ERC20TransferLog => {
                    switch (log.type) {
                        case 'unknown':
                        case 'added_owner':
                        case 'approval':
                        case 'account_deployed':
                        case 'threshold_updated':
                        case 'set_allowance':
                        case 'enable_module':
                        case 'disable_module':
                        case 'safe_module_transaction':
                        case 'safe_received':
                        case 'user_operation_event':
                        case 'user_operation_revert_reason':
                        case 'safe_module_transaction_for_native_fee_payment':
                        case 'native_wrapper_deposit':
                        case 'native_wrapper_withdraw':
                            return false
                        case 'erc20_transfer':
                            return (
                                log.to !== cardConfig.lastSeenSafeAddress &&
                                log.from === cardConfig.lastSeenSafeAddress &&
                                log.currencyId === CASHBACK_CURRENCY.id
                            )
                        /* istanbul ignore next */
                        default:
                            return notReachable(log)
                    }
                }
            )

            if (!transferLog) {
                return failure({ type: 'not_cashback_withdraw_transaction' })
            }

            return success({
                type: 'cashback_withdraw',
                toAddress: transferLog.to,
                amount: {
                    amount: transferLog.amount,
                    currency: CASHBACK_CURRENCY,
                },
                hash: input.hash,
                networkHexId: CASHBACK_CURRENCY.networkHexChainId,
                timestamp: new Date(input.timestamp),
                paidFee: null,
                priceInDefaultCurrency: null,
            })
        }
        /* istanbul ignore next */
        default:
            return notReachable(cardConfig)
    }
}

const parseCashbackRewardTransactionActivity = (
    input: IndexedTransaction,
    cardConfig: CardConfig
): Result<unknown, CashBackRewardTransaction> => {
    switch (cardConfig.type) {
        case 'card_readonly_signer_address_is_not_selected':
            return failure({ type: 'card_not_configured' })
        case 'card_readonly_signer_address_is_selected':
            return failure({ type: 'card_not_onboarded' })
        case 'card_readonly_signer_address_is_selected_fully_onboarded': {
            const transferLog = input.logs.find(
                (log): log is ERC20TransferLog => {
                    switch (log.type) {
                        case 'unknown':
                        case 'added_owner':
                        case 'approval':
                        case 'account_deployed':
                        case 'threshold_updated':
                        case 'set_allowance':
                        case 'enable_module':
                        case 'disable_module':
                        case 'safe_module_transaction':
                        case 'safe_received':
                        case 'user_operation_event':
                        case 'user_operation_revert_reason':
                        case 'safe_module_transaction_for_native_fee_payment':
                        case 'native_wrapper_deposit':
                        case 'native_wrapper_withdraw':
                            return false
                        case 'erc20_transfer':
                            return (
                                log.to === cardConfig.lastSeenSafeAddress &&
                                log.from ===
                                    GNOSIS_PAY_CASHBACK_DISTRIBUTION_ADDRESS &&
                                log.currencyId === CASHBACK_CURRENCY.id
                            )
                        /* istanbul ignore next */
                        default:
                            return notReachable(log)
                    }
                }
            )

            if (!transferLog) {
                return failure({ type: 'not_cashback_reward_transaction' })
            }

            return success({
                type: 'cashback_reward',
                amount: {
                    amount: transferLog.amount,
                    currency: CASHBACK_CURRENCY,
                },
                hash: input.hash,
                networkHexId: CASHBACK_CURRENCY.networkHexChainId,
                timestamp: new Date(input.timestamp),
                paidFee: null,
                priceInDefaultCurrency: null,
            })
        }
        /* istanbul ignore next */
        default:
            return notReachable(cardConfig)
    }
}

const parseSmartWalletGnosisTransactionActivity = (
    input: IndexedTransaction
): Result<
    unknown,
    | DeployedSmartWalletGnosisTransactionActivity
    | RecoveredSmartWalletGnosisTransactionActivity
> => {
    const accountDeployedLogs = input.logs.filter((log) => {
        switch (log.type) {
            case 'unknown':
            case 'approval':
            case 'erc20_transfer':
            case 'threshold_updated':
            case 'set_allowance':
            case 'enable_module':
            case 'disable_module':
            case 'safe_module_transaction':
            case 'safe_module_transaction_for_native_fee_payment':
            case 'safe_received':
            case 'user_operation_event':
            case 'user_operation_revert_reason':
            case 'added_owner':
            case 'native_wrapper_deposit':
            case 'native_wrapper_withdraw':
                return false
            case 'account_deployed':
                return true

            /* istanbul ignore next */
            default:
                return notReachable(log)
        }
    })

    const addedOwnerLogs = input.logs.filter((log) => {
        switch (log.type) {
            case 'unknown':
            case 'approval':
            case 'erc20_transfer':
            case 'threshold_updated':
            case 'set_allowance':
            case 'enable_module':
            case 'disable_module':
            case 'account_deployed':
            case 'safe_module_transaction':
            case 'safe_module_transaction_for_native_fee_payment':
            case 'safe_received':
            case 'user_operation_event':
            case 'user_operation_revert_reason':
            case 'native_wrapper_deposit':
            case 'native_wrapper_withdraw':
                return false
            case 'added_owner':
                return true
            /* istanbul ignore next */
            default:
                return notReachable(log)
        }
    })

    if (accountDeployedLogs.length === 1 && addedOwnerLogs.length === 1) {
        return success({
            type: 'deployed_smart_wallet_gnosis',
            hash: input.hash,
            networkHexId: EARN_NETWORK.hexChainId,
            timestamp: new Date(input.timestamp),
            paidFee: null,
        })
    }

    if (addedOwnerLogs.length === 1) {
        return success({
            type: 'recovered_smart_wallet_gnosis',
            hash: input.hash,
            networkHexId: EARN_NETWORK.hexChainId,
            timestamp: new Date(input.timestamp),
            paidFee: null,
        })
    }

    return failure({
        type: 'not_smart_wallet_gnosis_transaction',
    })
}

const parseRechargeTargetSetTransactionActivity = (
    input: IndexedTransaction,
    earn: Earn,
    cardConfig: CardConfig
): Result<
    unknown,
    RechargeTargetSetTransactionActivity | RechargeDisabledTransactionActivity
> => {
    const thresholdLog = input.logs.find((log): log is ThresholdUpdatedLog => {
        switch (log.type) {
            case 'unknown':
            case 'added_owner':
            case 'approval':
            case 'account_deployed':
            case 'set_allowance':
            case 'enable_module':
            case 'disable_module':
            case 'erc20_transfer':
            case 'safe_module_transaction':
            case 'safe_module_transaction_for_native_fee_payment':
            case 'safe_received':
            case 'user_operation_event':
            case 'user_operation_revert_reason':
            case 'native_wrapper_deposit':
            case 'native_wrapper_withdraw':
                return false
            case 'threshold_updated':
                return true
            /* istanbul ignore next */
            default:
                return notReachable(log)
        }
    })

    if (!thresholdLog) {
        return failure({
            type: 'no_threshold_log_found',
        })
    }
    switch (earn.type) {
        case 'not_configured':
            return failure({
                type: 'earn_not_configured',
            })

        case 'configured': {
            if (thresholdLog.threshold > 0n) {
                switch (cardConfig.type) {
                    case 'card_readonly_signer_address_is_not_selected':
                        return failure({
                            type: 'card_not_configured',
                        })
                    case 'card_readonly_signer_address_is_selected':
                        return failure({
                            type: 'card_not_onboarded',
                        })
                    case 'card_readonly_signer_address_is_selected_fully_onboarded':
                        return success({
                            type: 'recharge_target_set' as const,
                            target: {
                                amount: thresholdLog.threshold,
                                currency: cardConfig.currency,
                            },
                            hash: input.hash,
                            networkHexId: EARN_NETWORK.hexChainId,
                            timestamp: new Date(input.timestamp),
                            paidFee: null,
                        })
                    /* istanbul ignore next */
                    default:
                        return notReachable(cardConfig)
                }
            }

            return success({
                type: 'recharge_disabled' as const,
                hash: input.hash,
                networkHexId: EARN_NETWORK.hexChainId,
                timestamp: new Date(input.timestamp),
                paidFee: null,
            })
        }

        /* istanbul ignore next */
        default:
            return notReachable(earn)
    }
}

const parseTokenApprovelTransactionActivity = ({
    input,
    accountAddress,
    knownCurrencies,
}: {
    input: IndexedTransaction
    accountAddress: address.Address
    knownCurrencies: KnownCryptoCurrencies
}): Result<
    unknown,
    TokenApprovalTransactionActivity | TokenApprovalRevokedTransactionActivity
> => {
    const approvalLogs = input.logs.filter((log): log is ApprovalLog => {
        switch (log.type) {
            case 'unknown':
            case 'added_owner':
            case 'account_deployed':
            case 'threshold_updated':
            case 'set_allowance':
            case 'enable_module':
            case 'disable_module':
            case 'erc20_transfer':
            case 'safe_module_transaction':
            case 'safe_module_transaction_for_native_fee_payment':
            case 'safe_received':
            case 'user_operation_event':
            case 'user_operation_revert_reason':
            case 'native_wrapper_deposit':
            case 'native_wrapper_withdraw':
                return false
            case 'approval':
                return log.owner === accountAddress
            /* istanbul ignore next */
            default:
                return notReachable(log)
        }
    })
    const transfers = parseIndexedTransferTransaction({
        input,
        knownCurrencies,
        network: EARN_NETWORK,
    }).getSuccessResult()

    if (
        (!transfers || transfers.transfers.length === 0) &&
        approvalLogs.length === 1
    ) {
        const [approvalLog] = approvalLogs

        const currency = getCryptoCurrency({
            cryptoCurrencyId: approvalLog.currencyId,
            knownCurrencies,
        })

        if (!currency) {
            return failure({
                type: 'unknown currency',
                currencyId: approvalLog.currencyId,
            })
        }

        const token = {
            amount: approvalLog.amount,
            currency,
        }
        const tokenApprovalTransactionActivity: TokenApprovalTransactionActivity =
            {
                type: 'token_approval',
                approveTo: {
                    address: approvalLog.spender,
                    networkHexId: currency.networkHexChainId,
                    name: null,
                    logo: null,
                    website: null,
                },
                limit: {
                    type:
                        approvalLog.amount >= UNLIMITED_APPROVAL_AMOUNT
                            ? ('Unlimited' as const)
                            : ('Limited' as const),
                    amount: token,
                },
                hash: input.hash,
                networkHexId: currency.networkHexChainId,
                timestamp: new Date(input.timestamp),
                paidFee: null,
            }

        switch (tokenApprovalTransactionActivity.limit.type) {
            case 'Unlimited':
                return success(tokenApprovalTransactionActivity)
            case 'Limited':
                if (tokenApprovalTransactionActivity.limit.amount.amount > 0n) {
                    return success(tokenApprovalTransactionActivity)
                }
                return success({
                    type: 'token_approval_revoked',
                    token: currency,
                    approveTo: {
                        address: approvalLog.spender,
                        networkHexId: currency.networkHexChainId,
                        name: null,
                        logo: null,
                        website: null,
                    },
                    limit: tokenApprovalTransactionActivity.limit,
                    hash: input.hash,
                    networkHexId: currency.networkHexChainId,
                    timestamp: new Date(input.timestamp),
                    paidFee: null,
                })
            default:
                return notReachable(tokenApprovalTransactionActivity.limit)
        }
    }
    return failure({
        type: 'not_approve_transaction',
    })
}

type IndexedTransferTransaction = {
    from: address.Address
    to: address.Address | null
    blockNumber: bigint
    timestamp: number
    hash: Hexadecimal

    transfers: IndexedTransfer[]
}

type IndexedTransfer = {
    from: address.Address
    to: address.Address
    money: CryptoMoney
}

// TODO @resetko-zeal reuse for parsers which are checking user address as source
const parseIndexedTransferFromAddress = ({
    transfers,
    fromAddress,
}: {
    transfers: IndexedTransfer[]
    fromAddress: address.Address
}): Result<unknown, IndexedTransfer> => {
    const transfer = transfers.find((transfer) => transfer.from === fromAddress)

    return transfer
        ? success(transfer)
        : failure({
              type: 'no_transfers_from_given_address',
          })
}

const parseTransferToTaker = ({
    transfers,
    earn,
}: {
    transfers: IndexedTransfer[]
    earn: Earn
}): Result<unknown, { transfer: IndexedTransfer; taker: DeployedTaker }> =>
    parseDeployedTakers({ earn }).andThen((takers) => {
        for (const transfer of transfers) {
            const taker = takers.find((taker) => {
                return (
                    // aGnoEure is not rebalancing token,
                    // hence we want to exclude earn yeild comming from NULL_ADDRESS
                    transfer.from !== NULL_ADDRESS &&
                    transfer.to === taker.address
                )
            })
            if (taker) {
                return success({ transfer, taker })
            }
        }

        return failure({ type: 'transer_to_taker_not_found' })
    })

const parseNoTransfersFromRewards = ({
    transfers,
}: {
    transfers: IndexedTransfer[]
}): Result<unknown, null> => {
    const transfer = transfers.find(
        (transfer) => transfer.from === REWARDS_CONTRACT_ADDRESS
    )

    return transfer
        ? failure({ type: 'transfer_from_rewards_found' })
        : success(null)
}

const parseIndexedTransferTransaction = ({
    input,
    knownCurrencies,
    network,
}: {
    input: IndexedTransaction
    knownCurrencies: KnownCryptoCurrencies
    network: PredefinedNetwork
}): Result<unknown, IndexedTransferTransaction> => {
    const nativeTransferEOA: IndexedTransfer | null =
        input.to && toBigInt(input.value)
            ? {
                  from: input.from,
                  to: input.to,
                  money: {
                      amount: toBigInt(input.value),
                      currency: network.nativeCurrency,
                  },
              }
            : null

    const nativeTransafersSmartWallet: IndexedTransfer[] = input.logs
        .filter((log): log is SafeModuleTransactionLog | SafeReceivedLog => {
            switch (log.type) {
                case 'erc20_transfer':
                case 'added_owner':
                case 'approval':
                case 'account_deployed':
                case 'threshold_updated':
                case 'set_allowance':
                case 'enable_module':
                case 'disable_module':
                case 'unknown':
                case 'user_operation_event':
                case 'user_operation_revert_reason':
                case 'safe_module_transaction_for_native_fee_payment':
                case 'native_wrapper_deposit':
                case 'native_wrapper_withdraw':
                    return false
                case 'safe_module_transaction':
                case 'safe_received':
                    if (
                        nativeTransferEOA &&
                        nativeTransferEOA.from === log.from &&
                        nativeTransferEOA.to === log.to &&
                        nativeTransferEOA.money.amount === log.value
                    ) {
                        return false
                    }
                    return true

                /* istanbul ignore next */
                default:
                    return notReachable(log)
            }
        })
        .map<IndexedTransfer | null>((log) => {
            switch (log.type) {
                case 'safe_module_transaction':
                    return log.value > 0n
                        ? {
                              from: log.from,
                              to: log.to,
                              money: {
                                  amount: log.value,
                                  currency: network.nativeCurrency,
                              },
                          }
                        : null
                case 'safe_received': {
                    return log.value > 0n
                        ? {
                              from: log.from,
                              to: log.to,
                              money: {
                                  amount: log.value,
                                  currency: network.nativeCurrency,
                              },
                          }
                        : null
                }
                default:
                    return notReachable(log)
            }
        })
        .filter(excludeNullValues)
    const [unknowCurrencyTransafers, erc20Transfers] = groupByType(
        input.logs
            .filter((log): log is ERC20TransferLog => {
                switch (log.type) {
                    case 'unknown':
                    case 'added_owner':
                    case 'approval':
                    case 'account_deployed':
                    case 'threshold_updated':
                    case 'set_allowance':
                    case 'enable_module':
                    case 'disable_module':
                    case 'safe_module_transaction':
                    case 'safe_module_transaction_for_native_fee_payment':
                    case 'safe_received':
                    case 'user_operation_event':
                    case 'user_operation_revert_reason':
                    case 'native_wrapper_deposit':
                    case 'native_wrapper_withdraw':
                        return false
                    case 'erc20_transfer':
                        if (log.to === GNOSIS_PAY_SPENDER) {
                            return false
                        }
                        return true
                    /* istanbul ignore next */
                    default:
                        return notReachable(log)
                }
            })
            .map((log) =>
                MONERIUM_V1_TOKENS.has(log.currencyId)
                    ? {
                          ...log,
                          currencyId:
                              MONERIUM_GNOSIS_V1_TO_V2_MAP[log.currencyId],
                      }
                    : log
            )
            .reduce((acc, log) => {
                if (MONERIUM_V2_TOKENS.has(log.currencyId)) {
                    return acc.find(
                        (prevLog) =>
                            prevLog.currencyId === log.currencyId &&
                            prevLog.to === log.to &&
                            prevLog.from === log.from &&
                            prevLog.amount === log.amount
                    )
                        ? acc
                        : [...acc, log]
                }
                const existingLog = acc.find(
                    (prevLog) =>
                        prevLog.currencyId === log.currencyId &&
                        prevLog.to === log.to &&
                        prevLog.from === log.from
                )

                if (existingLog) {
                    return [
                        ...acc.filter((item) => item !== existingLog),
                        {
                            ...existingLog,
                            amount: existingLog.amount + log.amount,
                        },
                    ]
                } else {
                    return [...acc, log]
                }
            }, [] as ERC20TransferLog[])
            .map<Result<unknown, IndexedTransfer>>((log) => {
                const currency = getCryptoCurrency({
                    cryptoCurrencyId: log.currencyId,
                    knownCurrencies,
                })

                if (!currency) {
                    return failure({
                        type: 'unknown currency',
                        transactionHash: input.hash,
                        transactionLog: log.eventSignature,
                        currencyId: log.currencyId,
                    })
                }

                return success({
                    money: {
                        amount: log.amount,
                        currency: currency,
                    },
                    from: log.from,
                    to: log.to,
                })
            })
    )

    if (unknowCurrencyTransafers.length > 0) {
        captureError(
            new ImperativeError('Unknown currency in erc_20_transfer log', {
                unknowCurrencyTransafers,
            })
        )
    }

    const transfers = [
        nativeTransferEOA,
        ...nativeTransafersSmartWallet,
        ...erc20Transfers,
    ].filter(excludeNullValues)

    return success({
        from: input.from,
        to: input.to,
        blockNumber: BigInt(input.blockNumber),
        timestamp: input.timestamp,
        hash: input.hash,
        value: input.value,
        transfers,
    })
}

const parseSwapTransactionActivity = ({
    input,
    accountAddress,
}: {
    input: IndexedTransferTransaction
    accountAddress: address.Address
}): Result<unknown, SwapTransactionActivity> => {
    const ingoingTransfer = input.transfers.find((transfer) => {
        return transfer.to === accountAddress
    })
    const outgoingTransfer = input.transfers.find((transfer) => {
        return (
            transfer.from === accountAddress &&
            SOCKET_GATEWAY_CONTRACTS.has(transfer.to)
        )
    })
    if (ingoingTransfer === outgoingTransfer) {
        return failure({
            type: 'not_swap_transaction',
        })
    }

    if (ingoingTransfer && outgoingTransfer) {
        return success({
            type: 'swap',
            hash: input.hash,
            fromToken: outgoingTransfer.money,
            toToken: ingoingTransfer.money,
            networkHexId: ingoingTransfer.money.currency.networkHexChainId,
            timestamp: new Date(input.timestamp),
            paidFee: null,
        })
    }

    return failure({
        type: 'not_swap_transaction',
    })
}

const parseDepositIntoEarnTransactionActivity = ({
    input,
    earn,
    historicalTakerUserCurrencyRateMap,
    accountAddress,
}: {
    input: IndexedTransferTransaction
    earn: Earn
    historicalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    accountAddress: address.Address
}): Result<unknown, DepositIntoEarnTransactionActivity> =>
    shape({
        noTransfersFromRewards: parseNoTransfersFromRewards({
            transfers: input.transfers,
        }),
        transferFromUser: oneOf(input.transfers, [
            parseIndexedTransferFromAddress({
                transfers: input.transfers,
                fromAddress: accountAddress,
            }),
            success(null),
        ]),
        transferToTaker: parseTransferToTaker({
            transfers: input.transfers,
            earn,
        }),
    }).map(
        ({
            transferFromUser,
            transferToTaker: { transfer: transferToTaker, taker },
        }) => ({
            type: 'deposit_into_earn' as const,
            takerType: taker.type,
            fromAmount: transferFromUser?.money || null,
            toAmount: transferToTaker.money,
            toAmountInUserCurrency: applyRate2({
                baseAmount: transferToTaker.money,
                rate: historicalTakerUserCurrencyRateMap[taker.type][
                    Number(input.blockNumber)
                ],
            }),
            hash: input.hash,
            networkHexId: taker.cryptoCurrency.networkHexChainId,
            timestamp: new Date(input.timestamp),
            paidFee: null,
        })
    )

const parseBRewardClaimTransactionActivity = ({
    input,
    earn,
    historicalTakerUserCurrencyRateMap,
}: {
    input: IndexedTransferTransaction
    earn: Earn
    historicalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
}): Result<unknown, BRewardClaimTransactionActivity> =>
    shape({
        transferToTaker: parseTransferToTaker({
            transfers: input.transfers,
            earn,
        }),
        transferFromRewardsSender: parseIndexedTransferFromAddress({
            transfers: input.transfers,
            fromAddress: REWARDS_CONTRACT_ADDRESS,
        }),
    }).map(({ transferToTaker: { transfer: transferToTaker, taker } }) => ({
        type: 'breward_claim',
        takerType: taker.type,
        fromAmount: null,
        toAmount: transferToTaker.money,
        toAmountInUserCurrency: applyRate2({
            baseAmount: transferToTaker.money,
            rate: historicalTakerUserCurrencyRateMap[taker.type][
                Number(input.blockNumber)
            ],
        }),
        hash: input.hash,
        networkHexId: taker.cryptoCurrency.networkHexChainId,
        timestamp: new Date(input.timestamp),
        paidFee: null,
    }))

const parseWithdrawFromEarnTransactionActivity = ({
    input,
    earn,
    historicalTakerUserCurrencyRateMap,
    accountAddress,
    cardConfig,
}: {
    input: IndexedTransferTransaction
    earn: Earn
    historicalTakerUserCurrencyRateMap: HistoricalTakerUserCurrencyRateMap
    accountAddress: address.Address
    cardConfig: CardConfig
}): Result<unknown, WithdrawFromEarnTransactionActivity> => {
    const cardTransfers = parseCardTransactions({
        transfers: input.transfers,
        cardConfig,
    })
    if (cardTransfers.getSuccessResult()) {
        return failure({
            type: 'not_earn_withdraw_transaction',
        })
    }

    return parseTakerTransfer({
        transfers: input.transfers,
        earn,
    }).andThen(({ transfer, taker }) => {
        if (transfer.to !== accountAddress) {
            return failure({
                type: 'not_earn_withdraw_transaction',
            })
        }
        const ingoingSwapTransfer = input.transfers.find(
            (t) =>
                t.to === accountAddress &&
                t.money.currency.id !== taker.cryptoCurrency.id
        )

        return success({
            type: 'withdraw_from_earn',
            taker,
            toAmount: ingoingSwapTransfer?.money ?? transfer.money,
            hash: input.hash,
            networkHexId: taker.cryptoCurrency.networkHexChainId,
            timestamp: new Date(input.timestamp),
            paidFee: null,
            fromAmountInUserCurrency: applyRate2({
                baseAmount: transfer.money,
                rate: historicalTakerUserCurrencyRateMap[taker.type][
                    Number(input.blockNumber)
                ],
            }),
        })
    })
}

const parseBridgeTransactionOutgoingActivity = (
    input: SendTransactionActivity
): Result<unknown, OutgoingBridgeTransactionActivity> => {
    if (!SOCKET_GATEWAY_CONTRACTS.has(input.receiver)) {
        return failure({
            type: 'not_outgoing_bridge_transaction',
        })
    }

    return success({
        type: 'outgoing_bridge',
        toToken: input.amount,
        networkHexId: input.amount.currency.networkHexChainId,
        hash: input.hash,
        timestamp: new Date(input.timestamp),
        paidFee: null,
    })
}

const parseBridgeTransactionInComingActivity = (
    input: ReceiveTransactionActivity
): Result<unknown, IncomingBridgeTransactionActivity> => {
    if (!SOCKET_GATEWAY_CONTRACTS.has(input.sender)) {
        return failure({
            type: 'not_outgoing_bridge_transaction',
        })
    }
    return success({
        type: 'incoming_bridge',
        fromToken: input.amount,
        networkHexId: input.amount.currency.networkHexChainId,
        hash: input.hash,
        timestamp: new Date(input.timestamp),
        paidFee: null,
    })
}

const parseArbitrarySmartContractTransactionActivity = ({
    input,
    inputTransfers,
    accountAddress,
}: {
    input: IndexedTransaction
    inputTransfers: IndexedTransferTransaction
    accountAddress: address.Address
}): Result<unknown, ArbitrarySmartContractTransactionActivity> => {
    return oneOf(input, [
        match(input.from, accountAddress),
        match(
            input.logs.find((log): log is UserOperationEventLog => {
                switch (log.type) {
                    case 'erc20_transfer':
                    case 'added_owner':
                    case 'approval':
                    case 'account_deployed':
                    case 'threshold_updated':
                    case 'set_allowance':
                    case 'enable_module':
                    case 'disable_module':
                    case 'safe_module_transaction':
                    case 'safe_module_transaction_for_native_fee_payment':
                    case 'safe_received':
                    case 'unknown':
                    case 'native_wrapper_deposit':
                    case 'native_wrapper_withdraw':
                    case 'user_operation_revert_reason':
                        return false
                    case 'user_operation_event':
                        return true
                    /* istanbul ignore next */
                    default:
                        return notReachable(log)
                }
            })?.sender,
            accountAddress
        ),
    ]).andThen(() => {
        const outgoingTransfers = inputTransfers.transfers.filter(
            (transfer) => {
                return transfer.from === accountAddress
            }
        )

        const incomingTransfers = inputTransfers.transfers.filter(
            (transfer) => {
                return transfer.to === accountAddress
            }
        )

        const hasIncoming = incomingTransfers.length > 0
        const hasOutgoing = outgoingTransfers.length > 0

        const smartContractAddress = input.to

        if (!smartContractAddress) {
            return failure({
                type: 'not_arbitrary_smart_contract_interaction',
            })
        }

        let transactionType: ArbitrarySmartContractTransactionType

        switch (true) {
            case !hasIncoming && !hasOutgoing:
                transactionType = { type: 'noInNoOut' }
                break
            case hasIncoming && !hasOutgoing:
                transactionType = {
                    type: 'onlyIn',
                    incoming: incomingTransfers.map(
                        (transfer) => transfer.money
                    ),
                    priceInDefaultCurrency: null,
                }
                break
            case !hasIncoming && hasOutgoing:
                transactionType = {
                    type: 'onlyOut',
                    outgoing: outgoingTransfers.map(
                        (transfer) => transfer.money
                    ),
                    priceInDefaultCurrency: null,
                }
                break
            case hasIncoming &&
                hasOutgoing &&
                incomingTransfers.length === 1 &&
                outgoingTransfers.length === 1:
                transactionType = {
                    type: 'exactlyOneInOneOut',
                    incoming: incomingTransfers[0].money,
                    outgoing: outgoingTransfers[0].money,
                }
                break
            case hasIncoming &&
                hasOutgoing &&
                incomingTransfers.length >= 1 &&
                outgoingTransfers.length >= 1:
                transactionType = {
                    type: 'multipleInAndOut',
                    incoming: incomingTransfers.map(
                        (transfer) => transfer.money
                    ),
                    outgoing: outgoingTransfers.map(
                        (transfer) => transfer.money
                    ),
                }
                break
            default:
                return failure({
                    type: 'not_arbitrary_smart_contract_interaction',
                })
        }

        return success({
            type: 'arbitrary_smart_contract_interaction',
            functionSignature: null,
            smartContract: null,
            transactionType,
            networkHexId: GNOSIS.hexChainId,
            hash: input.hash,
            timestamp: new Date(input.timestamp),
            paidFee: null,
        })
    })
}
