import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import * as Web3 from '@zeal/toolkit/Web3'

import { requestNativeBalance } from '@zeal/domains/Address/api/fetchNativeBalance'
import { KnownCurrencies } from '@zeal/domains/Currency'
import { MONERIUM_V2_TOKENS } from '@zeal/domains/Currency/constants'
import {
    NetworkHexId,
    NetworkMap,
    NetworkRPCMap,
    PredefinedNetwork,
    TestNetwork,
} from '@zeal/domains/Network'
import {
    ParsedLog,
    SafeModuleTransactionForNativeFeePaymentLog,
    UserOperationRevertReasonLog,
} from '@zeal/domains/RPCRequest'
import { fetchRPCBatch2WithRetry } from '@zeal/domains/RPCRequest/api/fetchRPCResponse'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { createGetTransactionReceiptRequest } from '@zeal/domains/TransactionRequest/domains/SubmitedTransaction/api/fetchTransactionReceiptWithRetry'
import { createGetTransactionByHashRequest } from '@zeal/domains/Transactions/api/fetchTransactionByHash'
import { SimulatedTransaction } from '@zeal/domains/Transactions/domains/SimulatedTransaction'
import { getFee } from '@zeal/domains/Transactions/helpers/getFee'
import { PAYMASTERS_FEE_RECEIVERS } from '@zeal/domains/UserOperation/constants'
import { getUserOperationRelatedLogs } from '@zeal/domains/UserOperation/helpers/getUserOperationRelatedLogs'

import { fetchSimulatedTransactionFromOnchainData } from './fetchSimulatedTransactionFromOnchainData'

import { getMethodName } from '../helpers/getMethodName'

export type FetchTransactionResultByRequest2 = (_: {
    network: PredefinedNetwork | TestNetwork
    request: Request
    networkRPCMap: NetworkRPCMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    sender: Web3.address.Address
    signal?: AbortSignal
}) => Promise<{
    simulatedTransaction: SimulatedTransaction
    knownCurrencies: KnownCurrencies
}>

type Request =
    | { type: 'rpc_request'; transactionHash: Hexadecimal.Hexadecimal }
    | {
          type: 'user_operation'
          userOperationHash: Hexadecimal.Hexadecimal
          bundleTransactionHash: Hexadecimal.Hexadecimal
      }

const filterParsedLogs = ({
    log,
    networkHexId,
    sender,
}: {
    networkHexId: NetworkHexId
    sender: Web3.address.Address
    log: ParsedLog
}): boolean => {
    switch (log.type) {
        case 'erc20_transfer': {
            switch (true) {
                case MONERIUM_V2_TOKENS.has(log.currencyId):
                    return false
                case log.from === sender &&
                    log.to === PAYMASTERS_FEE_RECEIVERS[networkHexId]:
                    return false
                default:
                    return true
            }
        }

        case 'user_operation_event':
        case 'user_operation_revert_reason':
        case 'account_deployed':
        case 'added_owner':
        case 'approval':
        case 'disable_module':
        case 'enable_module':
        case 'safe_module_transaction':
        case 'safe_received':
        case 'set_allowance':
        case 'threshold_updated':
        case 'native_wrapper_deposit':
        case 'native_wrapper_withdraw':
        case 'unknown':
        case 'safe_module_transaction_for_native_fee_payment':
            return true
        /* istanbul ignore next */
        default:
            return notReachable(log)
    }
}

export const fetchTransactionResultByRequest2: FetchTransactionResultByRequest2 =
    ({
        network,
        request,
        networkRPCMap,
        defaultCurrencyConfig,
        networkMap,
        sender,
        signal,
    }): Promise<{
        simulatedTransaction: SimulatedTransaction
        knownCurrencies: KnownCurrencies
    }> => {
        switch (request.type) {
            case 'rpc_request':
                return fetchEOATransactionResult({
                    network,
                    transactionHash: request.transactionHash,
                    defaultCurrencyConfig,
                    networkMap,
                    networkRPCMap,
                    sender,
                    signal,
                })
            case 'user_operation':
                return fetchUserOperationResult({
                    network,
                    userOperationHash: request.userOperationHash,
                    bundleTransactionHash: request.bundleTransactionHash,
                    sender,
                    signal,
                    networkRPCMap,
                    defaultCurrencyConfig,
                    networkMap,
                })
            /* istanbul ignore next */
            default:
                return notReachable(request)
        }
    }

const fetchEOATransactionResult = async ({
    network,
    transactionHash,
    networkRPCMap,
    defaultCurrencyConfig,
    networkMap,
    sender,
    signal,
}: {
    network: PredefinedNetwork | TestNetwork
    sender: Web3.address.Address
    transactionHash: Hexadecimal.Hexadecimal
    networkRPCMap: NetworkRPCMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    signal?: AbortSignal
}): Promise<{
    simulatedTransaction: SimulatedTransaction
    knownCurrencies: KnownCurrencies
}> => {
    const [receipt, body] = await fetchRPCBatch2WithRetry(
        [
            createGetTransactionReceiptRequest({
                network,
                transactionHash,
            }),
            createGetTransactionByHashRequest({
                transactionHash,
            }),
        ],
        {
            network,
            networkRPCMap,
            signal,
        }
    )

    const [beforeNativeBalance, afterNativeBalance] =
        await fetchRPCBatch2WithRetry(
            [
                requestNativeBalance({
                    network,
                    address: sender,
                    block: receipt.blockNumber - 1n,
                }),
                requestNativeBalance({
                    network,
                    address: sender,
                    block: receipt.blockNumber,
                }),
            ],
            {
                network,
                networkRPCMap,
                signal,
            }
        )

    switch (receipt.status) {
        case 'failed':
            return {
                simulatedTransaction: {
                    type: 'FailedTransaction',
                    method: body.input
                        ? getMethodName({ input: body.input })
                        : '',
                },
                knownCurrencies: {},
            }

        case 'success':
            const fee = (() => {
                switch (receipt.receiptGasInfo.type) {
                    case 'generic':
                    case 'l2_rollup':
                        return getFee({
                            currency: network.nativeCurrency,
                            gasInfo: receipt.receiptGasInfo,
                        })
                    case 'no_gas_price':
                        return {
                            amount: 0n,
                            currency: network.nativeCurrency,
                        }
                    /* istanbul ignore next */
                    default:
                        return notReachable(receipt.receiptGasInfo)
                }
            })()

            return fetchSimulatedTransactionFromOnchainData({
                network,
                logs: receipt.logs.filter((log) =>
                    filterParsedLogs({
                        log,
                        networkHexId: network.hexChainId,
                        sender,
                    })
                ),
                to: receipt.to,
                value: body.value,
                input: body.input,
                defaultCurrencyConfig,
                networkMap,
                networkRPCMap,
                sender,
                nativeBalanceDiff:
                    afterNativeBalance.amount -
                    beforeNativeBalance.amount +
                    fee.amount, // compensate for gas
                signal,
            })

        /* istanbul ignore next */
        default:
            return notReachable(receipt)
    }
}

const fetchUserOperationResult = async ({
    network,
    sender,
    userOperationHash,
    bundleTransactionHash,
    networkRPCMap,
    defaultCurrencyConfig,
    networkMap,
    signal,
}: {
    network: PredefinedNetwork | TestNetwork
    userOperationHash: Hexadecimal.Hexadecimal
    bundleTransactionHash: Hexadecimal.Hexadecimal
    sender: Web3.address.Address
    networkRPCMap: NetworkRPCMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    signal?: AbortSignal
}): Promise<{
    simulatedTransaction: SimulatedTransaction
    knownCurrencies: KnownCurrencies
}> => {
    const [bundleReceipt] = await fetchRPCBatch2WithRetry(
        [
            createGetTransactionReceiptRequest({
                network,
                transactionHash: bundleTransactionHash,
            }),
        ],
        {
            network,
            networkRPCMap,
            signal,
        }
    )

    const [beforeNativeBalance, afterNativeBalance] =
        await fetchRPCBatch2WithRetry(
            [
                requestNativeBalance({
                    network,
                    address: sender,
                    block: bundleReceipt.blockNumber - 1n,
                }),
                requestNativeBalance({
                    network,
                    address: sender,
                    block: bundleReceipt.blockNumber,
                }),
            ],
            {
                network,
                networkRPCMap,
                signal,
            }
        )

    switch (bundleReceipt.status) {
        case 'failed':
            throw new ImperativeError(
                'UserOperation bundle failed or not found after being confirmed',
                {
                    bundleTransactionHash,
                    userOperationHash,
                    status: bundleReceipt.status,
                }
            )

        case 'success': {
            const { safeModuleTransactionLog, userOpRelatedLogs } =
                getUserOperationRelatedLogs({
                    logs: bundleReceipt.logs,
                    userOperationHash,
                })

            const nativeFeeEvent = userOpRelatedLogs.find(
                (log): log is SafeModuleTransactionForNativeFeePaymentLog => {
                    switch (log.type) {
                        case 'safe_module_transaction_for_native_fee_payment':
                            return true
                        case 'erc20_transfer':
                        case 'added_owner':
                        case 'approval':
                        case 'account_deployed':
                        case 'threshold_updated':
                        case 'set_allowance':
                        case 'enable_module':
                        case 'disable_module':
                        case 'safe_module_transaction':
                        case 'safe_received':
                        case 'user_operation_event':
                        case 'user_operation_revert_reason':
                        case 'native_wrapper_deposit':
                        case 'unknown':
                        case 'native_wrapper_withdraw':
                            return false
                        /* istanbul ignore next */
                        default:
                            return notReachable(log)
                    }
                }
            )

            const revertEvent =
                userOpRelatedLogs.find(
                    (log): log is UserOperationRevertReasonLog => {
                        switch (log.type) {
                            case 'user_operation_revert_reason':
                                return true
                            case 'account_deployed':
                            case 'added_owner':
                            case 'approval':
                            case 'disable_module':
                            case 'enable_module':
                            case 'erc20_transfer':
                            case 'safe_module_transaction':
                            case 'safe_received':
                            case 'set_allowance':
                            case 'threshold_updated':
                            case 'unknown':
                            case 'user_operation_event':
                            case 'safe_module_transaction_for_native_fee_payment':
                            case 'native_wrapper_deposit':
                            case 'native_wrapper_withdraw':
                                return false
                            /* istanbul ignore next */
                            default:
                                return notReachable(log)
                        }
                    }
                ) || null

            if (revertEvent) {
                return {
                    simulatedTransaction: {
                        type: 'FailedTransaction',
                        method: getMethodName({
                            input: safeModuleTransactionLog.data,
                        }),
                    },
                    knownCurrencies: {},
                }
            }

            return fetchSimulatedTransactionFromOnchainData({
                network,
                logs: userOpRelatedLogs.filter((log) =>
                    filterParsedLogs({
                        log,
                        networkHexId: network.hexChainId,
                        sender,
                    })
                ),
                to: safeModuleTransactionLog.to,
                value: null,
                input: safeModuleTransactionLog.data,
                defaultCurrencyConfig,
                networkMap,
                networkRPCMap,
                sender,
                nativeBalanceDiff:
                    afterNativeBalance.amount -
                    beforeNativeBalance.amount +
                    (nativeFeeEvent ? nativeFeeEvent.value : 0n), // compensate for gas
                signal,
            })
        }
        /* istanbul ignore next */
        default:
            return notReachable(bundleReceipt)
    }
}
