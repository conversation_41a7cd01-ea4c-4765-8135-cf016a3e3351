import { get, post } from '@zeal/api/request'

import { notReachable } from '@zeal/toolkit'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { stringify } from '@zeal/toolkit/JSON'
import { object, Result, shape } from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { CurrencyId, KnownCurrencies } from '@zeal/domains/Currency'
import { FIAT_CURRENCIES } from '@zeal/domains/Currency/constants'
import { parseKnownCurrencies } from '@zeal/domains/Currency/helpers/parse'
import {
    NetworkMap,
    NetworkRPCMap,
    PredefinedNetwork,
    TestNetwork,
} from '@zeal/domains/Network'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { getPredefinedNetworkMap } from '@zeal/domains/Network/helpers/getPredefinedNetworkMap'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import {
    SimulatedTransaction,
    UnknownTransaction,
} from '@zeal/domains/Transactions/domains/SimulatedTransaction'
import { parseSimulatedTransaction } from '@zeal/domains/Transactions/domains/SimulatedTransaction/parsers/parseSimulatedTransaction'
import { getExplorerLink } from '@zeal/domains/Transactions/domains/TransactionHash/helpers/getExplorerLink'

import { fetchTransactionResultByRequest2 } from './fetchTransactionResult2'
import fixture from './result_tokens_discrepancy_fixture.json'

export type TransactionResultResponse = {
    transaction: SimulatedTransaction
    currencies: KnownCurrencies
}

export type FetchTransactionResultByRequest = (_: {
    network: PredefinedNetwork | TestNetwork
    request: Request
    networkRPCMap: NetworkRPCMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    sender: Web3.address.Address
    signal?: AbortSignal
}) => Promise<TransactionResultResponse>

const parse = (input: unknown): Result<unknown, TransactionResultResponse> =>
    object(input).andThen((dto) =>
        shape({
            transaction: parseSimulatedTransaction(
                dto.transaction,
                dto.currencies
            ),
            currencies: parseKnownCurrencies(dto.currencies),
        })
    )

type Request =
    | { type: 'rpc_request'; transactionHash: Hexadecimal.Hexadecimal }
    | {
          type: 'user_operation'
          userOperationHash: Hexadecimal.Hexadecimal
          bundleTransactionHash: Hexadecimal.Hexadecimal
          sender: Web3.address.Address
      }

const isValidDiscrepancy = (
    be: SimulatedTransaction,
    fe: SimulatedTransaction
) => {
    if (be.type === 'UnknownTransaction' && fe.type === 'UnknownTransaction') {
        const beHash = Object.entries(
            be.tokens.reduce(
                (hash, token) => {
                    if (!hash[token.amount.currencyId]) {
                        hash[token.amount.currencyId] = 0n
                    }

                    switch (token.direction) {
                        case 'Send':
                            hash[token.amount.currencyId] -= token.amount.amount
                            return hash

                        case 'Receive':
                            hash[token.amount.currencyId] += token.amount.amount
                            return hash

                        default:
                            return notReachable(token.direction)
                    }
                },
                {} as Record<CurrencyId, bigint>
            )
        ).toSorted(([aId, aSum], [bId, bSum]) =>
            `${aId}-${aSum}`.localeCompare(`${bId}-${bSum}`)
        )

        const feHash = Object.entries(
            fe.tokens.reduce(
                (hash, token) => {
                    if (!hash[token.amount.currencyId]) {
                        hash[token.amount.currencyId] = 0n
                    }

                    switch (token.direction) {
                        case 'Send':
                            hash[token.amount.currencyId] -= token.amount.amount
                            return hash

                        case 'Receive':
                            hash[token.amount.currencyId] += token.amount.amount
                            return hash

                        default:
                            return notReachable(token.direction)
                    }
                },
                {} as Record<CurrencyId, bigint>
            )
        ).toSorted(([aId, aSum], [bId, bSum]) =>
            `${aId}-${aSum}`.localeCompare(`${bId}-${bSum}`)
        )

        return stringify(beHash) === stringify(feHash) // result of currency reduce is same as in old be
    }

    return false
}

window.fixture = fixture

const debug = ({
    network,
    tx1,
    tx2,
    txHash,
}: {
    tx1: UnknownTransaction
    tx2: UnknownTransaction
    network: PredefinedNetwork | TestNetwork
    txHash: Hexadecimal.Hexadecimal
}) => {
    const output = {
        v1: tx1.tokens
            .map((t) => t.amount)
            .toSorted((a, b) => {
                if (a.currencyId === b.currencyId) {
                    return Number(a.amount - b.amount)
                }
                return a.currencyId.localeCompare(b.currencyId)
            }),
        v2: tx2.tokens
            .map((t) => t.amount)
            .toSorted((a, b) => {
                if (a.currencyId === b.currencyId) {
                    return Number(a.amount - b.amount)
                }
                return a.currencyId.localeCompare(b.currencyId)
            }),
    }

    const v1Strings = output.v1
        .map((t) => `${t.amount.toString()}-${t.currencyId}`)
        .toSorted((a, b) => a.localeCompare(b))
    const v2Strings = output.v2
        .map((t) => `${t.amount.toString()}-${t.currencyId}`)
        .toSorted((a, b) => a.localeCompare(b))
    const discrepancyV1notInV2 = v1Strings.filter((s) => !v2Strings.includes(s))
    const discrepancyV2notInV1 = v2Strings.filter((s) => !v1Strings.includes(s))
    const discrepancies = [...discrepancyV1notInV2, ...discrepancyV2notInV1]
    const allDiscrepanciesAreNative = discrepancies.every(
        (d) => d.split('-')[1] === network.nativeCurrency.id
    )

    console.log('tx1', tx1)
    console.log('tx2', tx2)

    if (allDiscrepanciesAreNative) {
        console.log(
            'discrepancy! allDiscrepanciesAreNative',
            getExplorerLink({ transactionHash: txHash }, network),
            network.hexChainId
        )
    }
}

const testOne = async (
    index: number
): Promise<
    | { type: 'ok' }
    | { type: 'not_ok'; item: any; itemIndex: number; reason: string }
> => {
    const item = fixture[index] as any
    const networkMap = getPredefinedNetworkMap()
    const network = findNetworkByHexChainId(item.network, networkMap) as any

    const txHash =
        item.request.transactionHash || item.request.bundleTransactionHash
    console.log(getExplorerLink({ transactionHash: txHash }, network))

    const old = await fetchTransactionResultByRequest({
        defaultCurrencyConfig: {
            defaultCurrency: FIAT_CURRENCIES.USD as any,
        },
        network,
        networkMap,
        networkRPCMap: {},
        request: item.request,
        sender: item.request.sender || item.sender,
    })

    const newR = await fetchTransactionResultByRequest2({
        defaultCurrencyConfig: {
            defaultCurrency: FIAT_CURRENCIES.USD as any,
        },
        network,
        networkMap,
        networkRPCMap: {},
        request: item.request,
        sender: item.request.sender || item.sender,
    })

    const tx1 = old.transaction
    const tx2 = newR.simulatedTransaction

    if (
        tx1.type === 'UnknownTransaction' &&
        tx2.type === 'UnknownTransaction'
    ) {
        const output = {
            v1: tx1.tokens
                .map((t) => t.amount)
                .toSorted((a, b) => {
                    if (a.currencyId === b.currencyId) {
                        return Number(a.amount - b.amount)
                    }
                    return a.currencyId.localeCompare(b.currencyId)
                }),
            v2: tx2.tokens
                .map((t) => t.amount)
                .toSorted((a, b) => {
                    if (a.currencyId === b.currencyId) {
                        return Number(a.amount - b.amount)
                    }
                    return a.currencyId.localeCompare(b.currencyId)
                }),
        }

        const result = stringify(output.v1) === stringify(output.v2)

        if (!result) {
            if (isValidDiscrepancy(tx1, tx2)) {
                return { type: 'ok' }
            }

            debug({ network, tx1, tx2, txHash })

            return {
                type: 'not_ok',
                item,
                itemIndex: index,
                reason: 'unknown',
            }
        } else {
            return {
                type: 'ok',
            }
        }
    } else {
        return {
            type: 'not_ok',
            item,
            itemIndex: index,
            reason: 'not unknown',
        }
    }
}

window.testOne = testOne
window.test = async (limit?: number) => {
    const start = 0
    const notOk = []
    console.log('total', fixture.length)

    for (let i = start; i < (limit || fixture.length); i++) {
        const result = await testOne(i)
        switch (result.type) {
            case 'ok':
                console.log('ok!', i)
                break
            case 'not_ok':
                console.log('not ok!', result)
                notOk.push(result)
                break
            default:
                notReachable(result)
        }
        await new Promise((resolve) => setTimeout(resolve, 50))
    }

    console.log('done!')
    console.log('not ok!', notOk, notOk.length)
}

export const fetchTransactionResultByRequest: FetchTransactionResultByRequest =
    async ({
        network,
        request,
        defaultCurrencyConfig,
        networkMap,
        networkRPCMap,
        sender,
        signal,
    }): Promise<TransactionResultResponse> => {
        const response = await (() => {
            switch (request.type) {
                case 'rpc_request':
                    return fetchRPCTransactionResult({
                        network,
                        transactionHash: request.transactionHash,
                        signal,
                    })
                case 'user_operation':
                    return fetchUserOperationResult({
                        network,
                        userOperationHash: request.userOperationHash,
                        bundleTransactionHash: request.bundleTransactionHash,
                        sender: request.sender,
                        signal,
                    })
                /* istanbul ignore next */
                default:
                    return notReachable(request)
            }
        })()

        // fetchTransactionResultByRequest2({
        //     defaultCurrencyConfig,
        //     network,
        //     networkMap,
        //     networkRPCMap,
        //     request: (() => {
        //         switch (request.type) {
        //             case 'rpc_request': {
        //                 const request2: Parameters<
        //                     typeof fetchTransactionResultByRequest2
        //                 >[0]['request'] = {
        //                     type: 'rpc_request',
        //                     transactionHash: request.transactionHash,
        //                 }
        //                 return request2
        //             }

        //             case 'user_operation': {
        //                 const request2: Parameters<
        //                     typeof fetchTransactionResultByRequest2
        //                 >[0]['request'] = {
        //                     type: 'user_operation',
        //                     userOperationHash: request.userOperationHash,
        //                     bundleTransactionHash:
        //                         request.bundleTransactionHash,
        //                 }
        //                 return request2
        //             }
        //             /* istanbul ignore next */
        //             default:
        //                 return notReachable(request)
        //         }
        //     })(),
        //     sender,
        // }).then((result2) => {
        //     const trx = response.transaction
        //     const trx2 = result2.simulatedTransaction

        //     if (trx.type !== trx2.type) {
        //         captureError(
        //             new ImperativeError(
        //                 '[simulation2] fetchTransactionResultByRequest2 type level discrepancy detected',
        //                 {
        //                     networkHexId: network.hexChainId,
        //                     request,
        //                     sender,
        //                     trx,
        //                     trx2,
        //                 }
        //             )
        //         )
        //     } else if (
        //         trx.type === 'UnknownTransaction' &&
        //         trx2.type === 'UnknownTransaction' &&
        //         trx.tokens.length !== trx2.tokens.length
        //     ) {
        //         captureError(
        //             new ImperativeError(
        //                 '[simulation2] SimulationResult tokens level discrepancy detected',
        //                 {
        //                     networkHexId: network.hexChainId,
        //                     request,
        //                     trx,
        //                     trx2,
        //                 }
        //             )
        //         )
        //     }
        // })

        return response
    }

const fetchRPCTransactionResult = async ({
    network,
    transactionHash,
    signal,
}: {
    network: PredefinedNetwork | TestNetwork
    transactionHash: string
    signal?: AbortSignal
}): Promise<TransactionResultResponse> => {
    switch (network.type) {
        case 'predefined':
            switch (network.name) {
                case 'Ethereum':
                case 'Arbitrum':
                case 'zkSync':
                case 'BSC':
                case 'Polygon':
                case 'PolygonZkevm':
                case 'Linea':
                case 'Fantom':
                case 'Optimism':
                case 'Base':
                case 'Blast':
                case 'OPBNB':
                case 'Gnosis':
                case 'Celo':
                case 'Avalanche':
                case 'Cronos':
                case 'Mantle':
                case 'Manta':
                case 'Aurora':
                    return get(
                        `/wallet/transaction/${transactionHash}/result`,
                        {
                            query: { network: network.name },
                        },
                        signal
                    ).then((data) =>
                        parse(data).getSuccessResultOrThrow(
                            'Failed to parse submitted transaction simulation'
                        )
                    )

                case 'Sonic':
                    // FIXME :: @max durty hack we need to rethink simulations before pushing custom networks out
                    return {
                        transaction: {
                            type: 'UnknownTransaction',
                            nfts: [],
                            tokens: [],
                            method: '',
                        },
                        currencies: {},
                    }
                default:
                    return notReachable(network)
            }

        case 'testnet':
            return get(
                `/wallet/transaction/${transactionHash}/result`,
                {
                    query: { network: network.name },
                },
                signal
            ).then((data) =>
                parse(data).getSuccessResultOrThrow(
                    'Failed to parse submitted transaction simulation'
                )
            )

        /* istanbul ignore next */
        default:
            return notReachable(network)
    }
}

const fetchUserOperationResult = async ({
    network,
    sender,
    userOperationHash,
    bundleTransactionHash,
    signal,
}: {
    network: PredefinedNetwork | TestNetwork
    userOperationHash: string
    bundleTransactionHash: string
    sender: Web3.address.Address
    signal?: AbortSignal
}): Promise<TransactionResultResponse> => {
    switch (network.type) {
        case 'predefined':
        case 'testnet':
            return post(
                '/wallet/user-ops-transaction/result',
                {
                    body: {
                        sender,
                        network: network.name,
                        userOpHash: userOperationHash,
                        transactionHash: bundleTransactionHash,
                    },
                },
                signal
            ).then((response) =>
                parse(response).getSuccessResultOrThrow(
                    'Failed to parse user operation result'
                )
            )
        /* istanbul ignore next */
        default:
            return notReachable(network)
    }
}
