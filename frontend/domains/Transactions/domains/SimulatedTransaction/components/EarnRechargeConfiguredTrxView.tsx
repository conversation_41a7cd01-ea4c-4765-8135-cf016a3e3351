import { FormattedMessage } from 'react-intl'

import { Column } from '@zeal/uikit/Column'
import { Text } from '@zeal/uikit/Text'

import { convertStableCoinCurrencyToFiatCurrency } from '@zeal/domains/Currency/helpers/convertStableCoinCurrencyToFiatCurrency'
import { FormattedMoneyPrecise } from '@zeal/domains/Money/components/FormattedMoneyPrecise'
import {
    EarnRechargeConfigured,
    EarnRechargeUpdated,
} from '@zeal/domains/Transactions/domains/SimulatedTransaction'

type Props = {
    transaction: EarnRechargeConfigured | EarnRechargeUpdated
}
export const EarnRechargeConfiguredTrxView = ({ transaction }: Props) => {
    return (
        <Column spacing={16}>
            <Text variant="paragraph" weight="regular" color="textSecondary">
                <FormattedMessage
                    id="earn.recharge_configured.trx.subtitle"
                    defaultMessage="After each payment, cash will automatically be added from your Earn account(s) to keep your card balance at {value}"
                    values={{
                        value: (
                            <FormattedMoneyPrecise
                                withSymbol
                                sign={null}
                                money={{
                                    currency:
                                        convertStableCoinCurrencyToFiatCurrency(
                                            {
                                                cryptoCurrency:
                                                    transaction.request
                                                        .cardConfig.currency,
                                            }
                                        ),
                                    amount: transaction.request.threshold,
                                }}
                            />
                        ),
                    }}
                />
            </Text>

            <Text variant="paragraph" weight="regular" color="textSecondary">
                <FormattedMessage
                    id="earn.recharge_configured.trx.disclaimer"
                    defaultMessage="When you use your card, a CowSwap auction is created to buy the same amount as your payment using your Earn assets. This auction process typically gets you the best market rate, but be aware that the onchain rate may differ from real-world exchange rates."
                />
            </Text>
        </Column>
    )
}
