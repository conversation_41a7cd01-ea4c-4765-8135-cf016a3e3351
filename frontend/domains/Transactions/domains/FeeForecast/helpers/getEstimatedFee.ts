import { notReachable } from '@zeal/toolkit'
import { PollableData } from '@zeal/toolkit/LoadableData/PollableData'

import {
    FeeForecastRequest,
    FeeForecastResponse,
} from '@zeal/domains/Transactions/domains/FeeForecast'
import { EstimatedFee } from '@zeal/domains/Transactions/domains/FeeForecast'

export const getEstimatedFee = (
    pollable: Extract<
        PollableData<FeeForecastResponse, FeeForecastRequest>,
        { type: 'loaded' | 'reloading' | 'subsequent_failed' }
    >
): EstimatedFee | null => {
    switch (pollable.data.type) {
        case 'FeesForecastResponseEip1559RecommendedFee':
            return pollable.data.fast

        case 'FeesForecastResponseLegacyFee':
        case 'FeesForecastResponseEip1559Fee':
            switch (pollable.params.selectedPreset.type) {
                case 'Slow':
                    return pollable.data.slow
                case 'Normal':
                    return pollable.data.normal
                case 'Fast':
                    return pollable.data.fast
                case 'Custom':
                    return pollable.data.custom
                /* istanbul ignore next */
                default:
                    return notReachable(pollable.params.selectedPreset)
            }

        default:
            return notReachable(pollable.data)
    }
}
