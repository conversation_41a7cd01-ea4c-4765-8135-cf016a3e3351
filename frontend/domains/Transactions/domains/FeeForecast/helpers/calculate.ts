import { notReachable } from '@zeal/toolkit'
import { div, mulByNumber } from '@zeal/toolkit/BigInt'
import { ImperativeError } from '@zeal/toolkit/Error'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { PollableData } from '@zeal/toolkit/LoadableData/PollableData'

import { mulByNumber as helpersMulByNumber } from '@zeal/domains/Money/helpers/mul'
import {
    Eip1559CustomPresetRequestFee,
    Eip1559Fee,
    EstimatedFee,
    FeeForecastRequest,
    FeeForecastResponse,
    LegacyCustomPresetRequestFee,
    LegacyFee,
} from '@zeal/domains/Transactions/domains/FeeForecast'

type LoadedPollable = Extract<
    PollableData<FeeForecastResponse, FeeForecastRequest>,
    { type: 'loaded' | 'reloading' | 'subsequent_failed' }
>

const GAS_MULTIPLIER = 1.11 // at least 10%

export const calculate = (
    old:
        | EstimatedFee
        | LegacyCustomPresetRequestFee
        | Eip1559CustomPresetRequestFee,
    newFee: EstimatedFee
): EstimatedFee => {
    switch (old.type) {
        case 'LegacyCustomPresetRequestFee':
        case 'LegacyFee':
            switch (newFee.type) {
                case 'LegacyFee':
                    return calculateLegacyFee(old, newFee)
                case 'Eip1559Fee':
                    throw new ImperativeError(
                        'cannot mix legacy and Eip1559Fee fees'
                    )
                /* istanbul ignore next */
                default:
                    return notReachable(newFee)
            }
        case 'Eip1559Fee':
        case 'Eip1559CustomPresetRequestFee':
            switch (newFee.type) {
                case 'LegacyFee':
                    throw new ImperativeError(
                        'cannot mix Eip1559Fee and legacy fees'
                    )
                case 'Eip1559Fee':
                    return calculateEip1559Fee(old, newFee)
                /* istanbul ignore next */
                default:
                    return notReachable(newFee)
            }
        /* istanbul ignore next */
        default:
            return notReachable(old)
    }
}

export const mergeFastFee = (
    pollable: LoadedPollable,
    newFastFee: EstimatedFee
): LoadedPollable => {
    switch (pollable.data.type) {
        case 'FeesForecastResponseLegacyFee':
            switch (newFastFee.type) {
                case 'LegacyFee':
                    return {
                        ...pollable,
                        data: {
                            ...pollable.data,
                            type: 'FeesForecastResponseLegacyFee',
                            fast: newFastFee,
                        },
                    }
                case 'Eip1559Fee':
                    throw new ImperativeError(
                        'cannot mix legacy and Eip1559Fee fees'
                    )
                /* istanbul ignore next */
                default:
                    return notReachable(newFastFee)
            }
        case 'FeesForecastResponseEip1559RecommendedFee':
            switch (newFastFee.type) {
                case 'LegacyFee':
                    throw new ImperativeError(
                        'cannot mix legacy and Eip1559Fee fees'
                    )
                case 'Eip1559Fee':
                    return {
                        ...pollable,
                        data: {
                            ...pollable.data,
                            type: 'FeesForecastResponseEip1559RecommendedFee',
                            fast: newFastFee,
                        },
                    }
                /* istanbul ignore next */
                default:
                    return notReachable(newFastFee)
            }

        case 'FeesForecastResponseEip1559Fee':
            switch (newFastFee.type) {
                case 'LegacyFee':
                    throw new ImperativeError(
                        'cannot mix legacy and Eip1559Fee fees'
                    )
                case 'Eip1559Fee':
                    return {
                        ...pollable,
                        data: {
                            ...pollable.data,
                            type: 'FeesForecastResponseEip1559Fee',
                            fast: newFastFee,
                        },
                    }
                /* istanbul ignore next */
                default:
                    return notReachable(newFastFee)
            }
        /* istanbul ignore next */
        default:
            return notReachable(pollable.data)
    }
}

const calculateEip1559Fee = (
    old: Eip1559Fee | Eip1559CustomPresetRequestFee,
    newFee: Eip1559Fee
): Eip1559Fee => {
    switch (old.type) {
        case 'Eip1559CustomPresetRequestFee':
            return newFee
        case 'Eip1559Fee':
            const { base: oldBase, priority: oldPriority } = splitFee(old)
            const { base: newBase, priority: newPriority } = splitFee(newFee)
            const calculatedBase = compare(oldBase, newBase, GAS_MULTIPLIER)
            const calculatedPriority = compare(
                oldPriority,
                newPriority,
                GAS_MULTIPLIER
            )
            const calculatedFee = calculatedBase + calculatedPriority

            const rate = div(
                calculatedFee,
                Hexadecimal.toBigInt(
                    newFee.maxFeePerGas as Hexadecimal.Hexadecimal
                ) // TODO @resetko-zeal remove cast
            )

            return {
                type: 'Eip1559Fee',
                maxPriorityFeePerGas:
                    Hexadecimal.fromBigInt(calculatedPriority),
                forecastDuration: newFee.forecastDuration,
                maxFeePerGas: Hexadecimal.fromBigInt(calculatedFee),

                priceInNativeCurrency: helpersMulByNumber(
                    newFee.priceInNativeCurrency,
                    rate
                ),
                maxPriceInNativeCurrency: helpersMulByNumber(
                    newFee.maxPriceInNativeCurrency,
                    rate
                ),
                maxPriceInDefaultCurrency: newFee.maxPriceInDefaultCurrency
                    ? helpersMulByNumber(newFee.maxPriceInDefaultCurrency, rate)
                    : null,
                priceInDefaultCurrency: newFee.priceInDefaultCurrency
                    ? helpersMulByNumber(newFee.priceInDefaultCurrency, rate)
                    : null,
            }

        /* istanbul ignore next */
        default:
            return notReachable(old)
    }
}

const calculateLegacyFee = (
    old: LegacyFee | LegacyCustomPresetRequestFee,
    newFee: LegacyFee
): LegacyFee => {
    switch (old.type) {
        case 'LegacyCustomPresetRequestFee':
            return newFee
        case 'LegacyFee':
            const oldGasPriceWithMultiplier = mulByNumber(
                Hexadecimal.toBigInt(old.gasPrice),
                GAS_MULTIPLIER
            )
            const newPrice = Hexadecimal.toBigInt(newFee.gasPrice)

            if (oldGasPriceWithMultiplier >= newPrice) {
                return {
                    type: 'LegacyFee',
                    gasPrice: Hexadecimal.fromBigInt(oldGasPriceWithMultiplier),
                    forecastDuration: newFee.forecastDuration,
                    priceInDefaultCurrency: old.priceInDefaultCurrency
                        ? helpersMulByNumber(
                              old.priceInDefaultCurrency,
                              GAS_MULTIPLIER
                          )
                        : null,
                    priceInNativeCurrency: helpersMulByNumber(
                        old.priceInNativeCurrency,
                        GAS_MULTIPLIER
                    ),
                    maxPriceInDefaultCurrency: old.maxPriceInDefaultCurrency
                        ? helpersMulByNumber(
                              old.maxPriceInDefaultCurrency,
                              GAS_MULTIPLIER
                          )
                        : null,
                    maxPriceInNativeCurrency: helpersMulByNumber(
                        old.maxPriceInNativeCurrency,
                        GAS_MULTIPLIER
                    ),
                }
            }

            return newFee

        /* istanbul ignore next */
        default:
            return notReachable(old)
    }
}

const splitFee = (fee: Eip1559Fee): { base: bigint; priority: bigint } => {
    const max = Hexadecimal.toBigInt(
        fee.maxFeePerGas as Hexadecimal.Hexadecimal
    ) // TODO @resetko-zeal remove cast

    const priority = Hexadecimal.toBigInt(
        fee.maxPriorityFeePerGas as Hexadecimal.Hexadecimal
    ) // TODO @resetko-zeal remove cast

    const base = max - priority

    return { base, priority }
}

const compare = (oldFee: bigint, newFee: bigint, mul: number): bigint => {
    const oldWithM = mulByNumber(oldFee, mul)
    return oldWithM >= newFee ? oldWithM : newFee
}
