import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import * as Web3 from '@zeal/toolkit/Web3'

import { CryptoMoney, FiatMoney } from '@zeal/domains/Money'
import {
    Network,
    NetworkHexId,
    NetworkMap,
    NetworkRPCMap,
} from '@zeal/domains/Network'
import { EthSendTransaction } from '@zeal/domains/RPCRequest'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

export type CustomSelectedPreset = {
    type: 'Custom'
    fee:
        | (LegacyCustomPresetRequestFee & { nonce: number })
        | (Eip1559CustomPresetRequestFee & { nonce: number }) // TODO @resetko-zeal Not quite sure why we have this nonce difference. Maybe explain this by creating correctly named types?
}
export type CustomPresetRequestFee =
    | LegacyCustomPresetRequestFee
    | Eip1559CustomPresetRequestFee

export type Eip1559CustomPresetRequestFee = {
    type: 'Eip1559CustomPresetRequestFee'
    maxPriorityFee: string
    maxBaseFee: string
}

export type LegacyCustomPresetRequestFee = {
    type: 'LegacyCustomPresetRequestFee'
    gasPrice: Hexadecimal.Hexadecimal
}

export type FeePresetMap = Record<NetworkHexId, PredefinedPreset>

export type PredefinedPreset = { type: 'Slow' | 'Normal' | 'Fast' }

export type FeeForecastRequest = {
    network: Network
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    gasLimit: Hexadecimal.Hexadecimal
    gasEstimate: Hexadecimal.Hexadecimal
    address: Web3.address.Address
    sendTransactionRequest: EthSendTransaction

    selectedPreset: PredefinedPreset | CustomSelectedPreset
}

export type LegacyNetworkState = {
    type: 'LegacyNetworkState'
    durationMs: number
    minGasPrice: Hexadecimal.Hexadecimal
    maxGasPrice: Hexadecimal.Hexadecimal
}

export type Eip1559NetworkState = {
    type: 'Eip1559NetworkState'
    durationMs: number
    minPriorityFee: Hexadecimal.Hexadecimal
    maxPriorityFee: Hexadecimal.Hexadecimal
    baseFee: Hexadecimal.Hexadecimal
}

export type FeesForecastResponseLegacyFee = {
    type: 'FeesForecastResponseLegacyFee'
    slow: LegacyFee
    normal: LegacyFee
    fast: LegacyFee
    custom: LegacyFee | null
    nonce: number
    balanceInNativeCurrency: CryptoMoney
    networkState: LegacyNetworkState
}

export type FeesForecastResponseEip1559Fee = {
    type: 'FeesForecastResponseEip1559Fee'
    slow: Eip1559Fee
    normal: Eip1559Fee
    fast: Eip1559Fee
    custom: Eip1559Fee | null
    nonce: number
    networkState: Eip1559NetworkState
    balanceInNativeCurrency: CryptoMoney
}

export type FeesForecastResponseEip1559RecommendedFee = {
    type: 'FeesForecastResponseEip1559RecommendedFee'
    custom: Eip1559Fee | null
    fast: Eip1559Fee // TODO @resetko-zeal this is named fast just for compatibility, so we use fast as default for some silent flows. Though it's not far from true - we want this default thing to be fast so that trx is not considered slow
    nonce: number
    networkState: Eip1559NetworkState
    balanceInNativeCurrency: CryptoMoney
}

export type FeeForecastResponse =
    | FeesForecastResponseLegacyFee
    | FeesForecastResponseEip1559Fee
    | FeesForecastResponseEip1559RecommendedFee

export type EstimatedFee = LegacyFee | Eip1559Fee

export type ForecastDuration =
    | { type: 'OutsideOfForecast' }
    | { type: 'WithinForecast'; durationMs: number }

export type LegacyFee = {
    type: 'LegacyFee'
    gasPrice: Hexadecimal.Hexadecimal

    maxPriceInDefaultCurrency: FiatMoney | null
    maxPriceInNativeCurrency: CryptoMoney
    priceInDefaultCurrency: FiatMoney | null
    priceInNativeCurrency: CryptoMoney
    forecastDuration: ForecastDuration
}

export type Eip1559Fee = {
    type: 'Eip1559Fee'
    maxPriorityFeePerGas: string // TODO @resetko-zeal Hexadecimal.Hexadecimal
    maxFeePerGas: string // TODO @resetko-zeal Hexadecimal.Hexadecimal

    maxPriceInDefaultCurrency: FiatMoney | null
    maxPriceInNativeCurrency: CryptoMoney
    priceInDefaultCurrency: FiatMoney | null
    priceInNativeCurrency: CryptoMoney
    forecastDuration: ForecastDuration
}
