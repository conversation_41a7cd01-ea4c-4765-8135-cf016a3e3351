import { notReachable } from '@zeal/toolkit'
import { mulByNumber } from '@zeal/toolkit/BigInt'
import { ImperativeError } from '@zeal/toolkit/Error'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { generateRandomNumber } from '@zeal/toolkit/Number'
import { bigint, object } from '@zeal/toolkit/Result'
import { Address } from '@zeal/toolkit/Web3/address'

import { requestNativeBalance } from '@zeal/domains/Address/api/fetchNativeBalance'
import { CryptoCurrency, DefaultCurrency } from '@zeal/domains/Currency'
import { FXRate2 } from '@zeal/domains/FXRate'
import { fetchRate } from '@zeal/domains/FXRate/api/fetchRate'
import { applyNullableRate } from '@zeal/domains/FXRate/helpers/applyRate'
import { CryptoMoney } from '@zeal/domains/Money'
import {
    NetworkMap,
    NetworkRPCMap,
    PredefinedNetwork,
    TestNetwork,
} from '@zeal/domains/Network'
import { requestCurrentNonce } from '@zeal/domains/RPCRequest/api/fetchCurrentNonce'
import { fetchRPCBatch2 } from '@zeal/domains/RPCRequest/api/fetchRPCResponse'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import {
    Eip1559Fee,
    Eip1559NetworkState,
    FeeForecastRequest,
    FeesForecastResponseEip1559RecommendedFee,
} from '@zeal/domains/Transactions/domains/FeeForecast'

import { BLOCKS_UNTIL_INCLUSION } from '../constants'

type EIP1559Network = PredefinedNetwork | TestNetwork

const FEE_HISTORY_NUM_BLOCKS = 20

const BASE_FEE_BUFFER = 2
const PRIORITY_FEE_BUFFER = 1.5

export const fetchEip1559FeeForecast2 = async ({
    network,
    networkRPCMap,
    address,
    gasLimit,
    gasEstimate,
    selectedPreset,
    defaultCurrencyConfig,
    networkMap,
    signal,
}: {
    address: Address
    network: EIP1559Network
    networkRPCMap: NetworkRPCMap
    gasLimit: string
    gasEstimate: string
    selectedPreset: FeeForecastRequest['selectedPreset']
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    signal?: AbortSignal
}): Promise<FeesForecastResponseEip1559RecommendedFee> => {
    const nativeCurrency = network.nativeCurrency

    const [
        rate,
        [balanceInNativeCurrency, nonce, maxPriorityFee, baseFeePerGas],
    ] = await Promise.all([
        fetchRate({
            cryptoCurrency: nativeCurrency,
            defaultCurrencyConfig,
            networkMap,
            networkRPCMap,
            signal,
        }),
        fetchRPCBatch2(
            [
                requestNativeBalance({ address, block: 'latest', network }),
                requestCurrentNonce({ address }),
                {
                    request: {
                        id: generateRandomNumber(),
                        jsonrpc: '2.0',
                        method: 'eth_maxPriorityFeePerGas',
                        params: [],
                    },
                    parser: (input: unknown) =>
                        bigint(input).getSuccessResultOrThrow(
                            'failed to parse eth_maxPriorityFeePerGas'
                        ),
                },
                {
                    request: {
                        id: generateRandomNumber(),
                        jsonrpc: '2.0',
                        method: 'eth_getBlockByNumber',
                        params: ['latest', false],
                    },
                    parser: (input: unknown) =>
                        object(input)
                            .andThen((obj) => bigint(obj.baseFeePerGas))
                            .getSuccessResultOrThrow(
                                'Failed to parse block by number'
                            ),
                },
            ],
            { network, networkRPCMap, signal }
        ),
    ])

    const networkState: Eip1559NetworkState = {
        type: 'Eip1559NetworkState',
        maxPriorityFee: Hexadecimal.fromBigInt(maxPriorityFee),
        minPriorityFee: Hexadecimal.fromBigInt(maxPriorityFee),
        baseFee: Hexadecimal.fromBigInt(baseFeePerGas),
        durationMs: FEE_HISTORY_NUM_BLOCKS * network.blockTimeMS,
    }

    const fast = getRecommendedFee({
        rate,
        gasLimit,
        gasEstimate,
        nativeCurrency,
        baseFeePerGas,
        network,
        maxPriorityFee,
    })

    switch (selectedPreset.type) {
        case 'Slow':
        case 'Normal':
        case 'Fast':
            return {
                type: 'FeesForecastResponseEip1559RecommendedFee',
                networkState,
                balanceInNativeCurrency,
                nonce,
                custom: null,
                fast,
            }

        case 'Custom':
            switch (selectedPreset.fee.type) {
                case 'LegacyCustomPresetRequestFee':
                    throw new ImperativeError(
                        'Impossible state, got custom legacy preset for eip1559 trx type network'
                    )
                case 'Eip1559CustomPresetRequestFee':
                    const maxFeePerGas =
                        BigInt(selectedPreset.fee.maxBaseFee) +
                        BigInt(selectedPreset.fee.maxPriorityFee)

                    const priceInNativeCurrency: CryptoMoney = {
                        amount: maxFeePerGas * BigInt(gasEstimate),
                        currency: nativeCurrency,
                    }

                    const maxPriceInNativeCurrency: CryptoMoney = {
                        amount: maxFeePerGas * BigInt(gasLimit),
                        currency: nativeCurrency,
                    }

                    return {
                        type: 'FeesForecastResponseEip1559RecommendedFee',
                        networkState,
                        balanceInNativeCurrency,
                        nonce: selectedPreset.fee.nonce,
                        custom: {
                            type: 'Eip1559Fee',
                            forecastDuration: {
                                type: 'OutsideOfForecast',
                            },
                            maxFeePerGas: Hexadecimal.fromBigInt(maxFeePerGas),
                            maxPriorityFeePerGas:
                                selectedPreset.fee.maxPriorityFee,
                            priceInNativeCurrency,
                            priceInDefaultCurrency: applyNullableRate({
                                baseAmount: priceInNativeCurrency,
                                rate,
                            }),

                            maxPriceInNativeCurrency,
                            maxPriceInDefaultCurrency: applyNullableRate({
                                baseAmount: maxPriceInNativeCurrency,
                                rate,
                            }),
                        },
                        fast,
                    }
                /* istanbul ignore next */
                default:
                    return notReachable(selectedPreset.fee)
            }
        /* istanbul ignore next */
        default:
            return notReachable(selectedPreset)
    }
}

const getRecommendedFee = ({
    baseFeePerGas,
    nativeCurrency,
    gasEstimate,
    gasLimit,
    network,
    maxPriorityFee,
    rate,
}: {
    baseFeePerGas: bigint
    maxPriorityFee: bigint
    nativeCurrency: CryptoCurrency
    gasLimit: string
    gasEstimate: string
    network: EIP1559Network
    rate: FXRate2<CryptoCurrency, DefaultCurrency> | null
}): Eip1559Fee => {
    const baseFeePerGasBuffered = mulByNumber(baseFeePerGas, BASE_FEE_BUFFER)
    const priorityFeeBuffered = mulByNumber(maxPriorityFee, PRIORITY_FEE_BUFFER)

    const maxFeePerGas = baseFeePerGasBuffered + priorityFeeBuffered

    const priceInNativeCurrency: CryptoMoney = {
        amount: maxFeePerGas * BigInt(gasEstimate),
        currency: nativeCurrency,
    }

    const maxPriceInNativeCurrency: CryptoMoney = {
        amount: maxFeePerGas * BigInt(gasLimit),
        currency: nativeCurrency,
    }

    return {
        type: 'Eip1559Fee',
        maxFeePerGas: Hexadecimal.fromBigInt(maxFeePerGas),
        maxPriorityFeePerGas: Hexadecimal.fromBigInt(priorityFeeBuffered),

        priceInNativeCurrency,
        priceInDefaultCurrency: applyNullableRate({
            baseAmount: priceInNativeCurrency,
            rate,
        }),

        maxPriceInNativeCurrency,
        maxPriceInDefaultCurrency: applyNullableRate({
            baseAmount: maxPriceInNativeCurrency,
            rate,
        }),
        forecastDuration: {
            type: 'WithinForecast',
            durationMs: BLOCKS_UNTIL_INCLUSION * network.blockTimeMS,
        },
    }
}
