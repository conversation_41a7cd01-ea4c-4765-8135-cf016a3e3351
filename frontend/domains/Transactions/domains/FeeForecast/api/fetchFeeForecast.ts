import { notReachable } from '@zeal/toolkit'
import * as Web3 from '@zeal/toolkit/Web3'
import { staticFromString } from '@zeal/toolkit/Web3/address'

import { isEOARecommendedGasEnabled } from '@zeal/domains/ABTest'
import { OP_STACK_GAS_PRICE_ORACLE_ADDRESS } from '@zeal/domains/Address/constants'
import { NetworkHexId } from '@zeal/domains/Network'
import { parseNetworkHexId } from '@zeal/domains/Network/helpers/parse'
import {
    FeeForecastRequest,
    FeeForecastResponse,
} from '@zeal/domains/Transactions/domains/FeeForecast'
import { fetchEip1559FeeForecast } from '@zeal/domains/Transactions/domains/FeeForecast/api/fetchEip1559FeeForecast'
import { fetchL2FeeForecast } from '@zeal/domains/Transactions/domains/FeeForecast/api/fetchL2FeeForecast'
import { fetchLegacyFeeForecast } from '@zeal/domains/Transactions/domains/FeeForecast/api/fetchLegacyFeeForecast'
import { fetchLegacyL1DataFee } from '@zeal/domains/Transactions/domains/FeeForecast/api/fetchLegacyL1DataFee'

import { fetchEip1559FeeForecast2 } from './fetchEip1559FeeForecast2'

const PREDEFINED_OP_STACK_CUSTOM_NETWORKS: {
    networkHexId: NetworkHexId
    gasOracleAddress: Web3.address.Address
}[] = [
    {
        // Scroll
        networkHexId: parseNetworkHexId('0x82750').getSuccessResultOrThrow(
            'failed to parse network hex ID for Scroll network'
        ),
        gasOracleAddress: staticFromString(
            '0x5300000000000000000000000000000000000002'
        ),
    },
    {
        // Zora
        networkHexId: parseNetworkHexId('0x76adf1').getSuccessResultOrThrow(
            'failed to parse network hex ID for Zora network'
        ),
        gasOracleAddress: OP_STACK_GAS_PRICE_ORACLE_ADDRESS,
    },
    {
        // Blast Sepolia
        networkHexId: parseNetworkHexId('0xa0c71fd').getSuccessResultOrThrow(
            'failed to parse network hex ID for Blast Sepolia'
        ),
        gasOracleAddress: OP_STACK_GAS_PRICE_ORACLE_ADDRESS,
    },
]

export const fetchFeeForecast = async ({
    signal,
    address,
    network,
    networkMap,
    networkRPCMap,
    selectedPreset,
    gasLimit,
    gasEstimate,
    sendTransactionRequest,
    defaultCurrencyConfig,
}: FeeForecastRequest & {
    signal: AbortSignal
}): Promise<FeeForecastResponse> => {
    switch (network.type) {
        case 'predefined':
        case 'testnet':
            switch (network.name) {
                case 'Ethereum':
                case 'EthereumSepolia':
                case 'Polygon':
                case 'Avalanche':
                case 'AvalancheFuji':
                case 'Gnosis':
                case 'Linea':
                case 'Sonic': {
                    if (isEOARecommendedGasEnabled()) {
                        return fetchEip1559FeeForecast2({
                            network,
                            address,
                            gasLimit,
                            gasEstimate,
                            selectedPreset,
                            defaultCurrencyConfig,
                            networkMap,
                            networkRPCMap,
                            signal,
                        })
                    } else {
                        return fetchEip1559FeeForecast({
                            network,
                            address,
                            gasLimit,
                            gasEstimate,
                            selectedPreset,
                            defaultCurrencyConfig,
                            networkMap,
                            networkRPCMap,
                            signal,
                        })
                    }
                }

                case 'Manta':
                case 'BSC':
                case 'BscTestnet':
                case 'Fantom':
                case 'FantomTestnet':
                case 'Celo':
                case 'Aurora':
                case 'zkSync':
                case 'Cronos':
                case 'PolygonZkevm':
                    return fetchLegacyFeeForecast({
                        network,
                        address,
                        gasLimit,
                        gasEstimate,
                        selectedPreset,
                        defaultCurrencyConfig,
                        networkMap,
                        networkRPCMap,
                        signal,
                    })
                case 'Arbitrum':
                    return fetchL2FeeForecast({
                        l1DataFee: 0n,
                        network,
                        address,
                        gasLimit,
                        gasEstimate,
                        defaultCurrencyConfig,
                        networkMap,
                        networkRPCMap,
                        signal,
                    })
                case 'Optimism':
                case 'Base':
                case 'Blast':
                case 'OPBNB':
                case 'Mantle':
                    const l1DataFee = await fetchLegacyL1DataFee({
                        network,
                        networkRPCMap,
                        transaction: sendTransactionRequest,
                        gasOracleAddress: OP_STACK_GAS_PRICE_ORACLE_ADDRESS,
                    })

                    return fetchL2FeeForecast({
                        l1DataFee,
                        network,
                        address,
                        gasLimit,
                        gasEstimate,
                        defaultCurrencyConfig,
                        networkMap,
                        networkRPCMap,
                        signal,
                    })
                /* istanbul ignore next */
                default:
                    return notReachable(network)
            }
        case 'custom':
            const opStackCustomNetwork =
                PREDEFINED_OP_STACK_CUSTOM_NETWORKS.find(
                    (n) => n.networkHexId === network.hexChainId
                )

            if (opStackCustomNetwork) {
                const l1DataFee = await fetchLegacyL1DataFee({
                    network,
                    networkRPCMap,
                    transaction: sendTransactionRequest,
                    gasOracleAddress: opStackCustomNetwork.gasOracleAddress,
                })

                return fetchL2FeeForecast({
                    l1DataFee,
                    network,
                    address,
                    gasLimit,
                    gasEstimate,
                    defaultCurrencyConfig,
                    networkMap,
                    networkRPCMap,
                    signal,
                })
            }

            return fetchLegacyFeeForecast({
                network,
                address,
                gasLimit,
                gasEstimate,
                selectedPreset,
                defaultCurrencyConfig,
                networkMap,
                networkRPCMap,
                signal,
            })
        /* istanbul ignore next */
        default:
            return notReachable(network)
    }
}
