import { PredefinedPreset } from '@zeal/domains/Transactions/domains/FeeForecast'

export const CANCEL_GAS_AMOUNT = '0x5208'

export const ERC_20_TRANSFER_GAS = 75_000n
export const BUNGEE_NATIVE_TRANSFER_GAS = 200_000n
export const EARN_WITHDRAWAL_GAS = 180_000n
export const AAVE_WITHDRAWAL_GAS = 300_000n
export const ERC20_APPROVAL_GAS = 55_000n

export const DEFAULT_FEE_PRESET: PredefinedPreset = { type: 'Normal' }

export const DEFAULT_CANCEL_FEE_PRESET: PredefinedPreset = {
    type: 'Fast',
}

export const DEFAULT_SPEEDUP_FEE_PRESET: PredefinedPreset = {
    type: 'Fast',
}

export const BLOCKS_UNTIL_INCLUSION_MAP: Record<
    PredefinedPreset['type'],
    number
> = {
    Slow: 6,
    Normal: 3,
    Fast: 2,
}

export const BLOCKS_UNTIL_INCLUSION = 2

export const OP_STACK_GAS_PRICE_ORACLE_ABI = [
    {
        inputs: [
            {
                internalType: 'bytes',
                name: '_data',
                type: 'bytes',
            },
        ],
        name: 'getL1Fee',
        outputs: [
            {
                internalType: 'uint256',
                name: '',
                type: 'uint256',
            },
        ],
        stateMutability: 'view' as const,
        type: 'function' as const,
    },
] as const
