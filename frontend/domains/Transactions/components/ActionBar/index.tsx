import { useIntl } from 'react-intl'

import { ActionBar as UIActionBar } from '@zeal/uikit/ActionBar'
import { Column } from '@zeal/uikit/Column'
import { CloseCross } from '@zeal/uikit/Icon/CloseCross'
import { IconButton } from '@zeal/uikit/IconButton'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'

import { Account } from '@zeal/domains/Account'
import { ActionBarAccountIndicator } from '@zeal/domains/Account/components/ActionBarAccountIndicator'
import { ActionSource } from '@zeal/domains/Main'
import { Network } from '@zeal/domains/Network'
import { Avatar } from '@zeal/domains/Network/components/Avatar'

type Props = {
    title: React.ReactNode | null
    account: Account
    actionSourceType: ActionSource['type']
    network: Network | null
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'on_minimize_click' }

/**
 * @deprecated Please avoid using this component in internal flows
 */
export const ActionBar = ({
    account,
    actionSourceType,
    title,
    network,
    onMsg,
}: Props) => {
    const { formatMessage } = useIntl()
    return (
        <Column spacing={0}>
            <UIActionBar
                left={<ActionBarAccountIndicator account={account} />}
                size="small"
                right={(() => {
                    switch (actionSourceType) {
                        case 'internal_sign':
                        case 'internal':
                            return null
                        case 'external':
                            return (
                                <IconButton
                                    variant="on_light"
                                    onClick={() => {
                                        onMsg({
                                            type: 'on_minimize_click',
                                        })
                                    }}
                                    aria-label={formatMessage({
                                        id: 'action.minimize',
                                        defaultMessage: 'Minimise',
                                    })}
                                >
                                    {({ color }) => (
                                        <CloseCross size={24} color={color} />
                                    )}
                                </IconButton>
                            )
                        default:
                            return notReachable(actionSourceType)
                    }
                })()}
            />

            {title === null && network === null ? null : (
                <UIActionBar
                    left={
                        title === null ? null : (
                            <Text
                                variant="title3"
                                weight="medium"
                                color="textPrimary"
                            >
                                {title}
                            </Text>
                        )
                    }
                    right={
                        network === null ? null : (
                            <Avatar
                                currentNetwork={{
                                    type: 'specific_network',
                                    network,
                                }}
                                size={24}
                            />
                        )
                    }
                />
            )}
        </Column>
    )
}
