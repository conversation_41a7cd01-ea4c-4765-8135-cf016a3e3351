import { notReachable } from '@zeal/toolkit'

import { fetchRate } from '@zeal/domains/FXRate/api/fetchRate'
import { applyRate2 } from '@zeal/domains/FXRate/helpers/applyRate'
import { CryptoMoney, FiatMoney } from '@zeal/domains/Money'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { GasInfo } from '@zeal/domains/TransactionRequest/domains/SubmitedTransaction'

import { getFee } from '../helpers/getFee'

export const fetchFinalFee = async ({
    network,
    gasInfo,
    networkMap,
    networkRPCMap,
    defaultCurrencyConfig,
    signal,
}: {
    gasInfo: GasInfo
    network: Network
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    signal: AbortSignal
}): Promise<{
    fee: CryptoMoney
    priceInDefaultCurrency: FiatMoney | null
}> => {
    switch (network.type) {
        case 'predefined': {
            const nativeCurrency = network.nativeCurrency

            const rate = await fetchRate({
                cryptoCurrency: nativeCurrency,
                networkMap,
                networkRPCMap,
                defaultCurrencyConfig,
                signal,
            })

            const fee = getFee({ currency: nativeCurrency, gasInfo })

            if (!rate) {
                return {
                    fee,
                    priceInDefaultCurrency: null,
                }
            }

            const priceInDefaultCurrency = applyRate2({ baseAmount: fee, rate })

            return {
                priceInDefaultCurrency,
                fee,
            }
        }
        case 'custom':
        case 'testnet': {
            const currency = network.nativeCurrency

            return {
                fee: getFee({
                    gasInfo,
                    currency,
                }),
                priceInDefaultCurrency: null,
            }
        }

        /* istanbul ignore next */
        default:
            return notReachable(network)
    }
}
