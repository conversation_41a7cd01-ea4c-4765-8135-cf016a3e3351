import { useEffect } from 'react'
import { FormattedMessage } from 'react-intl'

import { Avatar } from '@zeal/uikit/Avatar'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { Header } from '@zeal/uikit/Header'
import { BoldDangerTriangle } from '@zeal/uikit/Icon/BoldDangerTriangle'
import { Popup } from '@zeal/uikit/Popup'

import { useLiveRef } from '@zeal/toolkit/React'

import { PasskeyScreenLockMissing } from '@zeal/domains/Error/domains/Passkey'
import { captureAppError } from '@zeal/domains/Error/helpers/captureAppError'
import { PasskeyOperationCouldNotBePerformedEvent } from '@zeal/domains/UserEvents'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

type Props = {
    error: PasskeyScreenLockMissing
    installationId: string
    location: PasskeyOperationCouldNotBePerformedEvent['location']
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'close' }

export const ScreenLockMissing = ({
    onMsg,
    error,
    installationId,
    location,
}: Props) => {
    const liveError = useLiveRef(error)

    useEffect(() => {
        captureAppError(liveError.current, {
            source: 'manually_captured',
        })
        postUserEvent({
            type: 'PasskeyOperationCouldNotBePerformedEvent',
            installationId,
            location,
            error: liveError.current.type,
        })
    }, [liveError, liveError.current.type, installationId, location])

    return (
        <Popup.Layout onMsg={onMsg} background="surfaceDefault">
            <Column spacing={24}>
                <Header
                    icon={({ size }) => (
                        <Avatar
                            size={72}
                            variant="round"
                            backgroundColor="backgroundLight"
                        >
                            <BoldDangerTriangle
                                size={size}
                                color="iconStatusWarning"
                            />
                        </Avatar>
                    )}
                    title={
                        <FormattedMessage
                            id="screen-lock-missing.modal.title"
                            defaultMessage="Screen lock missing"
                        />
                    }
                    subtitle={
                        <FormattedMessage
                            id="screen-lock-missing.modal.subtitle"
                            defaultMessage="Your device requires a screen lock to use Passkeys. Please set up a screen lock and try again."
                        />
                    }
                />
                <Popup.Actions>
                    <Button
                        variant="primary"
                        onClick={() => onMsg({ type: 'close' })}
                        size="regular"
                    >
                        <FormattedMessage
                            id="screen-lock-missing.modal.close"
                            defaultMessage="Close"
                        />
                    </Button>
                </Popup.Actions>
            </Column>
        </Popup.Layout>
    )
}
