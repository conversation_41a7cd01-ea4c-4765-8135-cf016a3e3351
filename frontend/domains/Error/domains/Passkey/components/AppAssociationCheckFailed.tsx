import { useEffect } from 'react'
import { FormattedMessage } from 'react-intl'

import { Avatar } from '@zeal/uikit/Avatar'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { Header } from '@zeal/uikit/Header'
import { BoldDangerTriangle } from '@zeal/uikit/Icon/BoldDangerTriangle'
import { Popup } from '@zeal/uikit/Popup'

import { notReachable } from '@zeal/toolkit'
import { useLiveRef } from '@zeal/toolkit/React'

import {
    PasskeyAndroidCannotValidateIncomingRequest,
    PasskeyAppNotAssociatedWithDomain,
} from '@zeal/domains/Error/domains/Passkey'
import { captureAppError } from '@zeal/domains/Error/helpers/captureAppError'
import { Network } from '@zeal/domains/Network'
import { PasskeyAppAssociationCheckFailedEvent } from '@zeal/domains/UserEvents'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

type Props = {
    error:
        | PasskeyAppNotAssociatedWithDomain
        | PasskeyAndroidCannotValidateIncomingRequest
    variant: 'passkey_creation' | 'passkey_signing'
    location: PasskeyAppAssociationCheckFailedEvent['location']
    installationId: string
    network: Network
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'on_try_again_clicked' } | { type: 'close' }

export const AppAssociationCheckFailed = ({
    variant,
    onMsg,
    error,
    location,
    installationId,
    network,
}: Props) => {
    const liveError = useLiveRef(error)

    // We hold actual error in ref, but if type somehow changes - we report
    // This will prevent report spamming, since we usually do parsing in render
    useEffect(() => {
        captureAppError(liveError.current, { source: 'manually_captured' })
        postUserEvent({
            type: 'PasskeyAppAssociationCheckFailedEvent',
            installationId,
            location,
            network: network.name,
        })
    }, [
        liveError,
        liveError.current.type,
        location,
        installationId,
        network.name,
    ])

    return (
        <Popup.Layout onMsg={onMsg} background="surfaceDefault">
            <Column spacing={24}>
                <Header
                    icon={({ size }) => (
                        <Avatar
                            size={72}
                            variant="round"
                            backgroundColor="backgroundLight"
                        >
                            <BoldDangerTriangle
                                size={size}
                                color="iconStatusWarning"
                            />
                        </Avatar>
                    )}
                    title={(() => {
                        switch (variant) {
                            case 'passkey_creation':
                                return (
                                    <FormattedMessage
                                        id="app-association-check-failed.modal.title.creation"
                                        defaultMessage="Your device failed to create a passkey"
                                    />
                                )
                            case 'passkey_signing':
                                return (
                                    <FormattedMessage
                                        id="app-association-check-failed.modal.title.signing"
                                        defaultMessage="Your device failed to load Passkeys"
                                    />
                                )
                            /* istanbul ignore next */
                            default:
                                return notReachable(variant)
                        }
                    })()}
                    subtitle={(() => {
                        switch (variant) {
                            case 'passkey_creation':
                                return (
                                    <FormattedMessage
                                        id="app-association-check-failed.modal.subtitle.creation"
                                        defaultMessage="Please try again. Connectivity issues are causing delays in Passkey creation. If the issue persists then restart Zeal and try one more time."
                                    />
                                )
                            case 'passkey_signing':
                                return (
                                    <FormattedMessage
                                        id="app-association-check-failed.modal.subtitle"
                                        defaultMessage="Please try again. Connectivity issues are causing delays when fetching your Passkeys. If the issue persists then restart Zeal and try one more time."
                                    />
                                )
                            /* istanbul ignore next */
                            default:
                                return notReachable(variant)
                        }
                    })()}
                />
                <Popup.Actions>
                    <Button
                        variant="primary"
                        onClick={() => onMsg({ type: 'on_try_again_clicked' })}
                        size="regular"
                    >
                        <FormattedMessage
                            id="app-association-check-failed.modal.cta"
                            defaultMessage="Try again"
                        />
                    </Button>
                </Popup.Actions>
            </Column>
        </Popup.Layout>
    )
}
