import { useEffect } from 'react'
import { FormattedMessage } from 'react-intl'

import { Avatar } from '@zeal/uikit/Avatar'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { Header } from '@zeal/uikit/Header'
import { BoldDangerTriangle } from '@zeal/uikit/Icon/BoldDangerTriangle'
import { Popup } from '@zeal/uikit/Popup'
import { TextButton } from '@zeal/uikit/TextButton'

import { useLiveRef } from '@zeal/toolkit/React'
import { openExternalURL } from '@zeal/toolkit/Window'

import {
    PasskeyAndroidFidoApiNotAvailable,
    PasskeyAndroidFidoApiNotSupported,
    PasskeyAndroidNoCreateOptionsAvailable,
    PasskeyAndroidResidentKeyCreationNotSupported,
} from '@zeal/domains/Error/domains/Passkey'
import { captureAppError } from '@zeal/domains/Error/helpers/captureAppError'
import { DISCORD_URL } from '@zeal/domains/Main/constants'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

type Props = {
    error:
        | PasskeyAndroidNoCreateOptionsAvailable
        | PasskeyAndroidResidentKeyCreationNotSupported
        | PasskeyAndroidFidoApiNotAvailable
        | PasskeyAndroidFidoApiNotSupported
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'close' }

export const PasskeyCreationNotPossible = ({
    onMsg,
    error,
    installationId,
}: Props) => {
    const liveError = useLiveRef(error)

    useEffect(() => {
        captureAppError(liveError.current, { source: 'manually_captured' })
        postUserEvent({
            type: 'PasskeyOperationCouldNotBePerformedEvent',
            installationId,
            location: 'wallet_creation',
            error: liveError.current.type,
        })
    }, [liveError, liveError.current.type, installationId])
    return (
        <Popup.Layout onMsg={onMsg} background="surfaceDefault">
            <Column spacing={24}>
                <Header
                    icon={({ size }) => (
                        <Avatar
                            size={72}
                            variant="round"
                            backgroundColor="backgroundLight"
                        >
                            <BoldDangerTriangle
                                size={size}
                                color="iconStatusWarning"
                            />
                        </Avatar>
                    )}
                    title={
                        <FormattedMessage
                            id="passkey-creation-not-possible.modal.title"
                            defaultMessage="Unable to create passkey"
                        />
                    }
                    subtitle={
                        <FormattedMessage
                            id="passkey-creation-not-possible.modal.subtitle"
                            defaultMessage="We were unable to create a Passkey for your wallet. Please make sure your device supports Passkeys and try again. <link>Contact support</link> if the issue persists."
                            values={{
                                link: (msg) => (
                                    <TextButton
                                        onClick={() =>
                                            openExternalURL(DISCORD_URL)
                                        }
                                    >
                                        {msg}
                                    </TextButton>
                                ),
                            }}
                        />
                    }
                />
                <Popup.Actions>
                    <Button
                        variant="primary"
                        onClick={() => onMsg({ type: 'close' })}
                        size="regular"
                    >
                        <FormattedMessage
                            id="passkey-creation-not-possible.modal.close"
                            defaultMessage="Close"
                        />
                    </Button>
                </Popup.Actions>
            </Column>
        </Popup.Layout>
    )
}
