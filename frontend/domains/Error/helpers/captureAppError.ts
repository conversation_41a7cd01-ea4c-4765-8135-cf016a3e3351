import { notReachable } from '@zeal/toolkit'
import { getEnvironment } from '@zeal/toolkit/Environment/getEnvironment'
import * as reporting from '@zeal/toolkit/Error/reporting'
import { stringify } from '@zeal/toolkit/JSON'
import { scrubSensitiveFields } from '@zeal/toolkit/Object'
import { replaceUUID } from '@zeal/toolkit/replaceUUID'
import { string } from '@zeal/toolkit/Result'

import { AppError, Source } from '../AppError'

type UIReportingParams = {
    source: Source
    extra?: Record<string, unknown>
}

type BackendReportingParams = {
    source: Source
    extra?: Record<string, unknown>
    requestId?: string
}

/**
 * We need to strip UUIDs and non-UUID specific params from URL to improve tagging in sentry and reduce duplication
 */
const cleanupUrl = (url: string) => {
    const noQueryString = url.split('?')[0]
    const noUUID = replaceUUID(noQueryString, 'uuid')
    const noAddress = noUUID.replace(/0x[0-9a-fA-F]{40}/gim, ':address')
    const noSpecificParams = noAddress
        .replace(
            /\/wallet\/rate\/default\/[a-zA-Z]+\/0x[0-9a-fA-F]+\//gim,
            '/wallet/rate/default/:network/:address/'
        )
        .replace(
            /\/wallet\/transaction\/0x[0-9a-fA-F]+\/result/gim,
            '/wallet/transaction/:trx_hash/result'
        )
        .replace(/\/api\/v1\/order\/[a-z0-9]+/gim, '/api/v1/order/:id')

    return noSpecificParams
}

// TODO @resetko-zeal move this to separate helper if needed
export const getTagsAndExtra = ({
    error,
}: {
    error: AppError
}): {
    tags: Record<string, string | number | null>
    extra: Record<string, unknown>
} => {
    const tags = {
        errorType: error.type,
    }

    switch (error.type) {
        case 'biometric_prompt_auth_failed':
        case 'biometric_prompt_cancelled':
        case 'decrypt_incorrect_password':
        case 'encrypted_object_invalid_format':
        case 'failed_to_fetch_google_auth_token':
        case 'google_api_error':
        case 'hardware_wallet_failed_to_open_device':
        case 'invalid_encrypted_file_format':
        case 'ios_could_not_communicate_with_helper_application':
        case 'ledger_blind_sign_not_enabled_or_running_non_eth_app':
        case 'ledger_is_locked':
        case 'ledger_not_running_any_app':
        case 'ledger_running_non_eth_app':
        case 'monerium_error_address_re_link_required':
        case 'monerium_error_duplicate_order':
        case 'passkey_android_cannot_validate_incoming_request':
        case 'passkey_android_creation_interrupted':
        case 'passkey_android_failed_to_launch_selector':
        case 'passkey_android_fido_api_not_available':
        case 'passkey_android_fido_api_not_supported':
        case 'passkey_android_no_create_options_available':
        case 'passkey_android_no_credential_available':
        case 'passkey_android_provider_configuration_error':
        case 'passkey_android_resident_key_creation_not_supported':
        case 'passkey_android_timeout_error':
        case 'passkey_android_unable_to_get_sync_account':
        case 'passkey_app_not_associated_with_domain':
        case 'passkey_google_account_missing':
        case 'passkey_not_supported_in_mobile_browser_error':
        case 'passkey_operation_cancelled':
        case 'passkey_screen_lock_missing':
        case 'secure_store_keychain_decryption_error':
        case 'swaps_io_not_enough_balance_error':
        case 'swaps_io_swap_below_minimum':
        case 'trezor_action_cancelled':
        case 'trezor_connection_already_initialized':
        case 'trezor_device_used_elsewhere':
        case 'trezor_method_cancelled':
        case 'trezor_permissions_not_granted':
        case 'trezor_pin_cancelled':
        case 'trezor_popup_closed':
        case 'unblock_account_number_and_sort_code_mismatch':
        case 'unblock_can_not_change_details_after_kyc':
        case 'unblock_hard_kyc_failure':
        case 'unblock_invalid_faster_payment_configuration':
        case 'unblock_invalid_iban':
        case 'unblock_invalid_otp':
        case 'unblock_login_user_did_not_exists':
        case 'unblock_maximum_otp_attempts_exceeded':
        case 'unblock_nonce_already_in_use':
        case 'unblock_otp_expired':
        case 'unblock_session_expired':
        case 'unblock_unsupported_country':
        case 'unblock_user_associated_with_other_merchant':
        case 'unblock_user_with_address_already_exists':
        case 'unblock_user_with_such_email_already_exists':
        case 'unblock_invalid_postal_code':
        case 'user_trx_denied_by_user':
        case 'wagmi_add_ethereum_chain_not_supported':
        case 'wagmi_switch_chain_chain_id_not_supported':
        case 'wallet_connect_add_ethereum_chain_missing_or_invalid':
        case 'wallet_connect_proposal_expired':
        case 'wallet_connect_proposal_no_more_available':
        case 'zeal_a_rewards_already_claimed_error':
        case 'breward_already_claimed':
        case 'breward_cannotbe_claimed':
            return { tags, extra: {} }

        case 'bungee_order_failed_error':
            return {
                tags,
                extra: {
                    orderHash: error.orderHash,
                    transactionEventSource:
                        error.actionSource.transactionEventSource,
                },
            }

        case 'unblock_invalid_date_of_birth':
            return {
                tags,
                extra: {
                    responseHeaders: error.responseHeaders,
                    requestBody: error.requestBody,
                },
            }

        // TODO @resetko-zeal this is not reported by manual capture but repotred by other components
        case 'signal_aborted_for_unknown_reason':
            return { tags, extra: {} }

        case 'unexpected_failure':
            return {
                tags,
                extra: {
                    reason: stringifyUnknownData(error.reason),
                },
            }

        case 'unknown_error':
            return {
                tags,
                extra: {
                    extra: {
                        stringifyError: stringifyUnknownData(
                            error.originalError
                        ),
                        message: error.message,
                    },
                },
            }

        case 'rpc_request_parse_error':
            return {
                tags: {
                    ...tags,
                    rpcMethod:
                        string(error.rpcMethod).getSuccessResult() || null,
                },
                extra: {
                    reason: stringifyUnknownData(error.reason),
                },
            }

        case 'gnosis_pay_address_linked_to_another_monerium_profile':
        case 'gnosis_pay_already_accepted_terms_error':
        case 'gnosis_pay_email_or_account_already_registered_error':
        case 'gnosis_pay_invalid_phone_otp_error':
        case 'gnosis_pay_no_active_cards_found':
        case 'gnosis_pay_public_key_not_matching_error':
        case 'gnosis_pay_readonly_signer_is_already_in_use':
        case 'gnosis_pay_there_is_already_pending_card_order':
        case 'gnosis_pay_user_already_has_monerium_account':
        case 'gnosis_pay_user_cannot_create_card_re_kyc_needed':
        case 'gnosis_pay_card_blocked_cant_be_activated':
        case 'gnosis_pay_user_doesnt_meet_risk_score_criteria':
        case 'gnosis_pay_invalid_signup_otp_error':
            return { tags, extra: {} }

        case 'gnosis_pay_is_not_available_in_this_country':
        case 'gnosis_pay_card_unsupported_countries':
        case 'gnosis_pay_user_doesnt_have_sof_answered':
        case 'gnosis_pay_failed_to_verify_signature':
        case 'gnosis_pay_card_delay_relay_not_empty':
        case 'gnosis_pay_name_too_long':
            return {
                tags,
                extra: {
                    responseHeaders: error.responseHeaders,
                    requestBody: error.requestBody,
                },
            }

        case 'gnosis_pay_user_is_not_signed_up':
            return {
                tags,
                extra: {
                    responseHeaders: error.responseHeaders,
                },
            }

        case 'http_error':
            return {
                tags: {
                    ...tags,
                    url: cleanupUrl(error.url),
                    method: error.method,
                    status: error.status,
                    pathname: cleanupUrl(error.urlPathname),
                    origin: cleanupUrl(error.urlOrigin),
                    requestId: error.responseHeaders['request-id'] || null,
                },
                extra: {
                    responseHeaders: error.responseHeaders,
                    responseBody: error.responseBody,
                    urlQuery: error.urlQuery,
                    requestBody: error.requestBody,
                },
            }

        // TODO @resetko-zeal this is not reported by manual capture but repotred by other components
        case 'connectivity_error':
            return error.requestInfo
                ? {
                      tags: {
                          ...tags,
                          url: cleanupUrl(error.requestInfo.url),
                          method: error.requestInfo.method,
                          pathname: error.requestInfo.urlPathname
                              ? cleanupUrl(error.requestInfo.urlPathname)
                              : null,
                          origin: error.requestInfo.urlOrigin
                              ? cleanupUrl(error.requestInfo.urlOrigin)
                              : null,
                      },
                      extra: {
                          urlQuery: error.requestInfo.urlQuery,
                          requestBody: error.requestInfo.requestBody,
                          message: error.message,
                          stringifyError: stringifyUnknownData(error.error),
                      },
                  }
                : {
                      tags,
                      extra: {
                          message: error.message,
                          stringifyError: stringifyUnknownData(error.error),
                      },
                  }

        case 'imperative_error':
            return {
                tags,
                extra: {
                    message: error.message,
                    ...error.extra,
                },
            }

        case 'passkey_signer_not_found_error':
            return {
                tags,
                extra: {
                    recoveryId: error.recoveryId,
                },
            }

        case 'rpc_error_already_known':
        case 'rpc_error_block_gas_limit_exceeded':
        case 'rpc_error_block_state_unavailable':
        case 'rpc_error_cannot_execute_request':
        case 'rpc_error_cannot_query_unfinalized_data':
        case 'rpc_error_contract_signature_hash_not_approved':
        case 'rpc_error_execution_reverted':
        case 'rpc_error_execution_timeout':
        case 'rpc_error_gas_price_is_less_than_minimum':
        case 'rpc_error_gas_required_exceeds_allowance':
        case 'rpc_error_insufficient_balance_for_transfer':
        case 'rpc_error_insufficient_funds_for_gas_and_value':
        case 'rpc_error_invalid_argument':
        case 'rpc_error_invalid_rsv_values':
        case 'rpc_error_invalid_sender':
        case 'rpc_error_max_fee_per_gas_less_than_block_base_fee':
        case 'rpc_error_nounce_is_too_low':
        case 'rpc_error_priority_fee_too_low':
        case 'rpc_error_replacement_not_allowed':
        case 'rpc_error_replacement_transaction_underpriced':
        case 'rpc_error_swap_failed':
        case 'rpc_error_too_many_requests':
        case 'rpc_error_transaction_erc20_insufficient_allowance':
        case 'rpc_error_transaction_erc20_insufficient_balance':
        case 'rpc_error_transaction_erc20_transfer_error':
        case 'rpc_error_transaction_underpriced':
        case 'rpc_error_tx_pool_disabled':
        case 'rpc_error_block_range_limit_exceeded':
        case 'rpc_error_unknown':
            return {
                tags: {
                    ...tags,
                    networkHexId: error.networkHexId,
                    networkType: error.networkType,
                    actionSourceType: error.actionSource?.type || null,
                    transactionEventSource:
                        error.actionSource?.transactionEventSource || null,
                },
                extra: {
                    response: error.response,
                    request: error.request,
                    dAppSiteInfo: error.actionSource?.dAppSiteInfo || null,
                },
            }

        case 'passkey_android_unknown_error':
            return {
                tags,
                extra: {
                    error: error.originalError,
                    stringifyError: stringifyUnknownData(error.originalError),
                },
            }

        case 'bundler_error_aa10_sender_already_constructed':
        case 'bundler_error_aa13_init_code_failed_or_out_of_gas':
        case 'bundler_error_aa21_didnt_pay_prefund':
        case 'bundler_error_aa22_expired_or_not_due':
        case 'bundler_error_aa23_reverted_or_oog':
        case 'bundler_error_aa24_signature_error':
        case 'bundler_error_aa25_invalid_account_nonce':
        case 'bundler_error_aa31_paymaster_deposit_too_low':
        case 'bundler_error_aa33_reverted_or_out_of_gas':
        case 'bundler_error_aa34_signature_error':
        case 'bundler_error_aa40_over_verification_gas_limit':
        case 'bundler_error_aa41_too_little_verification_gas':
        case 'bundler_error_aa51_prefund_below_gas_cost':
        case 'bundler_error_aa93_invalid_paymaster_and_data':
        case 'bundler_error_aa95_out_of_gas':
        case 'bundler_error_cannot_execute_request':
        case 'bundler_error_max_priority_fee_per_gas_is_lower_than_expected':
        case 'bundler_error_unknown':
        case 'bundler_error_user_operation_reverted_during_execution_phase':
        case 'bundler_error_user_operation_reverted_during_execution_phase_address_not_whitelisted':
        case 'bundler_error_user_operation_reverted_during_execution_phase_already_minted':
        case 'bundler_error_user_operation_reverted_during_execution_phase_return_amount_not_enough':
        case 'bundler_error_user_operation_reverted_during_execution_phase_transfer_amount_exceeds_balance':
            return {
                tags: {
                    ...tags,
                    networkHexId: error.networkHexId,
                    actionSourceType: error.actionSource?.type || null,
                    transactionEventSource:
                        error.actionSource?.transactionEventSource || null,
                    provider: error.provider,
                },
                extra: {
                    networkHexId: error.networkHexId,
                    response: stringifyUnknownData(error.response),
                    request: stringifyUnknownData(error.request),
                    dAppSiteInfo: error.actionSource?.dAppSiteInfo || null,
                    revertReasons: error.revertReasons || null,
                },
            }
        case 'unknown_merchant_code':
            return {
                tags: {
                    ...tags,
                    code: error.code,
                },
                extra: {
                    code: error.code,
                },
            }

        case 'eoa_transaction_failed_error':
            return {
                tags: {
                    ...tags,
                    actionSourceType: error.actionSource.type,
                    transactionEventSource:
                        error.actionSource.transactionEventSource,
                    networkHexId: error.networkHexId,
                    networkType: error.networkType,
                },
                extra: {
                    actionSourceType: error.actionSource.type,
                    transactionEventSource:
                        error.actionSource.transactionEventSource,
                    keyStoreType: error.keyStoreType,
                    networkHexId: error.networkHexId,
                    trxHash: error.trxHash,
                    reasons: error.reasons,
                },
            }

        case 'user_operation_failed_error':
            return {
                tags: {
                    ...tags,
                    actionSourceType: error.actionSource.type,
                    transactionEventSource:
                        error.actionSource.transactionEventSource,
                    networkHexId: error.networkHexId,
                    networkType: error.networkType,
                },
                extra: {
                    actionSourceType: error.actionSource.type,
                    transactionEventSource:
                        error.actionSource.transactionEventSource,
                    keyStoreType: error.keyStoreType,
                    networkHexId: error.networkHexId,
                    bundleTrxHash: error.bundleTrxHash,
                    userOperationHash: error.userOperationHash,
                    reasons: error.reasons,
                },
            }

        default:
            return notReachable(error)
    }
}

const stringifyUnknownData = (data: unknown): string =>
    stringify(scrubSensitiveFields(data), 2)

/**
 * @deprecated This helper is for domain internal use. Do not export and use it outside of Error domain
 */
export const captureAppError = (error: AppError, params: UIReportingParams) => {
    const { tags, extra } = getTagsAndExtra({ error })

    return report({
        error,
        tags: { ...tags, source: params.source },
        extra: { ...extra, ...params.extra },
    })
}

/**
 * @deprecated This helper is for domain internal use. Do not export and use it outside of Error domain
 */
export const captureBackendAppError = (
    error: AppError,
    params: BackendReportingParams
) => {
    const { tags, extra } = getTagsAndExtra({ error })

    return report({
        error,
        tags: {
            ...tags,
            source: params.source,
            requestId: params.requestId || null,
        },
        extra: { ...extra, ...params.extra },
    })
}

const report = ({
    error,
    tags,
    extra: rawExtra,
}: {
    error: AppError
    tags: Record<string, string | number | null>
    extra?: Record<string, unknown>
}): void => {
    const env = getEnvironment()

    const extra = scrubSensitiveFields(rawExtra)

    const stringifyErrorForLocalReporting = (input: AppError) =>
        stringifyUnknownData({
            type: error.type,
            tags,
            extra,
            message:
                typeof input === 'object' &&
                input !== null &&
                'message' in input
                    ? input.message
                    : null,
            stack:
                typeof input === 'object' && input !== null && 'stack' in input
                    ? input.stack
                    : null,
        })

    const alertError = (input: AppError) => {
        // eslint-disable-next-line no-console
        console.error(
            '💥💥💥 ',
            env,
            stringifyErrorForLocalReporting(error),
            error
        )

        if (global.alert) {
            global.alert(stringifyErrorForLocalReporting(input))
        }
    }

    switch (env) {
        case 'local':
            alertError(error)
            break

        case 'development':
            alertError(error)
            reporting.captureException(error, { tags, extra })
            break

        case 'production':
            reporting.captureException(error, { tags, extra })
            break

        default:
            notReachable(env)
    }
}
