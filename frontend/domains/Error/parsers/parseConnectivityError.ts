import { failure, oneOf, Result, success } from '@zeal/toolkit/Result'

import { ConnectivityError } from '@zeal/domains/Error'

// TODO @resetko-zeal should we parse it based on message here, instead of wrapping in the interceptor? Like "failed to fetch, etc"
const parseInterceptedConnectivityError = (
    input: unknown
): Result<unknown, ConnectivityError> =>
    input instanceof ConnectivityError
        ? success(input)
        : failure('not_correct_instance')

const parseWalletConnectConnectivityError = (
    input: unknown
): Result<unknown, ConnectivityError> =>
    input instanceof Error &&
    input.message.match(
        'WebSocket connection failed for host: wss://relay.walletconnect.org'
    )
        ? success(new ConnectivityError({ error: input, info: null }))
        : failure('not_correct_instance')

const parseMobileFetchNetworkRequestFailed = (
    input: unknown
): Result<unknown, ConnectivityError> =>
    input instanceof Error && input.message === 'Network request failed'
        ? success(new ConnectivityError({ error: input, info: null }))
        : failure('not_correct_instance')

export const parseConnectivityError = (
    input: unknown
): Result<unknown, ConnectivityError> =>
    oneOf(input, [
        parseInterceptedConnectivityError(input),
        parseWalletConnectConnectivityError(input),
        parseMobileFetchNetworkRequestFailed(input),
    ])
