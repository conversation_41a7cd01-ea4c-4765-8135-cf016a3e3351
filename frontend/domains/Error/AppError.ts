import {
    DecryptIncorrectPassword,
    EncryptedObjectInvalidFormat,
    InvalidEncryptedFileFormat,
} from '@zeal/toolkit/Crypto'
import { ImperativeError } from '@zeal/toolkit/Error'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { UnexpectedResultFailureError } from '@zeal/toolkit/Result'

import { BundlerResponseError } from '@zeal/domains/Error/domains/BundlerError'
import { BungeeError } from '@zeal/domains/Error/domains/Bungee'
import { GnosisPayError } from '@zeal/domains/Error/domains/GnosisPay'
import { LedgerError } from '@zeal/domains/Error/domains/Ledger'
import { MoneriumError } from '@zeal/domains/Error/domains/MoneriumError'
import { PasskeyError } from '@zeal/domains/Error/domains/Passkey'
import { RPCResponseError } from '@zeal/domains/Error/domains/RPCError'
import { SwapsIOError } from '@zeal/domains/Error/domains/SwapsIO'
import { TrezorError } from '@zeal/domains/Error/domains/Trezor'
import { UnblockError } from '@zeal/domains/Error/domains/Unblock'
import { WagmiError } from '@zeal/domains/Error/domains/Wagmi'
import { WalletConnectError } from '@zeal/domains/Error/domains/WalletConnect'
import { FailedToFetchGoogleAuthToken } from '@zeal/domains/GoogleDriveFile'
import { KeyStore, Safe4337 } from '@zeal/domains/KeyStore'
import { ActionSource } from '@zeal/domains/Main'
import { NetworkHexId } from '@zeal/domains/Network'
import {
    PREDEFINED_NETWORKS,
    TEST_NETWORKS,
} from '@zeal/domains/Network/constants'

import { RPCRequestParseError } from './RPCRequestParseError'

type Params = {
    url: string
    method: string
    status: number | null
    requestBody: unknown
    responseBody: unknown
    responseHeaders: Partial<Record<string, string>>
}

export type InfoParams = {
    url: string
    method: string
    requestBody: unknown
}

export class HttpError extends Error {
    isHttpError = true
    type: 'http_error' = 'http_error' as const
    name = 'HttpError' as const

    url: string
    urlOrigin: string
    urlPathname: string
    urlQuery: unknown

    method: string
    status: number | null
    requestBody: unknown
    responseBody: unknown
    responseHeaders: Partial<Record<string, string>>

    public static collectHeaders(
        headers: Record<string, string> | Headers = {}
    ): Partial<Record<string, string>> {
        const headersToCollect = [
            'trace-id',
            'request-id',
            'socketrequestid',
            'x-trace-id',
            'server-req-id',
            'x-unblock-amzn-requestid',
            'x-unblock-amzn-trace-id',
        ]

        if (headers instanceof Headers) {
            return headersToCollect.reduce(
                (acc, key) => {
                    if (headers.has(key)) {
                        const value = headers.get(key)
                        if (value) {
                            acc[key] = value
                        }
                    }
                    return acc
                },
                {} as Partial<Record<string, string>>
            )
        }

        return headersToCollect.reduce(
            (acc, key) => {
                if (headers[key] && typeof headers[key] === 'string') {
                    acc[key] = headers[key]
                }
                return acc
            },
            {} as Partial<Record<string, string>>
        )
    }

    constructor({
        responseHeaders,
        method,
        responseBody,
        requestBody,
        status,
        url,
    }: Params) {
        super('HttpError')
        this.url = url
        this.method = method
        this.status = status
        this.responseBody = responseBody
        this.requestBody = requestBody
        this.responseHeaders = responseHeaders

        try {
            const parsedUrl = new URL(url)
            this.urlOrigin = parsedUrl.origin
            this.urlPathname = parsedUrl.pathname
            this.urlQuery = parsedUrl.searchParams.toString()
        } catch {
            this.urlOrigin = ''
            this.urlPathname = ''
            this.urlQuery = ''
        }
    }
}

export class ConnectivityError extends Error {
    isConnectivityError = true
    type: 'connectivity_error' = 'connectivity_error' as const
    name = 'ConnectivityError' as const

    requestInfo: {
        url: string
        urlOrigin: string | null
        urlPathname: string | null
        urlQuery: unknown
        method: string
        requestBody: unknown
    } | null

    error: unknown
    message: string

    constructor({
        info,
        error,
    }: {
        error: unknown
        info: {
            url: string
            method: string
            requestBody: unknown
        } | null
    }) {
        super(`ConnectivityError`)

        if (info) {
            const urlParts = (() => {
                try {
                    const parsedUrl = new URL(info.url)
                    return {
                        urlOrigin: parsedUrl.origin,
                        urlPathname: parsedUrl.pathname,
                        urlQuery: parsedUrl.searchParams.toString(),
                    }
                } catch {
                    return {
                        urlOrigin: null,
                        urlPathname: null,
                        urlQuery: null,
                    }
                }
            })()

            this.requestInfo = {
                url: info.url,
                urlOrigin: urlParts.urlOrigin,
                urlPathname: urlParts.urlPathname,
                urlQuery: urlParts.urlQuery,
                method: info.method,
                requestBody: info.requestBody,
            }
        } else {
            this.requestInfo = null
        }

        if (error instanceof Error) {
            this.message = error.message
            this.stack = error.stack
        } else {
            this.message = ''
        }
    }
}

export class UnknownError extends Error {
    type: 'unknown_error' = 'unknown_error' as const
    name = 'UnknownError' as const
    originalError: unknown

    constructor(originalError: unknown) {
        super(
            originalError instanceof Error ? originalError.message : undefined
        )

        this.originalError = originalError
        this.stack = originalError instanceof Error ? originalError.stack : ''
    }
}

export type GoogleApiError = {
    type: 'google_api_error'
    code: number // TODO  @max-tern :: add more union codes here
    message: string
    error: unknown
}

export type BiometricPromptCancelled = { type: 'biometric_prompt_cancelled' }
export type BiometricPromptAuthFailed = { type: 'biometric_prompt_auth_failed' }
export type SecureStoreKeychainDecryptionError = {
    type: 'secure_store_keychain_decryption_error'
}

export type IosCouldNotCommunicateWithHelperApplication = {
    type: 'ios_could_not_communicate_with_helper_application'
}

export class UnknownMerchantCode extends Error {
    type: 'unknown_merchant_code' = 'unknown_merchant_code' as const
    name: string = 'UnknownMerchantCode' as const

    code: number

    constructor(code: number) {
        super()
        this.code = code
        this.message = `Unknown merchant code: ${code}`
    }
}

export class SignalAborted extends Error {
    type: 'signal_aborted_for_unknown_reason' =
        'signal_aborted_for_unknown_reason' as const
    error: unknown

    constructor(error: unknown) {
        super()
        this.error = error
    }
}

export class EOATransactionFailedError extends Error {
    type: 'eoa_transaction_failed_error' =
        'eoa_transaction_failed_error' as const
    name: string = 'EOATransactionFailedError' as const

    keyStoreType: KeyStore['type']
    networkHexId: NetworkHexId
    actionSource: ActionSource
    trxHash: Hexadecimal.Hexadecimal
    reasons: string[]
    networkType: 'predefined' | 'testnet' | 'custom'

    constructor({
        keyStoreType,
        networkHexId,
        actionSource,
        trxHash,
        reasons,
    }: {
        keyStoreType: KeyStore['type']
        networkHexId: NetworkHexId
        actionSource: ActionSource
        trxHash: Hexadecimal.Hexadecimal
        reasons: string[]
    }) {
        super()
        this.keyStoreType = keyStoreType
        this.networkHexId = networkHexId
        this.actionSource = actionSource
        this.trxHash = trxHash
        this.reasons = reasons
        this.networkType = (() => {
            if (
                PREDEFINED_NETWORKS.some(
                    (network) => network.hexChainId === networkHexId
                )
            ) {
                return 'predefined'
            }

            if (
                TEST_NETWORKS.some(
                    (network) => network.hexChainId === networkHexId
                )
            ) {
                return 'testnet'
            }

            return 'custom'
        })()
    }
}

export class UserOperationFailedError extends Error {
    type: 'user_operation_failed_error' = 'user_operation_failed_error' as const
    name: string = 'UserOperationFailedError' as const

    keyStoreType: Safe4337['type']
    networkHexId: NetworkHexId
    actionSource: ActionSource
    bundleTrxHash: Hexadecimal.Hexadecimal | null
    userOperationHash: Hexadecimal.Hexadecimal
    reasons: string[]
    networkType: 'predefined' | 'testnet' | 'custom'

    constructor({
        keyStoreType,
        networkHexId,
        actionSource,
        bundleTrxHash,
        userOperationHash,
        reasons,
    }: {
        keyStoreType: Safe4337['type']
        networkHexId: NetworkHexId
        actionSource: ActionSource
        bundleTrxHash: Hexadecimal.Hexadecimal | null
        userOperationHash: Hexadecimal.Hexadecimal
        reasons: string[]
    }) {
        super()
        this.keyStoreType = keyStoreType
        this.networkHexId = networkHexId
        this.actionSource = actionSource
        this.bundleTrxHash = bundleTrxHash
        this.userOperationHash = userOperationHash
        this.reasons = reasons
        this.networkType = (() => {
            if (
                PREDEFINED_NETWORKS.some(
                    (network) => network.hexChainId === networkHexId
                )
            ) {
                return 'predefined'
            }

            if (
                TEST_NETWORKS.some(
                    (network) => network.hexChainId === networkHexId
                )
            ) {
                return 'testnet'
            }

            return 'custom'
        })()
    }
}

export const ZEAL_ERROR_CODES = {
    A_REWARDS_ALREADY_CLAIMED: 4002,
    B_REWARDS_ALREADY_CLAIMED: 4003,
    B_REWARD_CANNOT_BE_CLAIMED: 4004,
    B_REWARD_CANNOT_BE_CLAIMED_NOT_ONBOARDED_USER: 4005,
} as const

export type BRewardCannotBeClamed = {
    type: 'breward_cannotbe_claimed'
    code: (typeof ZEAL_ERROR_CODES)['B_REWARD_CANNOT_BE_CLAIMED']
    originalError: unknown
}

export type BRewardAlreadyClaimed = {
    type: 'breward_already_claimed'
    code: (typeof ZEAL_ERROR_CODES)['B_REWARDS_ALREADY_CLAIMED']
    originalError: unknown
}

export type ZealARewardsAlreadyClaimedError = {
    type: 'zeal_a_rewards_already_claimed_error'
    code: (typeof ZEAL_ERROR_CODES)['A_REWARDS_ALREADY_CLAIMED']
}

/**
 * General rules of adding more errors:
 * 0. Always make sure you have `type` field in your error, this is used for parsing and grouping in sentry
 * 1. If you need stacktrace - your error should be an instance of Error
 * 2. If you need message - your error should be an instance of Error
 * 3. Try to avoid wrapping errors into another errors (unknwon_error is the only exception)
 */
export type AppError =
    | BiometricPromptAuthFailed
    | BiometricPromptCancelled
    | BundlerResponseError
    | BungeeError
    | ConnectivityError
    | DecryptIncorrectPassword
    | EOATransactionFailedError
    | EncryptedObjectInvalidFormat
    | FailedToFetchGoogleAuthToken
    | GnosisPayError
    | GoogleApiError
    | HttpError
    | ImperativeError
    | InvalidEncryptedFileFormat
    | IosCouldNotCommunicateWithHelperApplication
    | LedgerError
    | MoneriumError
    | PasskeyError
    | RPCRequestParseError
    | RPCResponseError
    | SecureStoreKeychainDecryptionError
    | SignalAborted
    | SwapsIOError
    | TrezorError
    | UnblockError
    | UnexpectedResultFailureError<unknown>
    | UnknownError
    | UnknownMerchantCode
    | UserOperationFailedError
    | WagmiError
    | WalletConnectError
    | ZealARewardsAlreadyClaimedError
    | BRewardAlreadyClaimed
    | BRewardCannotBeClamed

export type Source =
    | 'manually_captured'
    | 'app_error_popup'
    | 'error_boundary'
    | 'app_error_list_item'
    | 'app_error_banner'
    | 'api_error_handler'
