import React from 'react'
import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Clickable } from '@zeal/uikit/Clickable'
import { Column } from '@zeal/uikit/Column'
import { Group } from '@zeal/uikit/Group'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { BoldGeneralBank } from '@zeal/uikit/Icon/BoldGeneralBank'
import { CreditCardSolid } from '@zeal/uikit/Icon/CreditCardSolid'
import { ListItem } from '@zeal/uikit/ListItem'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { ScrollContainer } from '@zeal/uikit/ScrollContainer'
import { Toggle } from '@zeal/uikit/Toggle'

import { notReachable } from '@zeal/toolkit'
import { values } from '@zeal/toolkit/Object'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { CurrencyHiddenMap } from '@zeal/domains/Currency'
import { KeyStoreMap, SigningKeyStore, TrackOnly } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import {
    DefaultCurrencyConfig,
    NotificationsConfig,
} from '@zeal/domains/Storage'

import { AccountNotificationListItem } from './AccountNotificationListItem'

type Props = {
    notificationsConfig: NotificationsConfig
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    currencyHiddenMap: CurrencyHiddenMap
    installationId: string
    portfolioMap: PortfolioMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | {
          type: 'on_notifications_config_changed'
          notificationsConfig: NotificationsConfig
      }

type GroupedAccounts = {
    activeAccounts: { account: Account; keystore: SigningKeyStore }[]
    readOnlyAccounts: { account: Account; keystore: TrackOnly }[]
}
const groupAccountsByKeyStore = (
    accountsMap: AccountsMap,
    keyStoreMap: KeyStoreMap
): GroupedAccounts =>
    values(accountsMap).reduce(
        (result, account) => {
            const keystore = getKeyStore({
                address: account.address,
                keyStoreMap,
            })
            switch (keystore.type) {
                case 'track_only':
                    result.readOnlyAccounts.push({ account, keystore })
                    break
                case 'private_key_store':
                case 'ledger':
                case 'secret_phrase_key':
                case 'trezor':
                case 'safe_4337':
                    result.activeAccounts.push({ account, keystore })
                    break
                /* istanbul ignore next */
                default:
                    return notReachable(keystore)
            }
            return result
        },
        {
            activeAccounts: [],
            readOnlyAccounts: [],
        } as GroupedAccounts
    )

export const Layout = ({
    notificationsConfig,
    onMsg,
    accountsMap,
    currencyHiddenMap,
    keyStoreMap,
    installationId,
    portfolioMap,
    defaultCurrencyConfig,
}: Props) => {
    const { activeAccounts, readOnlyAccounts } = groupAccountsByKeyStore(
        accountsMap,
        keyStoreMap
    )

    const allActiveWalletsEnabled = activeAccounts.every(
        ({ account }) => notificationsConfig.wallets[account.address]
    )
    const allReadOnlyWalletsEnabled = readOnlyAccounts.every(
        ({ account }) => notificationsConfig.wallets[account.address]
    )

    return (
        <Screen
            padding="form"
            background="light"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <Column spacing={16} fill>
                <ActionBar
                    left={
                        <Clickable onClick={() => onMsg({ type: 'close' })}>
                            <Row spacing={4}>
                                <BackIcon size={24} color="iconDefault" />

                                <ActionBar.Header>
                                    <FormattedMessage
                                        id="notification-settings.title"
                                        defaultMessage="Notification settings"
                                    />
                                </ActionBar.Header>
                            </Row>
                        </Clickable>
                    }
                />
                <ScrollContainer withFloatingActions={false}>
                    <Column spacing={8}>
                        <Group variant="default">
                            <ListItem
                                avatar={({ size }) => (
                                    <CreditCardSolid
                                        size={size}
                                        color="iconAccent2"
                                    />
                                )}
                                aria-current={false}
                                size="large"
                                primaryText={
                                    <FormattedMessage
                                        id="notification-settings.toggles.card-payments"
                                        defaultMessage="Card payments"
                                    />
                                }
                                side={{
                                    rightIcon: () => (
                                        <Toggle
                                            variant="default"
                                            size="regular"
                                            title={null}
                                            checked={
                                                notificationsConfig.cardPayments
                                            }
                                            onClick={() => {
                                                onMsg({
                                                    type: 'on_notifications_config_changed',
                                                    notificationsConfig: {
                                                        ...notificationsConfig,
                                                        cardPayments:
                                                            !notificationsConfig.cardPayments,
                                                    },
                                                })
                                            }}
                                        />
                                    ),
                                }}
                            />
                        </Group>
                        <Group variant="default">
                            <ListItem
                                avatar={({ size }) => (
                                    <BoldGeneralBank
                                        size={size}
                                        color="iconAccent2"
                                    />
                                )}
                                aria-current={false}
                                size="large"
                                primaryText={
                                    <FormattedMessage
                                        id="notification-settings.toggles.bank-transfers"
                                        defaultMessage="Bank transfers"
                                    />
                                }
                                side={{
                                    rightIcon: () => (
                                        <Toggle
                                            variant="default"
                                            size="regular"
                                            title={null}
                                            checked={
                                                notificationsConfig.bankTransfers
                                            }
                                            onClick={() => {
                                                onMsg({
                                                    type: 'on_notifications_config_changed',
                                                    notificationsConfig: {
                                                        ...notificationsConfig,
                                                        bankTransfers:
                                                            !notificationsConfig.bankTransfers,
                                                    },
                                                })
                                            }}
                                        />
                                    ),
                                }}
                            />
                        </Group>
                        {activeAccounts.length > 0 ? (
                            <Group variant="default">
                                <ListItem
                                    aria-current={false}
                                    size="large"
                                    primaryText={
                                        <FormattedMessage
                                            id="notification-settings.toggles.active-wallets"
                                            defaultMessage="Active wallets"
                                        />
                                    }
                                    side={{
                                        rightIcon: () => (
                                            <Toggle
                                                variant="default"
                                                size="regular"
                                                title={null}
                                                checked={
                                                    allActiveWalletsEnabled
                                                }
                                                onClick={() => {
                                                    const updatedWallets =
                                                        activeAccounts.reduce(
                                                            (
                                                                wallets,
                                                                { account }
                                                            ) => {
                                                                wallets[
                                                                    account.address
                                                                ] =
                                                                    !allActiveWalletsEnabled
                                                                return wallets
                                                            },
                                                            notificationsConfig.wallets
                                                        )

                                                    onMsg({
                                                        type: 'on_notifications_config_changed',
                                                        notificationsConfig: {
                                                            ...notificationsConfig,
                                                            wallets:
                                                                updatedWallets,
                                                        },
                                                    })
                                                }}
                                            />
                                        ),
                                    }}
                                />
                                {activeAccounts.map(({ account, keystore }) => {
                                    const isEnabled =
                                        notificationsConfig.wallets[
                                            account.address
                                        ]
                                    return (
                                        <AccountNotificationListItem
                                            defaultCurrencyConfig={
                                                defaultCurrencyConfig
                                            }
                                            key={account.address}
                                            account={account}
                                            keyStore={keystore}
                                            portfolioMap={portfolioMap}
                                            installationId={installationId}
                                            currencyHiddenMap={
                                                currencyHiddenMap
                                            }
                                            notificationsEnabled={isEnabled}
                                            onMsg={(msg) => {
                                                switch (msg.type) {
                                                    case 'on_account_notification_toggle':
                                                        onMsg({
                                                            type: 'on_notifications_config_changed',
                                                            notificationsConfig:
                                                                {
                                                                    ...notificationsConfig,
                                                                    wallets: {
                                                                        ...notificationsConfig.wallets,
                                                                        [account.address]:
                                                                            !isEnabled,
                                                                    },
                                                                },
                                                        })
                                                        break
                                                    /* istanbul ignore next */
                                                    default:
                                                        return notReachable(
                                                            msg.type
                                                        )
                                                }
                                            }}
                                        />
                                    )
                                })}
                            </Group>
                        ) : null}

                        {readOnlyAccounts.length > 0 ? (
                            <Group variant="default">
                                <ListItem
                                    aria-current={false}
                                    size="large"
                                    primaryText={
                                        <FormattedMessage
                                            id="notification-settings.toggles.readonly-wallets"
                                            defaultMessage="Read-only wallets"
                                        />
                                    }
                                    side={{
                                        rightIcon: () => (
                                            <Toggle
                                                variant="default"
                                                size="regular"
                                                title={null}
                                                checked={
                                                    allReadOnlyWalletsEnabled
                                                }
                                                onClick={() => {
                                                    const updatedWallets =
                                                        readOnlyAccounts.reduce(
                                                            (
                                                                wallets,
                                                                { account }
                                                            ) => {
                                                                wallets[
                                                                    account.address
                                                                ] =
                                                                    !allReadOnlyWalletsEnabled
                                                                return wallets
                                                            },
                                                            notificationsConfig.wallets
                                                        )

                                                    onMsg({
                                                        type: 'on_notifications_config_changed',
                                                        notificationsConfig: {
                                                            ...notificationsConfig,
                                                            wallets:
                                                                updatedWallets,
                                                        },
                                                    })
                                                }}
                                            />
                                        ),
                                    }}
                                />
                                {readOnlyAccounts.map(
                                    ({ account, keystore }) => {
                                        const isEnabled =
                                            notificationsConfig.wallets[
                                                account.address
                                            ]
                                        return (
                                            <AccountNotificationListItem
                                                defaultCurrencyConfig={
                                                    defaultCurrencyConfig
                                                }
                                                key={account.address}
                                                account={account}
                                                keyStore={keystore}
                                                portfolioMap={portfolioMap}
                                                installationId={installationId}
                                                currencyHiddenMap={
                                                    currencyHiddenMap
                                                }
                                                notificationsEnabled={isEnabled}
                                                onMsg={(msg) => {
                                                    switch (msg.type) {
                                                        case 'on_account_notification_toggle':
                                                            onMsg({
                                                                type: 'on_notifications_config_changed',
                                                                notificationsConfig:
                                                                    {
                                                                        ...notificationsConfig,
                                                                        wallets:
                                                                            {
                                                                                ...notificationsConfig.wallets,
                                                                                [account.address]:
                                                                                    !isEnabled,
                                                                            },
                                                                    },
                                                            })
                                                            break
                                                        /* istanbul ignore next */
                                                        default:
                                                            return notReachable(
                                                                msg.type
                                                            )
                                                    }
                                                }}
                                            />
                                        )
                                    }
                                )}
                            </Group>
                        ) : null}
                    </Column>
                </ScrollContainer>
            </Column>
        </Screen>
    )
}
