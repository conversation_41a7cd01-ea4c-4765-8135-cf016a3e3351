import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { Address } from '@zeal/toolkit/Web3/address'

import { AccountsMap } from '@zeal/domains/Account'
import {
    CardSlientSignKeyStore,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import {
    ConfiguredEarn,
    DeployedTaker,
    EarnTakerMetrics,
} from '@zeal/domains/Earn'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { CryptoMoney } from '@zeal/domains/Money'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { Portfolio2, PortfolioMap } from '@zeal/domains/Portfolio'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { AutoAllocation } from './AutoAllocation'
import { ManualAllocation } from './ManualAllocation'

type Variant =
    | {
          type: 'send_to_earn'
          earn: ConfiguredEarn
          taker: DeployedTaker
      }
    | {
          type: 'send_to_card'
      }
    | {
          type: 'let_user_choose'
          senderKeyStore: CardSlientSignKeyStore
      }

type Props = {
    earnTakerMetrics: EarnTakerMetrics
    toAddress: Address
    variant: Variant
    cachedCardOwnerPortfolio: Portfolio2

    recievedMoney: CryptoMoney
    cardWalletKeySore: CardSlientSignKeyStore
    portfolioMap: PortfolioMap
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    sessionPassword: string
    installationId: string
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    feePresetMap: FeePresetMap
    customCurrencies: CustomCurrencyMap
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig

    defaultCurrencyConfig: DefaultCurrencyConfig
    experimentalMode: boolean

    onMsg: (msg: Msg) => void
}

type Msg = MsgOf<typeof AutoAllocation> | MsgOf<typeof ManualAllocation>

export const MakeDepositSpendable = ({
    earnTakerMetrics,
    toAddress,
    sessionPassword,
    variant,
    recievedMoney,
    portfolioMap,
    onMsg,
    networkRPCMap,
    networkMap,
    cachedCardOwnerPortfolio,
    keyStoreMap,
    installationId,
    gasCurrencyPresetMap,
    feePresetMap,
    defaultCurrencyConfig,
    customCurrencies,
    currencyPinMap,
    currencyHiddenMap,
    cardConfig,
    accountsMap,
    cardWalletKeySore,
    experimentalMode,
}: Props) => {
    switch (variant.type) {
        case 'send_to_card':
        case 'send_to_earn':
            return (
                <AutoAllocation
                    earnTakerMetrics={earnTakerMetrics}
                    toAddress={toAddress}
                    sendTo={variant}
                    recievedMoney={recievedMoney}
                    cardWalletKeySore={cardWalletKeySore}
                    portfolioMap={portfolioMap}
                    accountsMap={accountsMap}
                    keyStoreMap={keyStoreMap}
                    sessionPassword={sessionPassword}
                    installationId={installationId}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    feePresetMap={feePresetMap}
                    customCurrencies={customCurrencies}
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    cardConfig={cardConfig}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    experimentalMode={experimentalMode}
                    onMsg={onMsg}
                />
            )
        case 'let_user_choose':
            return (
                <ManualAllocation
                    receivedMoney={recievedMoney}
                    senderAddress={toAddress}
                    senderKeyStore={variant.senderKeyStore}
                    cachedCardOwnerPortfolio={cachedCardOwnerPortfolio}
                    cardConfig={cardConfig}
                    sessionPassword={sessionPassword}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    portfolioMap={portfolioMap}
                    accountsMap={accountsMap}
                    keyStoreMap={keyStoreMap}
                    installationId={installationId}
                    feePresetMap={feePresetMap}
                    customCurrencies={customCurrencies}
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    cardWalletKeySore={cardWalletKeySore}
                    earnTakerMetrics={earnTakerMetrics}
                    onMsg={onMsg}
                />
            )
        default:
            return notReachable(variant)
    }
}
