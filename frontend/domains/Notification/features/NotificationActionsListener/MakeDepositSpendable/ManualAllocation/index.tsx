import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { uuid } from '@zeal/toolkit/Crypto'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { AccountsMap } from '@zeal/domains/Account'
import {
    CardSlientSignKeyStore,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { Taker } from '@zeal/domains/Earn'
import { KeyStoreMap, SigningKeyStore } from '@zeal/domains/KeyStore'
import { CryptoMoney } from '@zeal/domains/Money'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { Portfolio2, PortfolioMap } from '@zeal/domains/Portfolio'
import { CustomCurrencyMap, DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { Form } from './Form'

import { fetchData } from '../api/fetchData'
import { RedirectToEarn } from '../RedirectToEarn'

type Props = {
    receivedMoney: CryptoMoney
    senderAddress: Web3.address.Address
    senderKeyStore: SigningKeyStore
    cardWalletKeySore: CardSlientSignKeyStore
    cachedCardOwnerPortfolio: Portfolio2
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig

    sessionPassword: string
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    portfolioMap: PortfolioMap
    accountsMap: AccountsMap
    keyStoreMap: KeyStoreMap
    installationId: string
    feePresetMap: FeePresetMap
    customCurrencies: CustomCurrencyMap
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<MsgOf<typeof Form>, { type: 'close' | 'on_card_top_up_success' }>
    | Extract<
          MsgOf<typeof RedirectToEarn>,
          {
              type:
                  | 'close'
                  | 'on_earn_configured'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_predefined_fee_preset_selected'
                  | 'import_keys_button_clicked'
                  | 'on_swaps_io_swap_request_created'
                  | 'on_earn_deposit_success'
          }
      >

type State =
    | { type: 'form' }
    | { type: 'earn_deposit'; amount: CryptoMoney; taker: Taker }

export const ManualAllocation = ({
    cachedCardOwnerPortfolio,
    senderAddress,
    cardConfig,
    senderKeyStore,
    sessionPassword,
    networkRPCMap,
    networkMap,
    receivedMoney,
    cardWalletKeySore,
    keyStoreMap,
    currencyHiddenMap,
    currencyPinMap,
    customCurrencies,
    gasCurrencyPresetMap,
    defaultCurrencyConfig,
    feePresetMap,
    portfolioMap,
    accountsMap,
    installationId,
    onMsg,
}: Props) => {
    const [state, setState] = useState<State>({ type: 'form' })

    const sender = accountsMap[senderAddress]

    const [loadable, setLoadable] = useLoadableData(fetchData, {
        type: 'loading',
        params: {
            account: sender,
            cardConfig,
            currencyHiddenMap,
            customCurrencies,
            defaultCurrencyConfig,
            installationId,
            networkMap,
            networkRPCMap,
            cardWalletKeySore,
            sessionPassword,
            cacheKey: uuid(),
        },
    })

    switch (state.type) {
        case 'form':
            return (
                <Form
                    installationId={installationId}
                    receivedMoney={receivedMoney}
                    senderAddress={senderAddress}
                    senderKeyStore={senderKeyStore}
                    cachedCardOwnerPortfolio={cachedCardOwnerPortfolio}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    cardConfig={cardConfig}
                    sessionPassword={sessionPassword}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                onMsg(msg)
                                break
                            case 'on_form_submitted_without_card_top_up':
                                setState({
                                    type: 'earn_deposit',
                                    taker: msg.taker,
                                    amount: msg.earnAmount,
                                })
                                break
                            case 'on_card_top_up_success':
                                // FIXME :: @Nicvaniek - onMsg + setState if there is an earn amount
                                throw new Error(`Not implemented`)
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'earn_deposit':
            return (
                <RedirectToEarn
                    loadable={loadable}
                    initialSender={sender}
                    taker={state.taker}
                    recievedMoney={state.amount}
                    portfolioMap={portfolioMap}
                    accountsMap={accountsMap}
                    keyStoreMap={keyStoreMap}
                    sessionPassword={sessionPassword}
                    installationId={installationId}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    feePresetMap={feePresetMap}
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    cardConfig={cardConfig}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                            case 'on_earn_configured':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'on_predefined_fee_preset_selected':
                            case 'import_keys_button_clicked':
                            case 'on_swaps_io_swap_request_created':
                            case 'on_earn_deposit_success':
                                onMsg(msg)
                                break
                            case 'on_account_selected':
                                setLoadable({
                                    type: 'loading',
                                    params: {
                                        ...loadable.params,
                                        account: msg.account,
                                    },
                                })
                                break
                            case 'on_redirect_to_earn_try_again_clicked':
                                setLoadable({
                                    type: 'loading',
                                    params: {
                                        ...loadable.params,
                                        cacheKey: uuid(),
                                    },
                                })
                                break
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        default:
            return notReachable(state)
    }
}
