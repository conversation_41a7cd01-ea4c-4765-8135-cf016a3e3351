import { useState } from 'react'

import { noop, notReachable } from '@zeal/toolkit'
import { div } from '@zeal/toolkit/BigInt'
import { useLazyLoadableData } from '@zeal/toolkit/LoadableData/LazyLoadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { RangeInt } from '@zeal/toolkit/Range'
import * as Web3 from '@zeal/toolkit/Web3'

import {
    ReadonlySignerSelectedOnboardedCardConfig,
    SubmittedEOASendCardTopUp,
    SubmittedSafeSendCardTopUp,
} from '@zeal/domains/Card'
import { Earn, Taker } from '@zeal/domains/Earn'
import { sortTakersByBalance } from '@zeal/domains/Earn/helpers/sortTakersByBalance'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { SigningKeyStore } from '@zeal/domains/KeyStore'
import { CryptoMoney } from '@zeal/domains/Money'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { Portfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { Form as FormType, Layout } from './Layout'
import { Modal, State as ModalState } from './Modal'

type Props = {
    receivedMoney: CryptoMoney
    senderAddress: Web3.address.Address
    senderKeyStore: SigningKeyStore
    cachedCardOwnerPortfolio: Portfolio2
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig

    sessionPassword: string
    installationId: string
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<MsgOf<typeof Layout>, { type: 'close' }>
    | {
          type: 'on_form_submitted_without_card_top_up'
          earnAmount: CryptoMoney
          taker: Taker
      }
    | {
          type: 'on_card_top_up_success'
          submittedCardTopUp:
              | SubmittedEOASendCardTopUp
              | SubmittedSafeSendCardTopUp
      }

const calculateInitialTaker = ({
    earn,
    defaultCurrencyConfig,
}: {
    earn: Earn
    defaultCurrencyConfig: DefaultCurrencyConfig
}): Taker => {
    switch (earn.type) {
        case 'not_configured':
            return (
                earn.takers.find((taker) => {
                    switch (taker.type) {
                        case 'eur':
                            return true
                        case 'usd':
                        case 'chf':
                        case 'eth':
                            return false
                        default:
                            return notReachable(taker.type)
                    }
                }) || earn.takers[0]
            )
        case 'configured':
            switch (earn.cardRecharge.type) {
                case 'recharge_enabled':
                    return earn.cardRecharge.rebalancers[0]
                case 'recharge_disabled':
                    return earn.takers.toSorted(
                        sortTakersByBalance({
                            takerPortfolioMap: earn.takerPortfolioMap,
                            defaultCurrencyConfig,
                        })
                    )[0]
                default:
                    return notReachable(earn.cardRecharge)
            }
        default:
            return notReachable(earn)
    }
}

const calculateInitialForm = ({
    cardOwnerPortfolio,
    defaultCurrencyConfig,
    receivedMoney,
}: {
    cardOwnerPortfolio: Portfolio2
    defaultCurrencyConfig: DefaultCurrencyConfig
    receivedMoney: CryptoMoney
}): FormType => {
    const { earn, cardBalance } = cardOwnerPortfolio

    const taker = calculateInitialTaker({ earn, defaultCurrencyConfig })

    const defaultSplit: FormType = { taker, cardSplit: 50 } // FIXME :: @Nicvaniek use storage preference

    switch (earn.type) {
        case 'not_configured':
            return defaultSplit
        case 'configured':
            switch (earn.cardRecharge.type) {
                case 'recharge_disabled':
                    return defaultSplit
                case 'recharge_enabled':
                    if (!cardBalance) {
                        return defaultSplit
                    }

                    const amountNeededToRecharge =
                        earn.cardRecharge.threshold - cardBalance.total.amount

                    if (amountNeededToRecharge <= 0) {
                        return { taker, cardSplit: 0 }
                    }

                    const cardSplit = Math.min(
                        100,
                        Math.ceil(
                            div(amountNeededToRecharge, receivedMoney.amount) *
                                100
                        )
                    ) as RangeInt<0, 100>

                    return {
                        taker,
                        cardSplit,
                    }
                default:
                    return notReachable(earn.cardRecharge)
            }
        default:
            return notReachable(earn)
    }
}

const submitCardTopUpTx = async (_: {
    senderAddress: Web3.address.Address
    senderKeyStore: SigningKeyStore
    amount: CryptoMoney
    cardSafeAddress: Web3.address.Address
    sessionPassword: string
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}): Promise<SubmittedEOASendCardTopUp | SubmittedSafeSendCardTopUp> => {
    // FIXME :: @Nicvaniek
    throw new Error(`Not implemented`)
}

export const Form = ({
    cachedCardOwnerPortfolio,
    receivedMoney,
    installationId,
    defaultCurrencyConfig,
    onMsg,
}: Props) => {
    const [modal, setModal] = useState<ModalState>({ type: 'closed' })

    const [form, setForm] = useState<FormType>(() =>
        calculateInitialForm({
            receivedMoney,
            defaultCurrencyConfig,
            cardOwnerPortfolio: cachedCardOwnerPortfolio,
        })
    )

    const [cardTopUpLoadable, setCardTopUpLoadable] = useLazyLoadableData(
        submitCardTopUpTx,
        {
            type: 'not_asked',
        }
    )

    // FIXME :: @Nicvaniek - useEffect on loadable to send on_card_top_up_success

    switch (cardTopUpLoadable.type) {
        case 'not_asked':
            return (
                <>
                    <Layout
                        form={form}
                        receivedMoney={receivedMoney}
                        cardOwnerPortfolio={cachedCardOwnerPortfolio}
                        isLoading={false}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg(msg)
                                    break
                                case 'on_earn_account_clicked':
                                    setModal({ type: 'select_earn_account' })
                                    break
                                case 'on_slider_changed':
                                    setForm({
                                        ...form,
                                        cardSplit: msg.newCardSplit,
                                    })
                                    break
                                case 'on_form_submitted':
                                    // FIXME :: @Nicvaniek - if no cardSplit -> on_form_submitted_without_card_top_up else setLoadable
                                    throw new Error(`Not implemented`)
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                    <Modal
                        state={modal}
                        cardOwnerEarn={cachedCardOwnerPortfolio.earn}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    setModal({ type: 'closed' })
                                    break
                                case 'on_earn_account_selected':
                                    setForm({ ...form, taker: msg.taker })
                                    break
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </>
            )
        case 'loading':
        case 'loaded':
            return (
                <Layout
                    form={form}
                    receivedMoney={receivedMoney}
                    cardOwnerPortfolio={cachedCardOwnerPortfolio}
                    isLoading
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                onMsg(msg)
                                break
                            case 'on_earn_account_clicked':
                            case 'on_slider_changed':
                            case 'on_form_submitted':
                                noop()
                                break
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'error':
            return (
                <>
                    <Layout
                        form={form}
                        receivedMoney={receivedMoney}
                        cardOwnerPortfolio={cachedCardOwnerPortfolio}
                        isLoading
                        onMsg={noop}
                    />
                    <AppErrorPopup
                        error={parseAppError(cardTopUpLoadable.error)}
                        installationId={installationId}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg(msg)
                                    break
                                case 'try_again_clicked':
                                    setCardTopUpLoadable({
                                        type: 'loading',
                                        params: cardTopUpLoadable.params,
                                    })
                                    break
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </>
            )
        default:
            return notReachable(cardTopUpLoadable)
    }
}
