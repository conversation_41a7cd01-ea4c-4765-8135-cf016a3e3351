import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { HeaderV2 } from '@zeal/uikit/HeaderV2'
import { CloseCross } from '@zeal/uikit/Icon/CloseCross'
import { IconButton } from '@zeal/uikit/IconButton'
import { Popup } from '@zeal/uikit/Popup'
import { Row } from '@zeal/uikit/Row'
import { Slider } from '@zeal/uikit/Slider'
import { Text } from '@zeal/uikit/Text'

import { fromFixedWithFraction } from '@zeal/toolkit/BigInt'
import { RangeInt } from '@zeal/toolkit/Range'

import { Taker } from '@zeal/domains/Earn'
import { CryptoMoney } from '@zeal/domains/Money'
import { FormattedMoneyPrecise } from '@zeal/domains/Money/components/FormattedMoneyPrecise'
import { convertStableCoinToFiat } from '@zeal/domains/Money/helpers/convertStableCoinToFiat'
import { floor } from '@zeal/domains/Money/helpers/floor'
import { mulByNumber } from '@zeal/domains/Money/helpers/mul'
import { sub2 } from '@zeal/domains/Money/helpers/sub'
import { Portfolio2 } from '@zeal/domains/Portfolio'

type Props = {
    form: Form
    receivedMoney: CryptoMoney
    cardOwnerPortfolio: Portfolio2
    isLoading: boolean
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | { type: 'on_earn_account_clicked' }
    | { type: 'on_slider_changed'; newCardSplit: RangeInt<0, 100> }
    | {
          type: 'on_form_submitted'
          taker: Taker
          cardAmount: CryptoMoney
      }

export type Form = {
    taker: Taker
    cardSplit: RangeInt<0, 100>
}

const THRESHOLD_AMOUNT_FOR_INT_SPLITS = '20'

const getCardAmount = ({
    form,
    receivedMoney,
}: {
    form: Form
    receivedMoney: CryptoMoney
}): CryptoMoney => {
    const threshold = fromFixedWithFraction(
        THRESHOLD_AMOUNT_FOR_INT_SPLITS,
        receivedMoney.currency.fraction
    )

    const cardAmount = mulByNumber(receivedMoney, form.cardSplit / 100)

    return receivedMoney.amount < threshold ? cardAmount : floor(cardAmount)
}

// FIXME :: @Nicvaniek events
export const Layout = ({ onMsg, form, receivedMoney, isLoading }: Props) => {
    const cardAmount = getCardAmount({ form, receivedMoney })
    const earnAmount = sub2(receivedMoney, cardAmount)

    return (
        <Popup.Layout onMsg={onMsg}>
            <ActionBar
                right={
                    <IconButton
                        variant="on_light"
                        onClick={() => onMsg({ type: 'close' })}
                    >
                        {({ color }) => <CloseCross size={24} color={color} />}
                    </IconButton>
                }
            />
            <Column spacing={32}>
                <HeaderV2
                    title={
                        <FormattedMessage
                            id="makeSpendable.title"
                            defaultMessage="{amount} Received"
                            values={{
                                amount: (
                                    <FormattedMoneyPrecise
                                        money={convertStableCoinToFiat({
                                            money: receivedMoney,
                                        })}
                                        sign={null}
                                        withSymbol
                                    />
                                ),
                            }}
                        />
                    }
                    subtitle={
                        <FormattedMessage
                            id="makeSpendable.subtitle"
                            defaultMessage="Where do you want to deposit?"
                        />
                    }
                    size="large"
                    align="left"
                />
                {/* FIXME @Nicvaniek - tiles */}
                <Row spacing={8}>
                    <Text>
                        <FormattedMoneyPrecise
                            money={earnAmount}
                            withSymbol
                            sign="+"
                        />
                    </Text>
                    <Text>
                        <FormattedMoneyPrecise
                            money={cardAmount}
                            withSymbol
                            sign="+"
                        />
                    </Text>
                </Row>
                <Slider
                    disabled={isLoading}
                    minValue={0}
                    maxValue={100}
                    initialValue={form.cardSplit}
                    onChange={(value) =>
                        onMsg({
                            type: 'on_slider_changed',
                            newCardSplit: value,
                        })
                    }
                    numSteps={20}
                    leftTrackColour="green30"
                    rightTrackColour="blue30"
                />
                <Actions variant="default">
                    <Button
                        loading={isLoading}
                        size="regular"
                        variant="primary"
                        onClick={() =>
                            onMsg({
                                type: 'on_form_submitted',
                                taker: form.taker,
                                cardAmount,
                            })
                        }
                    >
                        <FormattedMessage
                            id="action.deposit"
                            defaultMessage="Deposit"
                        />
                    </Button>
                </Actions>
            </Column>
        </Popup.Layout>
    )
}
