import { post } from '@zeal/api/requestBackend'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { parse as parseJSON } from '@zeal/toolkit/JSON'
import { failure, object, string, success } from '@zeal/toolkit/Result'

import { DAppSiteInfo } from '@zeal/domains/DApp'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { PredefinedNetwork } from '@zeal/domains/Network'
import {
    EthSignTypedData,
    EthSignTypedDataV3,
    EthSignTypedDataV4,
} from '@zeal/domains/RPCRequest'

import { BLOCKAID_NETWORK } from '../constants'
import {
    SmartContractBlacklistCheckPassed,
    SmartContractedBlacklistCheckFailed,
} from '../SafetyCheck'

const BLOCK_AID_RESULT_TYPE: Record<
    'Malicious' | 'Benign' | 'Warning' | 'Error',
    true
> = {
    Malicious: true,
    Benign: true,
    Warning: true,
    Error: true,
}

export const fetchBlockaidSignRequestSaftyCheck = async ({
    requestToCheck,
    network,
    dApp,
    signal,
}: {
    requestToCheck: EthSignTypedDataV4 | EthSignTypedData | EthSignTypedDataV3
    network: PredefinedNetwork
    dApp: DAppSiteInfo | null
    signal?: AbortSignal
}): Promise<
    (SmartContractedBlacklistCheckFailed | SmartContractBlacklistCheckPassed)[]
> => {
    try {
        const blockAidNetwork = BLOCKAID_NETWORK[network.name]

        if (!blockAidNetwork) {
            return []
        }

        const responseRaw = string(
            await post(
                `/proxy/ba/${blockAidNetwork}/v0/validate/json-rpc`,
                {
                    body: {
                        options: ['validation'],

                        account_address: requestToCheck.params[0],
                        metadata: dApp
                            ? { domain: dApp.hostname }
                            : { non_dapp: true },
                        data: {
                            method: requestToCheck.method,
                            params: requestToCheck.params,
                        },
                    },
                },
                signal
            )
        )
            .andThen(parseJSON)
            .andThen(object)
            .getSuccessResultOrThrow(
                'Failed to parse JSON response from blockaid signining message validation'
            )

        const resultType = object(responseRaw.validation)
            .andThen((obj) => string(obj.result_type))
            .andThen((resultType) =>
                BLOCK_AID_RESULT_TYPE[
                    resultType as keyof typeof BLOCK_AID_RESULT_TYPE
                ]
                    ? success(resultType as keyof typeof BLOCK_AID_RESULT_TYPE)
                    : failure({
                          type: 'unknown_blockaid_result_type',
                          value: resultType,
                      })
            )
            .getSuccessResultOrThrow('Failed to blockaid safety checks')

        switch (resultType) {
            case 'Benign':
            case 'Warning':
                return [
                    {
                        type: 'SmartContractBlacklistCheck',
                        severity: 'Danger',
                        state: 'Passed',
                        checkSource: { source: 'BlockAid', url: null },
                    },
                ]
            case 'Malicious':
                return [
                    {
                        type: 'SmartContractBlacklistCheck',
                        severity: 'Danger',
                        state: 'Failed',
                        checkSource: { source: 'BlockAid', url: null },
                    },
                ]

            case 'Error':
                captureError(
                    new ImperativeError('Error result type from blockaid', {
                        responseRaw,
                        requestToCheck,
                    })
                )
                return []

            default:
                return notReachable(resultType)
        }
    } catch (error) {
        captureError(error)
        return []
    }
}
