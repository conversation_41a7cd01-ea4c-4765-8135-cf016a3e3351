import * as Web3 from '@zeal/toolkit/Web3'

import { Network, NetworkRPCMap } from '@zeal/domains/Network'
import { fetchRPCBatch2 } from '@zeal/domains/RPCRequest/api/fetchRPCResponse'

import { createApprovalSpenderSafetyCheckRequest } from '../helpers/createApprovalSpenderSafetyCheckRequest'
import {
    ApprovalSpenderTypeCheckFailed,
    ApprovalSpenderTypeCheckPassed,
} from '../SafetyCheck'

export const fetchApprovalSpenderSaftyCheck = async ({
    networkRPCMap,
    network,
    signal,
    spenderAddress,
}: {
    spenderAddress: Web3.address.Address
    network: Network
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}): Promise<
    ApprovalSpenderTypeCheckPassed | ApprovalSpenderTypeCheckFailed
> => {
    const [checks] = await fetchRPCBatch2(
        [createApprovalSpenderSafetyCheckRequest({ spenderAddress })],
        {
            network,
            networkRPCMap,
            signal,
        }
    )
    return checks
}
