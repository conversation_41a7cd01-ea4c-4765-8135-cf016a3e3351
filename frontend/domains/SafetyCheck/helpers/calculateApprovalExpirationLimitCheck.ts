import { notReachable } from '@zeal/toolkit'
import { add } from '@zeal/toolkit/Date'

import { PermitAllowance } from '@zeal/domains/RPCRequest/domains/SignMessageSimulation'

import {
    ApprovalExpirationLimitCheckFailed,
    ApprovalExpirationLimitCheckPassed,
} from '../SafetyCheck'

export const calculateApprovalExpirationLimitCheck = ({
    allowance,
}: {
    allowance: PermitAllowance
}): ApprovalExpirationLimitCheckPassed | ApprovalExpirationLimitCheckFailed => {
    switch (allowance.expiration.type) {
        case 'FiniteExpiration':
            return allowance.expiration.timestamp >
                add(Date.now(), { days: 30 }).valueOf()
                ? {
                      type: 'ApprovalExpirationLimitCheck',
                      severity: 'Caution',
                      state: 'Failed',
                  }
                : {
                      type: 'ApprovalExpirationLimitCheck',
                      severity: 'Caution',
                      state: 'Passed',
                  }
        case 'InfiniteExpiration':
            return {
                type: 'ApprovalExpirationLimitCheck',
                severity: 'Caution',
                state: 'Failed',
            }
        default:
            return notReachable(allowance.expiration)
    }
}
