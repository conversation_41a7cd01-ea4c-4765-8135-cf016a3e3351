import { Range } from '@zeal/toolkit/Range'

type SafetyCheckSeverity = 'Caution' | 'Danger'

type SafetyCheckFailedState = 'Failed'
type SafetyCheckPassedState = 'Passed'

export type SafetyCheckSource = {
    source:
        | 'BlockAid'
        | 'Tenderly'
        | 'Alchemy'
        | 'DappRadar'
        | 'DefiLlama'
        | 'Zeal'
        | 'Rarible'
        | 'CoinGecko'
    url: string | null
}

export type ApprovalExpirationLimitCheckPassed = {
    type: 'ApprovalExpirationLimitCheck'
    severity: SafetyCheckSeverity
    state: SafetyCheckPassedState
}

export type ApprovalExpirationLimitCheckFailed = {
    type: 'ApprovalExpirationLimitCheck'
    severity: SafetyCheckSeverity
    state: SafetyCheckFailedState
}

export type SignMessageSafetyCheck =
    | TokenVerificationCheckFailed
    | TokenVerificationCheckPassed
    | SmartContractedBlacklistCheckFailed
    | SmartContractBlacklistCheckPassed
    | ApprovalSpenderTypeCheckPassed
    | ApprovalSpenderTypeCheckFailed
    | ApprovalExpirationLimitCheckPassed
    | ApprovalExpirationLimitCheckFailed

type TransactionSimulationCheckFailed = {
    type: 'TransactionSimulationCheck'
    message: string
    severity: SafetyCheckSeverity
    state: SafetyCheckFailedState
    checkSource: SafetyCheckSource
}

type TransactionSimulationCheckPassed = {
    type: 'TransactionSimulationCheck'
    severity: SafetyCheckSeverity
    state: SafetyCheckPassedState
    simulationUrl: string
    checkSource: SafetyCheckSource
}

export type TokenVerificationCheckPassed = {
    type: 'TokenVerificationCheck'
    severity: SafetyCheckSeverity
    state: SafetyCheckPassedState
    currencyId: string
    checkSource: SafetyCheckSource
}

export type TokenVerificationCheckFailed = {
    type: 'TokenVerificationCheck'
    severity: SafetyCheckSeverity
    state: SafetyCheckFailedState
    currencyId: string
    checkSource: SafetyCheckSource
}

export type SmartContractBlacklistCheckPassed = {
    type: 'SmartContractBlacklistCheck'
    severity: SafetyCheckSeverity
    state: SafetyCheckPassedState
    checkSource: SafetyCheckSource
}

export type SmartContractedBlacklistCheckFailed = {
    type: 'SmartContractBlacklistCheck'
    severity: SafetyCheckSeverity
    state: SafetyCheckFailedState
    checkSource: SafetyCheckSource
}

type NftCollectionCheckPassed = {
    type: 'NftCollectionCheck'
    severity: SafetyCheckSeverity
    state: SafetyCheckPassedState
    nftCollectionAddress: string
    source: string
    checkSource: SafetyCheckSource
}

export type P2pReceiverTypeCheckPassed = {
    type: 'P2pReceiverTypeCheck'
    severity: SafetyCheckSeverity
    state: SafetyCheckPassedState
}

export type P2pReceiverTypeCheckFailed = {
    type: 'P2pReceiverTypeCheck'
    severity: SafetyCheckSeverity
    state: SafetyCheckFailedState
}

export type ApprovalSpenderTypeCheckPassed = {
    type: 'ApprovalSpenderTypeCheck'
    severity: SafetyCheckSeverity
    state: SafetyCheckPassedState
}

export type ApprovalSpenderTypeCheckFailed = {
    type: 'ApprovalSpenderTypeCheck'
    severity: SafetyCheckSeverity
    state: SafetyCheckFailedState
}

type NftCollectionCheckFailed = {
    type: 'NftCollectionCheck'
    severity: SafetyCheckSeverity
    state: SafetyCheckFailedState
    nftCollectionAddress: string
    source: string
    checkSource: SafetyCheckSource
}

export type TransactionSafetyCheck =
    | TransactionSimulationCheckFailed
    | TransactionSimulationCheckPassed
    | TokenVerificationCheckFailed
    | TokenVerificationCheckPassed
    | SmartContractedBlacklistCheckFailed
    | SmartContractBlacklistCheckPassed
    | NftCollectionCheckPassed
    | NftCollectionCheckFailed
    | P2pReceiverTypeCheckPassed
    | P2pReceiverTypeCheckFailed
    | ApprovalSpenderTypeCheckPassed
    | ApprovalSpenderTypeCheckFailed

export type TransactionSimulationCheck = Extract<
    TransactionSafetyCheck,
    { type: 'TransactionSimulationCheck' }
>

export type TokenVerificationCheck = Extract<
    TransactionSafetyCheck,
    { type: 'TokenVerificationCheck' }
>

export type SmartContractBlacklistCheck = Extract<
    TransactionSafetyCheck,
    { type: 'SmartContractBlacklistCheck' }
>

export type NftCollectionCheck = Extract<
    TransactionSafetyCheck,
    { type: 'NftCollectionCheck' }
>

export type P2pReceiverTypeCheck = Extract<
    TransactionSafetyCheck,
    { type: 'P2pReceiverTypeCheck' }
>

export type ApprovalSpenderTypeCheck = Extract<
    TransactionSafetyCheck,
    { type: 'ApprovalSpenderTypeCheck' }
>

export type FailedTransactionSafetyCheck = Extract<
    TransactionSafetyCheck,
    { state: 'Failed' }
>

export type ConnectionSafetyCheck =
    | SuspiciousCharactersCheckFailed
    | SuspiciousCharactersCheckPassed
    | BlacklistCheckFailed
    | BlacklistCheckPassed
    | DAppVerificationCheckFailed
    | DAppVerificationCheckPassed

type SuspiciousCharactersCheckFailed = {
    type: 'SuspiciousCharactersCheck'
    suspiciousPart: Range
    severity: SafetyCheckSeverity
    state: SafetyCheckFailedState
}

type SuspiciousCharactersCheckPassed = {
    type: 'SuspiciousCharactersCheck'
    severity: SafetyCheckSeverity
    state: SafetyCheckPassedState
}

type DAppVerificationCheckFailed = {
    type: 'DAppVerificationCheck'
    severity: SafetyCheckSeverity
    state: SafetyCheckFailedState
    checkSource: SafetyCheckSource
}

type DAppVerificationCheckPassed = {
    type: 'DAppVerificationCheck'
    verificationUrl: string | null
    severity: SafetyCheckSeverity
    state: SafetyCheckPassedState
    source: string
    checkSource: SafetyCheckSource
}

type BlacklistCheckFailed = {
    type: 'BlacklistCheck'
    severity: SafetyCheckSeverity
    state: SafetyCheckFailedState
    checkSource: SafetyCheckSource
}

type BlacklistCheckPassed = {
    type: 'BlacklistCheck'
    severity: SafetyCheckSeverity
    state: SafetyCheckPassedState
    checkSource: SafetyCheckSource
}

export type FailedConnectionSafetyCheck = Extract<
    ConnectionSafetyCheck,
    { state: 'Failed' }
>

export type SuspiciousCharactersCheck = Extract<
    ConnectionSafetyCheck,
    { type: 'SuspiciousCharactersCheck' }
>

export type FailedSignMessageSafetyCheck = Extract<
    SignMessageSafetyCheck,
    { state: 'Failed' }
>

export type SafetyCheck =
    | ConnectionSafetyCheck
    | TransactionSafetyCheck
    | SignMessageSafetyCheck
