import { PredefinedNetwork } from '@zeal/domains/Network'

// https://docs.blockaid.io/reference/supported-chains
export const BLOCKAID_NETWORK: Record<
    PredefinedNetwork['name'],
    string | null
> = {
    Arbitrum: 'arbitrum',
    Avalanche: 'avalanche',
    BSC: 'bsc',
    Base: 'base',
    Blast: 'blast',
    Ethereum: 'ethereum',
    Gnosis: 'gnosis',
    Linea: 'linea',
    Optimism: 'optimism',
    Polygon: 'polygon',
    zkSync: 'zksync',

    Aurora: null,
    Celo: null,
    Cronos: null,
    Fantom: null,
    Manta: null,
    Mantle: null,
    OPBNB: null,
    PolygonZkevm: null,
    Sonic: null,
}
