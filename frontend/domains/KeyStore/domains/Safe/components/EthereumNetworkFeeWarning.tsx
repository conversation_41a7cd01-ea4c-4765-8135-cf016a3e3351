import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Avatar } from '@zeal/uikit/Avatar'
import { Button } from '@zeal/uikit/Button'
import { Header } from '@zeal/uikit/Header'
import { BoldDangerTriangle } from '@zeal/uikit/Icon/BoldDangerTriangle'
import { CloseCross } from '@zeal/uikit/Icon/CloseCross'
import { Ethereum } from '@zeal/uikit/Icon/Ethereum'
import { IconButton } from '@zeal/uikit/IconButton'
import { Popup } from '@zeal/uikit/Popup'

type Props = {
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | { type: 'on_ethereum_network_fee_warning_understand_clicked' }

export const EthereumNetworkFeeWarning = ({ onMsg }: Props) => {
    return (
        <Popup.Layout
            onMsg={() => {
                onMsg({
                    type: 'close',
                })
            }}
        >
            <ActionBar
                right={
                    <IconButton
                        variant="on_light"
                        onClick={() => onMsg({ type: 'close' })}
                    >
                        {({ color }) => <CloseCross size={24} color={color} />}
                    </IconButton>
                }
            />
            <Header
                icon={({ size }) => (
                    <Avatar
                        size={size}
                        leftBadge={({ size }) => (
                            <BoldDangerTriangle size={size} color="orange30" />
                        )}
                    >
                        <Ethereum size={size} />
                    </Avatar>
                )}
                title={
                    <FormattedMessage
                        id="eth-cost-warning-modal.title"
                        defaultMessage="Avoid Ethereum - network fees are high"
                    />
                }
                subtitle={
                    <FormattedMessage
                        id="eth-cost-warning-modal.subtitle"
                        defaultMessage="Smart wallets work on Ethereum, but fees are very high and we STRONGLY recommend using other networks instead."
                    />
                }
            />
            <Popup.Actions>
                <Button
                    variant="primary"
                    size="regular"
                    onClick={() => {
                        onMsg({
                            type: 'on_ethereum_network_fee_warning_understand_clicked',
                        })
                    }}
                >
                    <FormattedMessage
                        id="action.copy-address-understand"
                        defaultMessage="OK - Copy address"
                    />
                </Button>
            </Popup.Actions>
        </Popup.Layout>
    )
}
