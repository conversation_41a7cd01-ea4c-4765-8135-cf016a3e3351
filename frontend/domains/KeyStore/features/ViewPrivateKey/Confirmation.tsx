import { useState } from 'react'
import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { BannerSolid } from '@zeal/uikit/BannerSolid'
import { Button } from '@zeal/uikit/Button'
import { Clickable } from '@zeal/uikit/Clickable'
import { Header } from '@zeal/uikit/Header'
import { BoldDangerTriangle } from '@zeal/uikit/Icon/BoldDangerTriangle'
import { CloseCross } from '@zeal/uikit/Icon/CloseCross'
import { LightSquare } from '@zeal/uikit/Icon/LightSquare'
import { LightTickSquare } from '@zeal/uikit/Icon/LightTickSquare'
import { IconButton } from '@zeal/uikit/IconButton'
import { Popup } from '@zeal/uikit/Popup'
import { Text } from '@zeal/uikit/Text'

type Props = {
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | {
          type: 'continue_clicked'
      }

export const Confirmation = ({ onMsg }: Props) => {
    const [risk, setRisk] = useState<boolean>(false)
    return (
        <Popup.Layout onMsg={onMsg}>
            <ActionBar
                right={
                    <IconButton
                        variant="on_light"
                        onClick={() => onMsg({ type: 'close' })}
                    >
                        {({ color }) => <CloseCross size={24} color={color} />}
                    </IconButton>
                }
            />
            <Header
                icon={({ size }) => (
                    <BoldDangerTriangle size={size} color="statusWarning" />
                )}
                title={
                    <FormattedMessage
                        id="privateKeyConfirmation.title"
                        defaultMessage="NEVER SHARE your Private Key with anyone"
                    />
                }
            />
            <Popup.Content>
                <BannerSolid
                    rounded
                    variant="light"
                    title={
                        <Clickable
                            onClick={() => {
                                setRisk(!risk)
                            }}
                        >
                            <Text>
                                <FormattedMessage
                                    id="privateKeyConfirmation.banner.title"
                                    defaultMessage="I understand the risks"
                                />
                            </Text>
                        </Clickable>
                    }
                    subtitle={
                        <FormattedMessage
                            id="privateKeyConfirmation.banner.subtitle"
                            defaultMessage="Anyone who has your private key has access to your wallet and your funds. Only scammers ask for your Private Key."
                        />
                    }
                    right={
                        <IconButton
                            variant="on_light"
                            onClick={() => {
                                setRisk(!risk)
                            }}
                        >
                            {() =>
                                risk ? (
                                    <LightTickSquare size={20} />
                                ) : (
                                    <LightSquare size={20} />
                                )
                            }
                        </IconButton>
                    }
                />
            </Popup.Content>

            <Popup.Actions>
                <Button
                    size="regular"
                    variant="primary"
                    onClick={() => onMsg({ type: 'close' })}
                >
                    <FormattedMessage
                        id="action.cancel"
                        defaultMessage="Cancel"
                    />
                </Button>

                <Button
                    onClick={() => {
                        onMsg({ type: 'continue_clicked' })
                    }}
                    size="regular"
                    variant="warning"
                    disabled={!risk}
                >
                    <FormattedMessage
                        id="action.continue"
                        defaultMessage="Continue"
                    />
                </Button>
            </Popup.Actions>
        </Popup.Layout>
    )
}
