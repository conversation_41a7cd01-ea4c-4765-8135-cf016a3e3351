import { FormattedMessage } from 'react-intl'

import { ActionBar as UIActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { Header } from '@zeal/uikit/Header'
import { BoldDangerTriangle } from '@zeal/uikit/Icon/BoldDangerTriangle'
import { Screen } from '@zeal/uikit/Screen'
import { Spacer } from '@zeal/uikit/Spacer'

import { Account } from '@zeal/domains/Account'
import { ActionBarAccountIndicator } from '@zeal/domains/Account/components/ActionBarAccountIndicator'
import { NetworkHexId } from '@zeal/domains/Network'

type Props = {
    account: Account
    networkHexId: NetworkHexId
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'on_minimize_click' } | { type: 'on_wrong_network_accepted' }

export const NetworkNotSupported = ({
    account,
    networkHexId,
    onMsg,
}: Props) => {
    return (
        <Screen
            background="light"
            padding="form"
            onNavigateBack={() => onMsg({ type: 'on_minimize_click' })}
        >
            <Column spacing={0}>
                <UIActionBar
                    left={<ActionBarAccountIndicator account={account} />}
                    size="small"
                    right={null}
                />
            </Column>

            <Column spacing={12} fill>
                <Header
                    title={
                        <FormattedMessage
                            id="UnsupportedMobileNetworkLayout.title"
                            defaultMessage="Network is not supported on the mobile version of Zeal"
                        />
                    }
                    subtitle={
                        <FormattedMessage
                            id="UnsupportedMobileNetworkLayout.subtitle"
                            defaultMessage="You can’t make transactions or sign messages on the network with id {networkHexId} using the mobile version of Zeal yet{br}{br}Switch to the browser extension to transact on this network while we work hard on adding support 🚀"
                            values={{ networkHexId, br: '\n' }}
                        />
                    }
                    icon={({ size }) => (
                        <BoldDangerTriangle
                            size={size}
                            color="iconStatusWarning"
                        />
                    )}
                />

                <Spacer />

                <Actions variant="default">
                    <Button
                        onClick={() =>
                            onMsg({ type: 'on_wrong_network_accepted' })
                        }
                        size="regular"
                        variant="primary"
                    >
                        <FormattedMessage
                            id="UnsupportedMobileNetworkLayout.gotIt"
                            defaultMessage="Got it!"
                        />
                    </Button>
                </Actions>
            </Column>
        </Screen>
    )
}
