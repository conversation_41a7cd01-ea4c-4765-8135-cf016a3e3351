import { FormattedMessage, useIntl } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Button } from '@zeal/uikit/Button'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { IconButton } from '@zeal/uikit/IconButton'
import { QRScanner } from '@zeal/uikit/QRScanner'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { oneOf } from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account } from '@zeal/domains/Account'
import { WalletConnectPairingLink } from '@zeal/domains/DApp/domains/WalletConnect'
import { parsePairingLink } from '@zeal/domains/DApp/domains/WalletConnect/parsers/parsePairingLink'
import { captureError } from '@zeal/domains/Error/helpers/captureError'

type Props = {
    account: Account
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | { type: 'show_qr_code_clicked' }
    | { type: 'on_pairing_uri_scanned'; link: WalletConnectPairingLink }
    | {
          type: 'on_address_scanned'
          address: Web3.address.Address
          account: Account
      }

export const Layout = ({ account, onMsg }: Props) => {
    const { formatMessage } = useIntl()

    return (
        <QRScanner
            labels={{
                tryAgain: formatMessage({
                    id: 'scan_qr_code.tryAgain',
                    defaultMessage: 'Try again',
                }),
                unlockCamera: formatMessage({
                    id: 'scan_qr_code.unlockCamera',
                    defaultMessage: 'Unlock camera',
                }),
            }}
            actionBar={
                <ActionBar
                    left={
                        <IconButton
                            variant="on_color"
                            onClick={() => onMsg({ type: 'close' })}
                        >
                            {() => (
                                <BackIcon size={24} color="iconDefaultOnDark" />
                            )}
                        </IconButton>
                    }
                />
            }
            action={
                <Button
                    variant="secondary"
                    size="regular"
                    onClick={() => onMsg({ type: 'show_qr_code_clicked' })}
                >
                    <FormattedMessage
                        id="scan_qr_code.show_qr_code"
                        defaultMessage="Show my QR code"
                    />
                </Button>
            }
            onMsg={async (msg) => {
                switch (msg.type) {
                    case 'on_permissions_error':
                        captureError(msg.error)
                        break

                    case 'on_qr_code_scanned': {
                        oneOf(msg.data, [
                            parsePairingLink(msg.data).map(
                                (link) =>
                                    ({
                                        type: 'pairing_link',
                                        link,
                                    }) as const
                            ),
                            Web3.address.parse(msg.data).map(
                                (address) =>
                                    ({
                                        type: 'address',
                                        address,
                                    }) as const
                            ),
                        ]).tap((parsed) => {
                            switch (parsed.type) {
                                case 'pairing_link':
                                    onMsg({
                                        type: 'on_pairing_uri_scanned',
                                        link: parsed.link,
                                    })
                                    break
                                case 'address':
                                    onMsg({
                                        type: 'on_address_scanned',
                                        address: parsed.address,
                                        account,
                                    })
                                    break
                                default:
                                    notReachable(parsed)
                            }
                        })

                        break
                    }
                    case 'close':
                        onMsg(msg)
                        break

                    /* istanbul ignore next */
                    default:
                        notReachable(msg)
                }
            }}
        >
            <Text
                variant="paragraph"
                weight="regular"
                color="textOnDarkPrimary"
            >
                <FormattedMessage
                    id="scan_qr_code.description"
                    defaultMessage="Scan wallet QR or connect to an app"
                />
            </Text>
        </QRScanner>
    )
}
