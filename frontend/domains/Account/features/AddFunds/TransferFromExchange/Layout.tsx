import { FormattedMessage, useIntl } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Avatar } from '@zeal/uikit/Avatar'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { HeaderV2 } from '@zeal/uikit/HeaderV2'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { Copy } from '@zeal/uikit/Icon/Copy'
import { LightArrowDown2 } from '@zeal/uikit/Icon/LightArrowDown2'
import { IconButton } from '@zeal/uikit/IconButton'
import { ListItemButton } from '@zeal/uikit/ListItem'
import { Screen } from '@zeal/uikit/Screen'
import { Text } from '@zeal/uikit/Text'

import * as Web3 from '@zeal/toolkit/Web3'

import { Account } from '@zeal/domains/Account'
import { CryptoCurrency } from '@zeal/domains/Currency'
import { Avatar as CurrencyAvatar } from '@zeal/domains/Currency/components/Avatar'
import { NetworkMap } from '@zeal/domains/Network'
import { Badge } from '@zeal/domains/Network/components/Badge'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'

type Props = {
    selectedCurrency: CryptoCurrency | null
    account: Account
    networkMap: NetworkMap
    onMsg: (msg: Msg) => void
}
type Msg =
    | { type: 'transfer_from_exchange_close_clicked' }
    | { type: 'transfer_from_exchange_back_clicked' }
    | { type: 'on_copy_address_clicked'; address: Web3.address.Address }
    | { type: 'on_select_token_clicked' }

export const Layout = ({
    account,
    selectedCurrency,
    networkMap,
    onMsg,
}: Props) => {
    const { formatMessage } = useIntl()
    const address = account.address
    return (
        <Screen
            padding="form"
            background="default"
            onNavigateBack={() =>
                onMsg({ type: 'transfer_from_exchange_back_clicked' })
            }
        >
            <ActionBar
                left={
                    <IconButton
                        variant="on_light"
                        aria-label={formatMessage({
                            id: 'action.close',
                            defaultMessage: 'Close',
                        })}
                        onClick={() =>
                            onMsg({
                                type: 'transfer_from_exchange_back_clicked',
                            })
                        }
                    >
                        {({ color }) => <BackIcon size={24} color={color} />}
                    </IconButton>
                }
            />

            <Column fill shrink spacing={12}>
                <HeaderV2
                    align="left"
                    size="large"
                    title={
                        <FormattedMessage
                            id="add_funds.from_exchange.header"
                            defaultMessage="Send from exchange"
                        />
                    }
                    subtitle={null}
                />
                <ListItemButton
                    variant="outline"
                    background="surface"
                    aria-current={false}
                    avatar={({ size }) => (
                        <Avatar size={size} border="borderSecondary">
                            <Text
                                variant="callout"
                                weight="medium"
                                color="textPrimary"
                                align="center"
                            >
                                1
                            </Text>
                        </Avatar>
                    )}
                    onClick={() =>
                        onMsg({ type: 'on_copy_address_clicked', address })
                    }
                    disabled={false}
                    primaryText={
                        <FormattedMessage
                            id="add_funds.from_exchange.header.copy_wallet_address"
                            defaultMessage="Copy your Zeal address"
                        />
                    }
                    side={{
                        rightIcon: ({ size }) => (
                            <Copy size={size} color="gray20" />
                        ),
                    }}
                    shortText={Web3.address.format(address)}
                />
                <ListItemButton
                    background="surface"
                    aria-current={false}
                    variant="outline_transparent"
                    avatar={({ size }) => (
                        <Avatar size={size} border="borderSecondary">
                            <Text
                                variant="callout"
                                weight="medium"
                                color="textPrimary"
                                align="center"
                            >
                                2
                            </Text>
                        </Avatar>
                    )}
                    disabled={false}
                    primaryText={
                        <FormattedMessage
                            id="add_funds.from_exchange.header.open_exchange"
                            defaultMessage="Open exchange app or site"
                        />
                    }
                    shortText={
                        <FormattedMessage
                            id="add_funds.from_exchange.header.list_of_exchanges"
                            defaultMessage="Coinbase, Binance, etc."
                        />
                    }
                />
                {selectedCurrency ? (
                    <ListItemButton
                        variant="outline"
                        background="surface"
                        aria-current={false}
                        avatar={({ size }) => (
                            <CurrencyAvatar
                                key={selectedCurrency.id}
                                currency={selectedCurrency}
                                size={size}
                                rightBadge={({ size }) => (
                                    <Badge
                                        network={findNetworkByHexChainId(
                                            selectedCurrency.networkHexChainId,
                                            networkMap
                                        )}
                                        size={size}
                                    />
                                )}
                            />
                        )}
                        onClick={() =>
                            onMsg({ type: 'on_select_token_clicked' })
                        }
                        disabled={false}
                        primaryText={
                            <FormattedMessage
                                id="add_funds.from_exchange.header.selected_token"
                                defaultMessage="Send {token} to Zeal"
                                values={{
                                    token: selectedCurrency.code,
                                }}
                            />
                        }
                        side={{
                            rightIcon: ({ size }) => (
                                <LightArrowDown2 size={size} color="gray20" />
                            ),
                        }}
                        shortText={
                            <FormattedMessage
                                id="add_funds.from_exchange.header.selected_token.subtitle"
                                defaultMessage="On {network}"
                                values={{
                                    network: findNetworkByHexChainId(
                                        selectedCurrency.networkHexChainId,
                                        networkMap
                                    ).name,
                                }}
                            />
                        }
                    />
                ) : (
                    <ListItemButton
                        variant="outline"
                        aria-current={false}
                        background="surface"
                        avatar={({ size }) => (
                            <Avatar size={size} border="borderSecondary">
                                <Text
                                    variant="callout"
                                    weight="medium"
                                    color="textPrimary"
                                    align="center"
                                >
                                    3
                                </Text>
                            </Avatar>
                        )}
                        onClick={() =>
                            onMsg({ type: 'on_select_token_clicked' })
                        }
                        disabled={false}
                        primaryText={
                            <FormattedMessage
                                id="add_funds.from_exchange.header.send_selected_token"
                                defaultMessage="Send supported token"
                            />
                        }
                        side={{
                            rightIcon: ({ size }) => (
                                <LightArrowDown2 size={size} color="gray20" />
                            ),
                        }}
                        shortText={
                            <FormattedMessage
                                id="add_funds.from_exchange.header.send_selected_token.subtitle"
                                defaultMessage="Select supported token & network"
                            />
                        }
                    />
                )}
            </Column>
            <Actions variant="default">
                <Button
                    size="regular"
                    variant="secondary"
                    onClick={() =>
                        onMsg({ type: 'transfer_from_exchange_close_clicked' })
                    }
                >
                    <FormattedMessage
                        id="action.close"
                        defaultMessage="Close"
                    />
                </Button>
            </Actions>
        </Screen>
    )
}
