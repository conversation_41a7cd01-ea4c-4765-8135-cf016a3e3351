import { FormattedMessage } from 'react-intl'

import { Avatar } from '@zeal/uikit/Avatar'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { Header } from '@zeal/uikit/Header'
import { BoldDangerTriangle } from '@zeal/uikit/Icon/BoldDangerTriangle'
import { Popup } from '@zeal/uikit/Popup'

import { PasskeyAndroidNoCredentialAvailable } from '@zeal/domains/Error/domains/Passkey'

type Props = {
    error: PasskeyAndroidNoCredentialAvailable
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'close' }

export const NoPasskeysFound = ({ onMsg }: Props) => (
    <Popup.Layout onMsg={onMsg} background="surfaceDefault">
        <Column spacing={24}>
            <Header
                icon={({ size }) => (
                    <Avatar
                        size={72}
                        variant="round"
                        backgroundColor="backgroundLight"
                    >
                        <BoldDangerTriangle
                            size={size}
                            color="iconStatusWarning"
                        />
                    </Avatar>
                )}
                title={
                    <FormattedMessage
                        id="no-passkeys-found.modal.title"
                        defaultMessage="No Passkeys found"
                    />
                }
                subtitle={
                    <FormattedMessage
                        id="no-passkeys-found.modal.subtitle"
                        defaultMessage="We are unable to detect any Zeal Passkeys on this device. Make sure you are signed in to the cloud account that you used to create your smart wallet."
                    />
                }
            />
            <Popup.Actions>
                <Button
                    variant="primary"
                    onClick={() => onMsg({ type: 'close' })}
                    size="regular"
                >
                    <FormattedMessage
                        id="no-passkeys-found.modal.cta"
                        defaultMessage="Close"
                    />
                </Button>
            </Popup.Actions>
        </Column>
    </Popup.Layout>
)
