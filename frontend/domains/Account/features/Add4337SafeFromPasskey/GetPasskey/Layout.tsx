import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { BannerSolid } from '@zeal/uikit/BannerSolid'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { HeaderV2 } from '@zeal/uikit/HeaderV2'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { IconButton } from '@zeal/uikit/IconButton'
import { Screen } from '@zeal/uikit/Screen'
import { Spacer } from '@zeal/uikit/Spacer'

type Props = {
    onMsg: (msg: LayoutMsg) => void
}
type LayoutMsg = { type: 'close' } | { type: 'on_select_passkey_click' }

export const Layout = ({ onMsg }: Props) => (
    <Screen
        padding="form"
        background="light"
        onNavigateBack={() => onMsg({ type: 'close' })}
    >
        <ActionBar
            left={
                <IconButton
                    variant="on_light"
                    onClick={() => onMsg({ type: 'close' })}
                >
                    {({ color }) => <BackIcon size={24} color={color} />}
                </IconButton>
            }
        />
        <Column spacing={16} fill>
            <HeaderV2
                size="large"
                align="left"
                title={
                    <FormattedMessage
                        id="passkey-recovery.select-passkey.title"
                        defaultMessage="Select Passkey"
                    />
                }
                subtitle={
                    <FormattedMessage
                        id="passkey-recovery.select-passkey.subtitle"
                        defaultMessage="Select the passkey linked to your wallet to regain access."
                    />
                }
            />
            <Spacer />
            <BannerSolid
                rounded
                variant="light"
                title={
                    <FormattedMessage
                        id="passkey-recovery.select-passkey.banner.title"
                        defaultMessage="Don’t see your wallet’s passkey?"
                    />
                }
                subtitle={
                    <FormattedMessage
                        id="passkey-recovery.select-passkey.banner.subtitle"
                        defaultMessage="Make sure you’re logged in to the correct account on your device. Passkeys are account-specific."
                    />
                }
            />
            <Actions variant="default">
                <Button
                    variant="primary"
                    size="regular"
                    onClick={() => onMsg({ type: 'on_select_passkey_click' })}
                >
                    <FormattedMessage
                        id="passkey-recovery.select-passkey.continue"
                        defaultMessage="Select passkey"
                    />
                </Button>
            </Actions>
        </Column>
    </Screen>
)
