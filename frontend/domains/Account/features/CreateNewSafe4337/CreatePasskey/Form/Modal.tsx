import React from 'react'
import { FormattedMessage } from 'react-intl'

import { Avatar, Badge } from '@zeal/uikit/Avatar'
import { Button } from '@zeal/uikit/Button'
import { Group } from '@zeal/uikit/Group'
import { Header } from '@zeal/uikit/Header'
import { OnePasswordWarning } from '@zeal/uikit/Icon/1PasswordWarning'
import { BoldCrossRound } from '@zeal/uikit/Icon/BoldCrossRound'
import { BoldShieldCautionWithBorder } from '@zeal/uikit/Icon/BoldShieldCautionWithBorder'
import { BoldShieldDoneWithBorder } from '@zeal/uikit/Icon/BoldShieldDoneWithBorder'
import { BulletpointsListItem } from '@zeal/uikit/ListItem/BulletpointsListItem'
import { Popup } from '@zeal/uikit/Popup'

import { notReachable } from '@zeal/toolkit'

type Props = {
    state: State
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'close' } | { type: 'on_passkey_notice_understand_clicked' }

export type State = { type: 'closed' } | { type: 'show_passkey_notice' }

export const Modal = ({ state, onMsg }: Props) => {
    switch (state.type) {
        case 'closed':
            return null
        case 'show_passkey_notice':
            return (
                <Popup.Layout onMsg={onMsg} background="backgroundLight">
                    <Header
                        icon={({ size }) => (
                            <Avatar
                                size={72}
                                variant="round"
                                backgroundColor="backgroundLight"
                                leftBadge={({ size }) => (
                                    <Badge
                                        size={size}
                                        backgroundColor="surfaceDefault"
                                    >
                                        <BoldCrossRound
                                            size={size}
                                            color="red40"
                                        />
                                    </Badge>
                                )}
                            >
                                <OnePasswordWarning size={size} />
                            </Avatar>
                        )}
                        title={
                            <FormattedMessage
                                id="smart-wallet.passkey-notice.title"
                                defaultMessage="Avoid 1Password"
                            />
                        }
                    />

                    <Group variant="default">
                        <BulletpointsListItem
                            avatar={({ size }) => (
                                <BoldShieldCautionWithBorder
                                    size={size}
                                    color="orange30"
                                />
                            )}
                            text={
                                <FormattedMessage
                                    id="smart-wallet.passkey-notice.bulletpoint.1passwors-may-block-your-wallet"
                                    defaultMessage="1Password may block access to your wallet"
                                />
                            }
                            rightIcon={null}
                        />
                        <BulletpointsListItem
                            avatar={({ size }) => (
                                <BoldShieldDoneWithBorder
                                    size={size}
                                    color="green30"
                                />
                            )}
                            text={
                                <FormattedMessage
                                    id="smart-wallet.passkey-notice.bulletpoint.use-apple-or-google-to-setup-zeal"
                                    defaultMessage="Use Apple or Google to safely set up Zeal"
                                />
                            }
                            rightIcon={null}
                        />
                    </Group>

                    <Popup.Actions>
                        <Button
                            variant="primary"
                            onClick={() =>
                                onMsg({
                                    type: 'on_passkey_notice_understand_clicked',
                                })
                            }
                            size="regular"
                        >
                            <FormattedMessage
                                id="action.understood"
                                defaultMessage="I understand"
                            />
                        </Button>
                    </Popup.Actions>
                </Popup.Layout>
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
