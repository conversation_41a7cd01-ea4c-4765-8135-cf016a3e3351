import React from 'react'
import { FormattedMessage } from 'react-intl'

import { Column } from '@zeal/uikit/Column'
import { Group } from '@zeal/uikit/Group'
import { BoldNewWallet } from '@zeal/uikit/Icon/BoldNewWallet'
import { ForwardIcon } from '@zeal/uikit/Icon/ForwardIcon'
import { InfoCircle } from '@zeal/uikit/Icon/InfoCircle'
import { InfoCircleOutline } from '@zeal/uikit/Icon/InfoCircleOutline'
import { LightBookmark } from '@zeal/uikit/Icon/LightBookmark'
import { LightDelete } from '@zeal/uikit/Icon/LightDelete'
import { LightDocument } from '@zeal/uikit/Icon/LightDocument'
import { OutlineQRCode } from '@zeal/uikit/Icon/OutlineQRCode'
import { PasskeyApple } from '@zeal/uikit/Icon/PasskeyApple'
import { ShieldDone } from '@zeal/uikit/Icon/ShieldDone'
import { SolidInterfaceEditAlt } from '@zeal/uikit/Icon/SolidInterfaceEditAlt'
import { SolidStatusKey } from '@zeal/uikit/Icon/SolidStatusKey'
import { ListItem } from '@zeal/uikit/ListItem'

import { notReachable } from '@zeal/toolkit'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account } from '@zeal/domains/Account'
import {
    KeyStore,
    PrivateKey,
    Safe4337,
    SecretPhrase,
} from '@zeal/domains/KeyStore'
import { recoveryKitStatus } from '@zeal/domains/KeyStore/helpers/recoveryKitStatus'

type Props = {
    keystore: KeyStore
    account: Account
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'on_account_delete_click' }
    | { type: 'on_add_private_key_click' }
    | { type: 'on_zeal_smart_wallets_info_click' }
    | { type: 'on_show_secret_phrase_click'; keystore: SecretPhrase }
    | { type: 'on_backup_recovery_smart_wallet_clicked'; keystore: Safe4337 }
    | {
          type: 'on_recovery_kit_setup'
          keystore: SecretPhrase
          address: Web3.address.Address
      }
    | { type: 'on_show_private_key_click'; keystore: PrivateKey | SecretPhrase }
    | { type: 'on_edit_label_click' }
    | { type: 'on_see_qr_code_click'; keystore: KeyStore }

export const Actions = ({ keystore, account, onMsg }: Props) => {
    switch (keystore.type) {
        case 'safe_4337':
            return (
                <Column spacing={8}>
                    <Group variant="default">
                        <ShowWalletAddress
                            account={account}
                            onClick={() => {
                                onMsg({
                                    type: 'on_see_qr_code_click',
                                    keystore,
                                })
                            }}
                        />
                        <ChangeWalletLabel
                            account={account}
                            onClick={() => {
                                onMsg({ type: 'on_edit_label_click' })
                            }}
                        />
                        <ListItem
                            size="regular"
                            aria-current={false}
                            onClick={() =>
                                onMsg({
                                    type: 'on_zeal_smart_wallets_info_click',
                                })
                            }
                            avatar={({ size }) => (
                                <PasskeyApple size={size} color="iconAccent2" />
                            )}
                            primaryText={
                                <FormattedMessage
                                    id="storage.accountDetails.zealSmartWallets"
                                    defaultMessage="Zeal Smart Wallets?"
                                />
                            }
                            side={{
                                rightIcon: ({ size }) => (
                                    <InfoCircleOutline
                                        size={size}
                                        color="iconDefault"
                                    />
                                ),
                            }}
                        />
                        <ListItem
                            size="regular"
                            aria-current={false}
                            onClick={() =>
                                onMsg({
                                    type: 'on_backup_recovery_smart_wallet_clicked',
                                    keystore,
                                })
                            }
                            avatar={({ size }) => (
                                <ShieldDone size={size} color="iconAccent2" />
                            )}
                            primaryText={
                                <FormattedMessage
                                    id="storage.accountDetails.smartBackup"
                                    defaultMessage="Backup & Recovery"
                                />
                            }
                            side={{
                                rightIcon: ({ size }) => (
                                    <ForwardIcon
                                        size={size}
                                        color="iconDefault"
                                    />
                                ),
                            }}
                        />
                    </Group>
                    <DeleteGroup
                        onClick={() =>
                            onMsg({ type: 'on_account_delete_click' })
                        }
                    />
                </Column>
            )

        case 'track_only':
            return (
                <Column spacing={8}>
                    <Group variant="default">
                        <Column spacing={8}>
                            <ShowWalletAddress
                                account={account}
                                onClick={() => {
                                    onMsg({
                                        type: 'on_see_qr_code_click',
                                        keystore,
                                    })
                                }}
                            />
                            <ChangeWalletLabel
                                account={account}
                                onClick={() => {
                                    onMsg({ type: 'on_edit_label_click' })
                                }}
                            />
                            <ListItem
                                size="regular"
                                aria-current={false}
                                onClick={() =>
                                    onMsg({ type: 'on_add_private_key_click' })
                                }
                                avatar={({ size }) => (
                                    <BoldNewWallet
                                        size={size}
                                        color="iconAccent2"
                                    />
                                )}
                                primaryText={
                                    <FormattedMessage
                                        id="storage.accountDetails.activateWallet"
                                        defaultMessage="Activate wallet"
                                    />
                                }
                                side={{
                                    rightIcon: ({ size }) => (
                                        <ForwardIcon
                                            size={size}
                                            color="iconDefault"
                                        />
                                    ),
                                }}
                            />
                        </Column>
                    </Group>
                    <DeleteGroup
                        onClick={() =>
                            onMsg({ type: 'on_account_delete_click' })
                        }
                    />
                </Column>
            )

        case 'private_key_store':
            return (
                <Column spacing={8}>
                    <Group variant="default">
                        <Column spacing={8}>
                            <ShowWalletAddress
                                account={account}
                                onClick={() => {
                                    onMsg({
                                        type: 'on_see_qr_code_click',
                                        keystore,
                                    })
                                }}
                            />
                            <ChangeWalletLabel
                                account={account}
                                onClick={() => {
                                    onMsg({ type: 'on_edit_label_click' })
                                }}
                            />
                            <ShowPrivateKey
                                onClick={() =>
                                    onMsg({
                                        type: 'on_show_private_key_click',
                                        keystore,
                                    })
                                }
                            />
                        </Column>
                    </Group>
                    <DeleteGroup
                        onClick={() =>
                            onMsg({ type: 'on_account_delete_click' })
                        }
                    />
                </Column>
            )

        case 'trezor':
        case 'ledger':
            return (
                <Column spacing={8}>
                    <Group variant="default">
                        <Column spacing={8}>
                            <ShowWalletAddress
                                account={account}
                                onClick={() => {
                                    onMsg({
                                        type: 'on_see_qr_code_click',
                                        keystore,
                                    })
                                }}
                            />
                            <ChangeWalletLabel
                                account={account}
                                onClick={() => {
                                    onMsg({ type: 'on_edit_label_click' })
                                }}
                            />
                        </Column>
                    </Group>

                    <DeleteGroup
                        onClick={() =>
                            onMsg({ type: 'on_account_delete_click' })
                        }
                    />
                </Column>
            )

        case 'secret_phrase_key':
            return (
                <Column spacing={8}>
                    <Group variant="default">
                        <Column spacing={8}>
                            <ShowWalletAddress
                                account={account}
                                onClick={() => {
                                    onMsg({
                                        type: 'on_see_qr_code_click',
                                        keystore,
                                    })
                                }}
                            />
                            <ChangeWalletLabel
                                account={account}
                                onClick={() => {
                                    onMsg({ type: 'on_edit_label_click' })
                                }}
                            />
                            <ListItem
                                size="regular"
                                aria-current={false}
                                onClick={() => {
                                    onMsg({
                                        type: 'on_recovery_kit_setup',
                                        keystore,
                                        address: account.address,
                                    })
                                }}
                                avatar={({ size }) => (
                                    <InfoCircle
                                        size={size}
                                        color={(() => {
                                            const status =
                                                recoveryKitStatus(keystore)
                                            switch (status) {
                                                case 'configured':
                                                    return 'iconAccent2'
                                                case 'not_configured':
                                                    return 'iconStatusWarning'
                                                /* istanbul ignore next */
                                                default:
                                                    return notReachable(status)
                                            }
                                        })()}
                                    />
                                )}
                                primaryText={
                                    <FormattedMessage
                                        id="storage.accountDetails.setup_recovery_kit"
                                        defaultMessage="Recovery Kit"
                                    />
                                }
                                side={{
                                    rightIcon: ({ size }) => (
                                        <ForwardIcon
                                            size={size}
                                            color="iconDefault"
                                        />
                                    ),
                                }}
                            />
                            <ShowPrivateKey
                                onClick={() =>
                                    onMsg({
                                        type: 'on_show_private_key_click',
                                        keystore,
                                    })
                                }
                            />

                            <ListItem
                                size="regular"
                                aria-current={false}
                                onClick={() => {
                                    onMsg({
                                        type: 'on_show_secret_phrase_click',
                                        keystore,
                                    })
                                }}
                                avatar={({ size }) => (
                                    <LightDocument
                                        size={size}
                                        color="iconAccent2"
                                    />
                                )}
                                primaryText={
                                    <FormattedMessage
                                        id="storage.accountDetails.viewSsecretPhrase"
                                        defaultMessage="View Secret Phrase"
                                    />
                                }
                                side={{
                                    rightIcon: ({ size }) => (
                                        <ForwardIcon
                                            size={size}
                                            color="iconDefault"
                                        />
                                    ),
                                }}
                            />
                        </Column>
                    </Group>
                    <DeleteGroup
                        onClick={() =>
                            onMsg({ type: 'on_account_delete_click' })
                        }
                    />
                </Column>
            )

        /* istanbul ignore next */
        default:
            return notReachable(keystore)
    }
}

const DeleteGroup = ({ onClick }: { onClick: () => void }) => (
    <Group variant="default">
        <ListItem
            size="regular"
            aria-current={false}
            onClick={onClick}
            avatar={({ size }) => <LightDelete size={size} color="red30" />}
            primaryText={
                <FormattedMessage
                    id="storage.accountDetails.deleteWallet"
                    defaultMessage="Remove wallet"
                />
            }
        />
    </Group>
)

const ShowPrivateKey = ({ onClick }: { onClick: () => void }) => (
    <ListItem
        size="regular"
        aria-current={false}
        onClick={onClick}
        avatar={({ size }) => (
            <SolidStatusKey size={size} color="iconAccent2" />
        )}
        primaryText={
            <FormattedMessage
                id="storage.accountDetails.showPrivateKey"
                defaultMessage="Show Private Key"
            />
        }
        side={{
            rightIcon: ({ size }) => (
                <ForwardIcon size={size} color="iconDefault" />
            ),
        }}
    />
)

const ChangeWalletLabel = ({
    onClick,
    account,
}: {
    onClick: () => void
    account: Account
}) => (
    <ListItem
        size="regular"
        aria-current={false}
        onClick={onClick}
        avatar={({ size }) => <LightBookmark size={size} color="iconAccent2" />}
        primaryText={
            <FormattedMessage
                id="storage.accountDetails.changeWalletLabel"
                defaultMessage="Change wallet label"
            />
        }
        shortText={account.label}
        side={{
            rightIcon: ({ size }) => (
                <SolidInterfaceEditAlt size={size} color="iconDefault" />
            ),
        }}
    />
)

const ShowWalletAddress = ({
    onClick,
    account,
}: {
    onClick: () => void
    account: Account
}) => (
    <ListItem
        size="regular"
        aria-current={false}
        onClick={onClick}
        avatar={({ size }) => <OutlineQRCode size={size} color="iconAccent2" />}
        primaryText={
            <FormattedMessage
                id="storage.accountDetails.showWalletAddress"
                defaultMessage="Show wallet address"
            />
        }
        shortText={Web3.address.format(account.address)}
        side={{
            rightIcon: ({ size }) => (
                <ForwardIcon size={size} color="iconDefault" />
            ),
        }}
    />
)
