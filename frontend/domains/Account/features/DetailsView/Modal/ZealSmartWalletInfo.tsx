import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Column } from '@zeal/uikit/Column'
import { Group } from '@zeal/uikit/Group'
import { Header } from '@zeal/uikit/Header'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { BoldGas } from '@zeal/uikit/Icon/BoldGas'
import { CloudCheck } from '@zeal/uikit/Icon/CloudCheck'
import { ShieldDone } from '@zeal/uikit/Icon/ShieldDone'
import { IconButton } from '@zeal/uikit/IconButton'
import { BulletpointsListItem } from '@zeal/uikit/ListItem/BulletpointsListItem'
import { Screen } from '@zeal/uikit/Screen'

type Props = {
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'close' }

export const ZealSmartWalletInfo = ({ onMsg }: Props) => {
    return (
        <Screen
            background="light"
            padding="form"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <ActionBar
                left={
                    <IconButton
                        variant="on_light"
                        onClick={() => onMsg({ type: 'close' })}
                    >
                        {({ color }) => <BackIcon size={24} color={color} />}
                    </IconButton>
                }
            />
            <Column spacing={24}>
                <Header
                    title={
                        <FormattedMessage
                            id="zealSmartWalletInfo.title"
                            defaultMessage="About Zeal smart wallets"
                        />
                    }
                />

                <Group variant="default">
                    <BulletpointsListItem
                        avatar={({ size }) => (
                            <CloudCheck size={size} color="green30" />
                        )}
                        text={
                            <FormattedMessage
                                id="zealSmartWalletInfo.recover"
                                defaultMessage="No Seed Phrases; Recover using biometric passkey from your password manager, iCloud or Google account."
                            />
                        }
                        rightIcon={null}
                    />
                    <BulletpointsListItem
                        avatar={({ size }) => (
                            <ShieldDone size={size} color="blue30" />
                        )}
                        text={
                            <FormattedMessage
                                id="zealSmartWalletInfo.selfCustodial"
                                defaultMessage="Completely self-custodial; Passkey signatures are validated onchain to minimise central dependencies."
                            />
                        }
                        rightIcon={null}
                    />
                    <BulletpointsListItem
                        avatar={({ size }) => (
                            <BoldGas size={size} color="purple50" />
                        )}
                        text={
                            <FormattedMessage
                                id="zealSmartWalletInfo.gas"
                                defaultMessage="Pay gas with many tokens; use popular ERC20 tokens on supported chains to pay for network fees, not just native tokens"
                            />
                        }
                        rightIcon={null}
                    />
                </Group>
            </Column>
        </Screen>
    )
}
