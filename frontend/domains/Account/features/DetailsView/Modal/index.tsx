import React from 'react'
import { FormattedMessage } from 'react-intl'

import { But<PERSON> } from '@zeal/uikit/Button'
import { Header } from '@zeal/uikit/Header'
import { ShieldFail } from '@zeal/uikit/Icon/ShieldFail'
import { Modal as UIModal } from '@zeal/uikit/Modal'
import { Popup } from '@zeal/uikit/Popup'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { EditLabel } from '@zeal/domains/Account/domains/Label/components/EditLabel'
import { ShowAndScanQRCode } from '@zeal/domains/Account/features/ShowAndScanQRCode'
import { UserAConfiguredReferralConfig } from '@zeal/domains/Card/domains/Reward'
import {
    KeyStore,
    PrivateKey,
    Safe4337,
    SecretPhrase,
} from '@zeal/domains/KeyStore'
import { ViewPrivateKey } from '@zeal/domains/KeyStore/features/ViewPrivateKey'
import { ViewSecretPhrase } from '@zeal/domains/KeyStore/features/ViewSecretPhrase'
import { NetworkMap } from '@zeal/domains/Network'

import { BackupRecoverySmartWallet } from './BackupRecoverySmartWallet'
import { RewardsAccessWarning } from './RewardsAccessWarning'
import { ZealSmartWalletInfo } from './ZealSmartWalletInfo'

type Props = {
    installationId: string
    accounts: AccountsMap
    account: Account
    encryptedPassword: string
    state: State
    networkMap: NetworkMap
    isEthereumNetworkFeeWarningSeen: boolean
    onMsg: (msg: Msg) => void
}

export type State =
    | { type: 'closed' }
    | { type: 'edit_label'; keystore: KeyStore }
    | { type: 'show_secret_phrase'; keystore: SecretPhrase }
    | {
          type: 'show_private_key'
          keystore: PrivateKey | SecretPhrase | Safe4337
      }
    | { type: 'show_qr_code'; keystore: KeyStore }
    | { type: 'backup_recovery_smart_wallet'; keystore: Safe4337 }
    | { type: 'confirm_delete' }
    | { type: 'zeal_smart_wallets_info' }
    | {
          type: 'account_delete_rewards_warning'
          userAReferralConfig: UserAConfiguredReferralConfig
      }

export type Msg =
    | { type: 'close' }
    | MsgOf<typeof EditLabel>
    | MsgOf<typeof BackupRecoverySmartWallet>
    | MsgOf<typeof ShowAndScanQRCode>
    | MsgOf<typeof RewardsAccessWarning>
    | { type: 'confirm_account_delete_click'; account: Account }

export const Modal = ({
    state,
    account,
    accounts,
    encryptedPassword,
    networkMap,
    isEthereumNetworkFeeWarningSeen,
    installationId,
    onMsg,
}: Props) => {
    switch (state.type) {
        case 'closed':
            return null
        case 'backup_recovery_smart_wallet':
            return (
                <UIModal>
                    <BackupRecoverySmartWallet
                        keystore={state.keystore}
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        case 'edit_label':
            return (
                <EditLabel
                    accounts={accounts}
                    account={account}
                    keystore={state.keystore}
                    onMsg={onMsg}
                />
            )
        case 'zeal_smart_wallets_info':
            return (
                <UIModal>
                    <ZealSmartWalletInfo onMsg={onMsg} />
                </UIModal>
            )

        case 'show_private_key':
            return (
                <ViewPrivateKey
                    installationId={installationId}
                    encryptedPassword={encryptedPassword}
                    keystore={state.keystore}
                    onMsg={onMsg}
                />
            )

        case 'show_secret_phrase':
            return (
                <ViewSecretPhrase
                    installationId={installationId}
                    encryptedPassword={encryptedPassword}
                    keystore={state.keystore}
                    onMsg={onMsg}
                />
            )

        case 'show_qr_code':
            return (
                <UIModal>
                    <ShowAndScanQRCode
                        keyStore={state.keystore}
                        networkMap={networkMap}
                        installationId={installationId}
                        account={account}
                        isEthereumNetworkFeeWarningSeen={
                            isEthereumNetworkFeeWarningSeen
                        }
                        accountMap={accounts}
                        initialState={{ type: 'show_qr_code' }}
                        onMsg={onMsg}
                    />
                </UIModal>
            )

        case 'confirm_delete':
            return (
                <Popup.Layout onMsg={onMsg}>
                    <Header
                        icon={({ size }) => (
                            <ShieldFail size={size} color="statusCritical" />
                        )}
                        title={
                            <FormattedMessage
                                id="walletDeleteConfirm.title"
                                defaultMessage="Remove wallet?"
                            />
                        }
                        subtitle={
                            <FormattedMessage
                                id="walletDeleteConfirm.subtitle"
                                defaultMessage="You’ll have to import again to view your portfolio or make transactions"
                            />
                        }
                    />
                    <Popup.Actions>
                        <Button
                            variant="primary"
                            size="regular"
                            onClick={() => {
                                onMsg({ type: 'close' })
                            }}
                        >
                            <FormattedMessage
                                id="action.cancel"
                                defaultMessage="Cancel"
                            />
                        </Button>
                        <Button
                            variant="secondary"
                            size="regular"
                            onClick={() =>
                                onMsg({
                                    type: 'confirm_account_delete_click',
                                    account,
                                })
                            }
                        >
                            <FormattedMessage
                                id="walletDeleteConfirm.main_action"
                                defaultMessage="Remove"
                            />
                        </Button>
                    </Popup.Actions>
                </Popup.Layout>
            )

        case 'account_delete_rewards_warning':
            return (
                <UIModal>
                    <RewardsAccessWarning
                        userAReferralConfig={state.userAReferralConfig}
                        account={account}
                        onMsg={onMsg}
                    />
                </UIModal>
            )

        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
