import React from 'react'
import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Avatar, Badge } from '@zeal/uikit/Avatar'
import { Column } from '@zeal/uikit/Column'
import { Group, GroupHeader, Section } from '@zeal/uikit/Group'
import { CloseCross } from '@zeal/uikit/Icon/Actions/CloseCross'
import { BoldAdd } from '@zeal/uikit/Icon/BoldAdd'
import { EyeOutline } from '@zeal/uikit/Icon/EyeOutline'
import { FaceIdLogo } from '@zeal/uikit/Icon/FaceIdLogo'
import { HardwareWallets } from '@zeal/uikit/Icon/HardwareWallets'
import { ImportWallets } from '@zeal/uikit/Icon/ImportWallets'
import { LightDocument } from '@zeal/uikit/Icon/LightDocument'
import { IconButton } from '@zeal/uikit/IconButton'
import { ListItem } from '@zeal/uikit/ListItem'
import { Popup } from '@zeal/uikit/Popup'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { ZealPlatform } from '@zeal/toolkit/OS/ZealPlatform'

type Props = {
    onMsg: (msg: Msg) => void
}

export type Msg =
    | { type: 'close' }
    | { type: 'track_wallet_clicked' }
    | { type: 'add_wallet_clicked' }
    | { type: 'create_clicked' } // TODO :: @resetko check naming
    | { type: 'hardware_wallet_clicked' }
    | { type: 'safe_wallet_clicked' }
    | { type: 'recover_safe_wallet_clicked' }

export const SelectTypeOfAccountToAdd = ({ onMsg }: Props) => {
    return (
        <Popup.Layout onMsg={onMsg}>
            <Popup.Content>
                <Column spacing={8}>
                    <ActionBar
                        left={
                            <ActionBar.Header>
                                <FormattedMessage
                                    id="account.select_type_of_account.header"
                                    defaultMessage="Add wallet"
                                />
                            </ActionBar.Header>
                        }
                        right={
                            <IconButton
                                variant="on_light"
                                onClick={() => onMsg({ type: 'close' })}
                            >
                                {({ color }) => (
                                    <CloseCross size={24} color={color} />
                                )}
                            </IconButton>
                        }
                    />
                    <Section>
                        <Group variant="default">
                            <ListItem
                                size="large"
                                aria-current={false}
                                avatar={({ size }) => (
                                    <Avatar
                                        size={size}
                                        leftBadge={({ size: badgeSize }) => (
                                            <Badge
                                                size={badgeSize}
                                                backgroundColor="teal40"
                                                outlineColor="surfaceDefault"
                                            >
                                                <BoldAdd
                                                    size={badgeSize}
                                                    color="surfaceLight"
                                                />
                                            </Badge>
                                        )}
                                    >
                                        <FaceIdLogo
                                            size={size}
                                            color="teal40"
                                        />
                                    </Avatar>
                                )}
                                primaryText={
                                    <FormattedMessage
                                        id="account.select_type_of_account.create_safe_wallet.title"
                                        defaultMessage="Create Smart wallet"
                                    />
                                }
                                onClick={() =>
                                    onMsg({ type: 'safe_wallet_clicked' })
                                }
                            />
                            <ListItem
                                size="large"
                                aria-current={false}
                                avatar={({ size }) => (
                                    <Avatar
                                        size={size}
                                        leftBadge={({ size: badgeSize }) => (
                                            <Badge
                                                size={badgeSize}
                                                backgroundColor="teal40"
                                                outlineColor="surfaceDefault"
                                            >
                                                <BoldAdd
                                                    size={badgeSize}
                                                    color="surfaceLight"
                                                />
                                            </Badge>
                                        )}
                                    >
                                        <LightDocument
                                            size={size}
                                            color="iconAccent2"
                                        />
                                    </Avatar>
                                )}
                                primaryText={
                                    <FormattedMessage
                                        id="account.select_type_of_account.create_eoa.title"
                                        defaultMessage="Create Seed phrase wallet"
                                    />
                                }
                                shortText={
                                    <FormattedMessage
                                        id="account.select_type_of_account.create_eoa.short"
                                        defaultMessage="Legacy wallet for experts"
                                    />
                                }
                                onClick={() =>
                                    onMsg({ type: 'create_clicked' })
                                }
                            />
                        </Group>
                    </Section>
                    <Section>
                        <Group variant="default">
                            <GroupHeader
                                left={({ color, textVariant, textWeight }) => (
                                    <Text
                                        color={color}
                                        variant={textVariant}
                                        weight={textWeight}
                                    >
                                        <FormattedMessage
                                            id="add-account.section.import.header"
                                            defaultMessage="Import"
                                        />
                                    </Text>
                                )}
                                right={null}
                            />
                            <ListItem
                                size="large"
                                aria-current={false}
                                avatar={({ size }) => (
                                    <FaceIdLogo size={size} color="teal40" />
                                )}
                                primaryText={
                                    <FormattedMessage
                                        id="account.select_type_of_account.existing_smart_wallet"
                                        defaultMessage="Existing Smart wallet"
                                    />
                                }
                                onClick={() =>
                                    onMsg({
                                        type: 'recover_safe_wallet_clicked',
                                    })
                                }
                            />

                            {(() => {
                                switch (ZealPlatform.OS) {
                                    case 'ios':
                                    case 'android':
                                        return null
                                    case 'web':
                                        return (
                                            <ListItem
                                                size="large"
                                                aria-current={false}
                                                avatar={({ size }) => (
                                                    <HardwareWallets
                                                        size={size}
                                                    />
                                                )}
                                                primaryText={
                                                    <FormattedMessage
                                                        id="account.select_type_of_account.hardware_wallet"
                                                        defaultMessage="Hardware wallet"
                                                    />
                                                }
                                                onClick={() =>
                                                    onMsg({
                                                        type: 'hardware_wallet_clicked',
                                                    })
                                                }
                                            />
                                        )
                                    /* istanbul ignore next */
                                    default:
                                        return notReachable(ZealPlatform)
                                }
                            })()}

                            <ListItem
                                size="large"
                                aria-current={false}
                                avatar={({ size }) => (
                                    <EyeOutline
                                        size={size}
                                        color="iconAccent2"
                                    />
                                )}
                                primaryText={
                                    <FormattedMessage
                                        id="account.select_type_of_account.read_only_wallet"
                                        defaultMessage="Read-only wallet"
                                    />
                                }
                                shortText={
                                    <FormattedMessage
                                        id="account.select_type_of_account.read_only_wallet.short"
                                        defaultMessage="Preview any portfolio"
                                    />
                                }
                                onClick={() =>
                                    onMsg({ type: 'track_wallet_clicked' })
                                }
                            />

                            <ListItem
                                size="large"
                                aria-current={false}
                                avatar={({ size }) => (
                                    <ImportWallets size={size} />
                                )}
                                primaryText={
                                    <FormattedMessage
                                        id="account.select_type_of_account.private_key_or_seed_phraze_wallet"
                                        defaultMessage="Private key / Seed phrase"
                                    />
                                }
                                onClick={() =>
                                    onMsg({ type: 'add_wallet_clicked' })
                                }
                            />
                        </Group>
                    </Section>
                </Column>
            </Popup.Content>
        </Popup.Layout>
    )
}
