import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import * as Web3 from '@zeal/toolkit/Web3'

export type SubmitedTransaction =
    | SubmitedTransactionQueued
    | SubmitedTransactionIncludedInBlock
    | SubmitedTransactionCompleted
    | SubmitedTransactionFailed
    | SubmitedTransactionReplaced

export type SubmitedTransactionQueued = {
    hash: Hexadecimal.Hexadecimal
    submittedNonce: number
    senderAddress: Web3.address.Address
    state: 'queued'
    queuedAt: number
}

export type SubmitedTransactionReplaced = {
    hash: Hexadecimal.Hexadecimal
    submittedNonce: number
    senderAddress: Web3.address.Address
    transactionCount: number
    state: 'replaced'
    queuedAt: number
}

export type SubmitedTransactionIncludedInBlock = {
    hash: Hexadecimal.Hexadecimal
    submittedNonce: number
    senderAddress: Web3.address.Address
    state: 'included_in_block'
    queuedAt: number
    gasInfo: GasInfo
}

export type SubmitedTransactionCompleted = {
    hash: Hexadecimal.Hexadecimal
    submittedNonce: number
    senderAddress: Web3.address.Address
    state: 'completed'
    queuedAt: number
    completedAt: number
    gasInfo: GasInfo
}

export type SubmitedTransactionFailed = {
    hash: Hexadecimal.Hexadecimal
    submittedNonce: number
    senderAddress: Web3.address.Address
    state: 'failed'
    queuedAt: number
    failedAt: number
    gasInfo: GasInfo
}

// TODO @resetko-zeal do we really need this raw type, or we can just get ready to use fee from receipt?
export type GasInfo =
    | { type: 'generic'; gasUsed: bigint; effectiveGasPrice: bigint }
    | {
          type: 'l2_rollup'
          l1Fee: bigint
          gasUsed: bigint
          l2GasPrice: bigint
      }
