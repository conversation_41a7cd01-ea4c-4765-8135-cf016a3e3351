import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { generateRandomNumber } from '@zeal/toolkit/Number'
import {
    match,
    object,
    oneOf,
    Result,
    shape,
    string,
    success,
} from '@zeal/toolkit/Result'

import { SAFE_4337_MODULE_ENTRYPOINT_ADDRESS } from '@zeal/domains/Address/constants'
import { RPCError } from '@zeal/domains/Error/domains/RPCError'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import {
    Network,
    NetworkRPCMap,
    PredefinedNetwork,
    TestNetwork,
} from '@zeal/domains/Network'
import { fetchLogs } from '@zeal/domains/RPCRequest/api/fetchLogs'
import {
    SubmittedUserOperation,
    SubmittedUserOperationBundled,
    SubmittedUserOperationCompleted,
    SubmittedUserOperationFailed,
    SubmittedUserOperationPending,
    SubmittedUserOperationRejected,
} from '@zeal/domains/TransactionRequest/domains/SubmittedUserOperation'
import { parseLog } from '@zeal/domains/Transactions/helpers/parseLog'
import {
    fetchBiconomyBundlerResponseWithRetries,
    fetchPimlicoBundlerResponseWithRetries,
} from '@zeal/domains/UserOperation/api/fetchBundlerResponse'
import { USER_OPERATION_EVENT_SIGNATURE } from '@zeal/domains/UserOperation/constants'

const PENDING_TRANSACTION_TTL_MS = 60_000

export const fetchSubmittedUserOperation = async ({
    submittedUserOperation,
    network,
    networkRPCMap,
    signal,
}: {
    submittedUserOperation: SubmittedUserOperation
    network: Network
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}): Promise<SubmittedUserOperation> => {
    switch (network.type) {
        case 'predefined':
        case 'testnet': {
            const userOperationOnChainStatus =
                await fetchSubmittedUserOperationOnChainStatus({
                    submittedUserOperation,
                    network,
                    networkRPCMap,
                    signal,
                })

            switch (userOperationOnChainStatus.type) {
                case 'success':
                    return {
                        state: 'completed' as const,
                        userOperationHash:
                            submittedUserOperation.userOperationHash,
                        sender: submittedUserOperation.sender,
                        queuedAt: submittedUserOperation.queuedAt,
                        type: submittedUserOperation.type,
                        submittedBlock: submittedUserOperation.submittedBlock,
                        bundleTransactionHash:
                            userOperationOnChainStatus.transactionHash,
                        actionSource: submittedUserOperation.actionSource,
                        completedAt: Date.now(),
                    }
                case 'failure':
                    return {
                        state: 'failed' as const,
                        userOperationHash:
                            submittedUserOperation.userOperationHash,
                        sender: submittedUserOperation.sender,
                        queuedAt: submittedUserOperation.queuedAt,
                        bundleTransactionHash:
                            userOperationOnChainStatus.transactionHash,
                        message: 'User operation failed',
                        failedAt: Date.now(),
                        type: submittedUserOperation.type,
                        submittedBlock: submittedUserOperation.submittedBlock,
                        actionSource: submittedUserOperation.actionSource,
                    }
                case 'unknown_block_limit_exceeded': {
                    const submittedUserOperationFromBundler =
                        await fetchSubmittedUserOperationFromBundler({
                            submittedUserOperation,
                            network,
                            signal,
                        })

                    switch (submittedUserOperationFromBundler.state) {
                        case 'pending':
                            captureError(
                                new ImperativeError(
                                    'Pending user operation exceeded rpc block range limit',
                                    {
                                        submittedUserOperationFromBundler,
                                    }
                                )
                            )
                            return {
                                state: 'rejected' as const,
                                userOperationHash:
                                    submittedUserOperation.userOperationHash,
                                sender: submittedUserOperation.sender,
                                queuedAt: submittedUserOperation.queuedAt,
                                type: submittedUserOperation.type,
                                rejectedAt: Date.now(),
                                submittedBlock:
                                    submittedUserOperation.submittedBlock,
                                actionSource:
                                    submittedUserOperation.actionSource,
                            }
                        case 'bundled':
                        case 'completed':
                        case 'rejected':
                        case 'failed':
                            return submittedUserOperationFromBundler
                        /* istanbul ignore next */
                        default:
                            return notReachable(
                                submittedUserOperationFromBundler
                            )
                    }
                }
                case 'not_found': {
                    if (
                        Date.now() - submittedUserOperation.queuedAt <
                        PENDING_TRANSACTION_TTL_MS
                    ) {
                        return {
                            state: 'pending' as const,
                            userOperationHash:
                                submittedUserOperation.userOperationHash,
                            sender: submittedUserOperation.sender,
                            queuedAt: submittedUserOperation.queuedAt,
                            type: submittedUserOperation.type,
                            submittedBlock:
                                submittedUserOperation.submittedBlock,
                            actionSource: submittedUserOperation.actionSource,
                        }
                    }

                    const submittedUserOperationFromBundler =
                        await fetchSubmittedUserOperationFromBundler({
                            submittedUserOperation,
                            network,
                            signal,
                        })

                    return submittedUserOperationFromBundler
                }

                /* istanbul ignore next */
                default:
                    return notReachable(userOperationOnChainStatus)
            }
        }

        case 'custom':
            throw new ImperativeError(
                'Cannot fetch submitted user operation on custom network'
            )

        /* istanbul ignore next */
        default:
            return notReachable(network)
    }
}

type SubmittedUserOperationOnChainStatus =
    | {
          type: 'success'
          transactionHash: Hexadecimal.Hexadecimal
      }
    | {
          type: 'failure'
          transactionHash: Hexadecimal.Hexadecimal
      }
    | {
          type: 'not_found'
      }
    | {
          type: 'unknown_block_limit_exceeded'
          rpcError: Extract<
              RPCError['type'],
              'rpc_error_block_range_limit_exceeded'
          >
      }

const fetchSubmittedUserOperationOnChainStatus = async ({
    submittedUserOperation,
    network,
    networkRPCMap,
    signal,
}: {
    submittedUserOperation: SubmittedUserOperation
    network: PredefinedNetwork | TestNetwork
    networkRPCMap: NetworkRPCMap
    signal?: AbortSignal
}): Promise<SubmittedUserOperationOnChainStatus> => {
    try {
        const logs = await fetchLogs({
            address: SAFE_4337_MODULE_ENTRYPOINT_ADDRESS,
            topics: [
                USER_OPERATION_EVENT_SIGNATURE,
                submittedUserOperation.userOperationHash,
            ],
            fromBlock: submittedUserOperation.submittedBlock,
            toBlock: 'latest',
            network,
            networkRPCMap,
            signal,
        })

        if (logs.length > 1) {
            throw new ImperativeError('Multiple UserOperationEventLogs found', {
                userOperationHash: submittedUserOperation.userOperationHash,
                submittedBlock: submittedUserOperation.submittedBlock,
            })
        }

        if (logs.length === 0) {
            return {
                type: 'not_found' as const,
            }
        }

        const parsedLog = parseLog(logs[0], network).getSuccessResultOrThrow(
            'Failed to parse UserOperationEvent log'
        )

        switch (parsedLog.type) {
            case 'user_operation_event':
                return parsedLog.success
                    ? {
                          type: 'success' as const,
                          transactionHash: logs[0].transactionHash || '0x',
                      }
                    : {
                          type: 'failure' as const,
                          transactionHash: logs[0].transactionHash || '0x',
                      }
            case 'unknown':
            case 'account_deployed':
            case 'added_owner':
            case 'approval':
            case 'disable_module':
            case 'enable_module':
            case 'user_operation_revert_reason':
            case 'safe_module_transaction':
            case 'safe_received':
            case 'set_allowance':
            case 'threshold_updated':
            case 'erc20_transfer':
            case 'native_wrapper_deposit':
            case 'native_wrapper_withdraw':
            case 'safe_module_transaction_for_native_fee_payment':
                throw new ImperativeError(
                    'Got impossible log when looking for UserOperationEvent',
                    {
                        userOperationHash:
                            submittedUserOperation.userOperationHash,
                        submittedBlock: submittedUserOperation.submittedBlock,
                    }
                )
            default:
                return notReachable(parsedLog)
        }
    } catch (error) {
        const appError = parseAppError(error)
        switch (appError.type) {
            case 'rpc_error_block_range_limit_exceeded':
                return {
                    type: 'unknown_block_limit_exceeded' as const,
                    rpcError: appError.type,
                }
            default:
                throw error
        }
    }
}

const fetchSubmittedUserOperationFromBundler = async ({
    submittedUserOperation,
    network,
    signal,
}: {
    submittedUserOperation: SubmittedUserOperation
    network: PredefinedNetwork | TestNetwork
    signal?: AbortSignal
}): Promise<SubmittedUserOperation> => {
    switch (submittedUserOperation.type) {
        case 'user_operation':
            return await fetchBiconomyBundlerResponseWithRetries({
                network,
                signal,
                actionSource: submittedUserOperation.actionSource,
                request: {
                    id: generateRandomNumber(),
                    jsonrpc: '2.0',
                    method: 'biconomy_getUserOperationStatus',
                    params: [submittedUserOperation.userOperationHash],
                },
            }).then((response) =>
                parse(response, submittedUserOperation).getSuccessResultOrThrow(
                    'Failed to parse submitted user operation status'
                )
            )
        case 'boosted_user_operation':
            return await fetchPimlicoBundlerResponseWithRetries({
                network,
                signal,
                actionSource: submittedUserOperation.actionSource,
                request: {
                    id: generateRandomNumber(),
                    jsonrpc: '2.0',
                    method: 'pimlico_getUserOperationStatus',
                    params: [submittedUserOperation.userOperationHash],
                },
            }).then((response) =>
                parse(response, submittedUserOperation).getSuccessResultOrThrow(
                    'Failed to parse pimlico submitted user operation status'
                )
            )
        /* istanbul ignore next */
        default:
            return notReachable(submittedUserOperation.type)
    }
}

const parsePending = (
    input: unknown,
    submittedUserOperation: SubmittedUserOperation
): Result<unknown, SubmittedUserOperationPending> =>
    object(input).andThen((obj) =>
        shape({
            state: oneOf(obj, [
                match(obj.state, 'BUNDLER_MEMPOOL'), // biconomy
                match(obj.status, 'not_submitted'), // pimlico
                match(obj.status, 'queued'), // pimlico
            ]).map(() => 'pending' as const),
            userOperationHash: success(
                submittedUserOperation.userOperationHash
            ),
            sender: success(submittedUserOperation.sender),
            queuedAt: success(submittedUserOperation.queuedAt),
            type: success(submittedUserOperation.type),
            submittedBlock: success(submittedUserOperation.submittedBlock),
            actionSource: success(submittedUserOperation.actionSource),
        })
    )

const parseBundled = (
    input: unknown,
    submittedUserOperation: SubmittedUserOperation
): Result<unknown, SubmittedUserOperationBundled> =>
    object(input).andThen((obj) =>
        shape({
            state: oneOf(obj, [
                match(obj.state, 'SUBMITTED'), // biconomy
                match(obj.status, 'submitted'), // pimlico
            ]).map(() => 'bundled' as const),
            userOperationHash: success(
                submittedUserOperation.userOperationHash
            ),
            sender: success(submittedUserOperation.sender),
            queuedAt: success(submittedUserOperation.queuedAt),
            bundleTransactionHash: Hexadecimal.parse(obj.transactionHash),
            type: success(submittedUserOperation.type),
            submittedBlock: success(submittedUserOperation.submittedBlock),
            actionSource: success(submittedUserOperation.actionSource),
        })
    )

const parseCompleted = (
    input: unknown,
    submittedUserOperation: SubmittedUserOperation
): Result<unknown, SubmittedUserOperationCompleted> =>
    object(input).andThen((obj) =>
        oneOf(obj, [
            shape({
                state: match(obj.state, 'CONFIRMED'),
                userOperationReceipt: object(obj.userOperationReceipt).andThen(
                    (userOpReceiptObj) =>
                        shape({
                            success: match(userOpReceiptObj.success, 'true'),
                            receipt: object(userOpReceiptObj.receipt).andThen(
                                (receiptObj) =>
                                    shape({
                                        transactionHash: Hexadecimal.parse(
                                            receiptObj.transactionHash
                                        ),
                                    })
                            ),
                        })
                ),
            }).map((result) => ({
                type: submittedUserOperation.type,
                state: 'completed' as const,
                userOperationHash: submittedUserOperation.userOperationHash,
                sender: submittedUserOperation.sender,
                queuedAt: submittedUserOperation.queuedAt,
                bundleTransactionHash:
                    result.userOperationReceipt.receipt.transactionHash,
                submittedBlock: submittedUserOperation.submittedBlock,
                actionSource: submittedUserOperation.actionSource,
                completedAt: Date.now(),
            })),
            shape({
                state: match(obj.status, 'included').map(
                    () => 'completed' as const
                ),
                type: success(submittedUserOperation.type),
                userOperationHash: success(
                    submittedUserOperation.userOperationHash
                ),
                sender: success(submittedUserOperation.sender),
                queuedAt: success(submittedUserOperation.queuedAt),
                bundleTransactionHash: Hexadecimal.parse(obj.transactionHash),
                submittedBlock: success(submittedUserOperation.submittedBlock),
                actionSource: success(submittedUserOperation.actionSource),
                completedAt: success(Date.now()),
            }),
        ])
    )

const parseRejected = (
    input: unknown,
    submittedUserOperation: SubmittedUserOperation
): Result<unknown, SubmittedUserOperationRejected> =>
    object(input).andThen((obj) =>
        shape({
            state: oneOf(obj, [
                match(obj.state, 'DROPPED_FROM_BUNDLER_MEMPOOL'), // biconomy
                match(obj.status, 'rejected'), // pimlico
            ]).map(() => 'rejected' as const),
            userOperationHash: success(
                submittedUserOperation.userOperationHash
            ),
            sender: success(submittedUserOperation.sender),
            queuedAt: success(submittedUserOperation.queuedAt),
            rejectedAt: success(Date.now()),
            submittedBlock: success(submittedUserOperation.submittedBlock),
            actionSource: success(submittedUserOperation.actionSource),
            type: success(submittedUserOperation.type),
        })
    )

const parseBundleTransactionFailed = (
    input: unknown,
    submittedUserOperation: SubmittedUserOperation
): Result<unknown, SubmittedUserOperationFailed> =>
    object(input).andThen((obj) =>
        shape({
            state: match(obj.state, 'FAILED').map(() => 'failed' as const),
            userOperationHash: success(
                submittedUserOperation.userOperationHash
            ),
            sender: success(submittedUserOperation.sender),
            queuedAt: success(submittedUserOperation.queuedAt),
            bundleTransactionHash: Hexadecimal.parse(obj.transactionHash),
            message: string(obj.message),
            failedAt: success(Date.now()),
            submittedBlock: success(submittedUserOperation.submittedBlock),
            actionSource: success(submittedUserOperation.actionSource),
            type: success(submittedUserOperation.type),
        })
    )

const parseUserOperationFailed = (
    input: unknown,
    submittedUserOperation: SubmittedUserOperation
): Result<unknown, SubmittedUserOperationFailed> =>
    object(input).andThen((obj) =>
        oneOf(obj, [
            shape({
                state: match(obj.state, 'CONFIRMED'),
                userOperationReceipt: object(obj.userOperationReceipt).andThen(
                    (userOpReceiptObj) =>
                        shape({
                            success: match(userOpReceiptObj.success, 'false'),
                            receipt: object(userOpReceiptObj.receipt).andThen(
                                (receiptObj) =>
                                    shape({
                                        transactionHash: Hexadecimal.parse(
                                            receiptObj.transactionHash
                                        ),
                                    })
                            ),
                        })
                ),
            }).map((result) => ({
                state: 'failed' as const,
                type: submittedUserOperation.type,
                userOperationHash: submittedUserOperation.userOperationHash,
                sender: submittedUserOperation.sender,
                queuedAt: submittedUserOperation.queuedAt,
                bundleTransactionHash:
                    result.userOperationReceipt.receipt.transactionHash,
                message: 'Operation was unsuccessful',
                submittedBlock: submittedUserOperation.submittedBlock,
                actionSource: submittedUserOperation.actionSource,
                failedAt: Date.now(),
            })),
            shape({
                state: match(obj.status, 'failed').map(() => 'failed' as const),
                type: success(submittedUserOperation.type),
                userOperationHash: success(
                    submittedUserOperation.userOperationHash
                ),
                sender: success(submittedUserOperation.sender),
                queuedAt: success(submittedUserOperation.queuedAt),
                bundleTransactionHash: Hexadecimal.parse(obj.transactionHash),
                submittedBlock: success(submittedUserOperation.submittedBlock),
                actionSource: success(submittedUserOperation.actionSource),
                message: success('Operation was unsuccessful'),
                failedAt: success(Date.now()),
            }),
        ])
    )

const parseFailed = (
    input: unknown,
    submittedUserOperation: SubmittedUserOperation
): Result<unknown, SubmittedUserOperationFailed> =>
    oneOf(input, [
        parseUserOperationFailed(input, submittedUserOperation),
        parseBundleTransactionFailed(input, submittedUserOperation),
    ])

const parse = (
    input: unknown,
    submittedUserOperation: SubmittedUserOperation
): Result<unknown, SubmittedUserOperation> =>
    object(input).andThen((obj) =>
        oneOf(obj, [
            parsePending(obj, submittedUserOperation),
            parseBundled(obj, submittedUserOperation),
            parseCompleted(obj, submittedUserOperation),
            parseRejected(obj, submittedUserOperation),
            parseFailed(obj, submittedUserOperation),
        ])
    )
