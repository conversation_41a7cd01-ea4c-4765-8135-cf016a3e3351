import React from 'react'
import { FormattedMessage } from 'react-intl'

import { BoldBrowseGlobe } from '@zeal/uikit/Icon/BoldBrowseGlobe'
import { ExternalLink } from '@zeal/uikit/Icon/ExternalLink'
import { ListItem } from '@zeal/uikit/ListItem'

import { notReachable } from '@zeal/toolkit'
import { ZealPlatform } from '@zeal/toolkit/OS/ZealPlatform'

type Props = {
    onClick: () => void
}

export const DiscoverMoreAppsListItem = ({ onClick }: Props) => {
    return (
        <ListItem
            aria-current={false}
            size="large"
            onClick={onClick}
            avatar={({ size }) => (
                <BoldBrowseGlobe size={size} color="iconDefault" />
            )}
            primaryText={
                <FormattedMessage
                    id="browse.discover_more_apps"
                    defaultMessage="Discover more apps"
                />
            }
            side={{
                rightIcon: ({ size }) => {
                    return (() => {
                        switch (ZealPlatform.OS) {
                            case 'ios':
                            case 'android':
                                return null
                            case 'web':
                                return (
                                    <ExternalLink
                                        color="iconDefault"
                                        size={size}
                                    />
                                )
                            /* istanbul ignore next */
                            default:
                                return notReachable(ZealPlatform)
                        }
                    })()
                },
            }}
        />
    )
}
