import { FormattedMessage } from 'react-intl'

import { BannerSolid } from '@zeal/uikit/BannerSolid'
import { ForwardIcon } from '@zeal/uikit/Icon/ForwardIcon'
import { Tertiary } from '@zeal/uikit/Tertiary'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account } from '@zeal/domains/Account'
import { CardConfig } from '@zeal/domains/Card'
import { CurrencyHiddenMap } from '@zeal/domains/Currency'
import { KeyStore, SecretPhrase } from '@zeal/domains/KeyStore'
import { recoveryKitStatus } from '@zeal/domains/KeyStore/helpers/recoveryKitStatus'
import { Portfolio2 } from '@zeal/domains/Portfolio'
import { isFunded } from '@zeal/domains/Portfolio/helpers/IsFunded'
import { BankTransferInfo, DefaultCurrencyConfig } from '@zeal/domains/Storage'

type Props = {
    account: Account
    keyStore: KeyStore
    bankTransferInfo: BankTransferInfo
    cardConfig: CardConfig
    portfolio: Portfolio2
    currencyHiddenMap: CurrencyHiddenMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg = {
    type: 'on_recovery_kit_setup'
    keystore: SecretPhrase
    address: Web3.address.Address
}

const isBankTransfersActivated = (
    bankTransferInfo: BankTransferInfo,
    account: Account
): boolean => {
    switch (bankTransferInfo.type) {
        case 'not_started':
            return false
        case 'unblock_user_created':
            return bankTransferInfo.connectedWalletAddress === account.address
        /* istanbul ignore next */
        default:
            return notReachable(bankTransferInfo)
    }
}

const isCardActivated = (cardConfig: CardConfig, account: Account): boolean => {
    switch (cardConfig.type) {
        case 'card_readonly_signer_address_is_not_selected':
        case 'card_readonly_signer_address_is_selected':
            return false
        case 'card_readonly_signer_address_is_selected_fully_onboarded':
            return cardConfig.readonlySignerAddress === account.address
        /* istanbul ignore next */
        default:
            return notReachable(cardConfig)
    }
}

export const SecretPhraseWalletBackupBanner = ({
    keyStore,
    account,
    onMsg,
    bankTransferInfo,
    currencyHiddenMap,
    cardConfig,
    portfolio,
    defaultCurrencyConfig,
}: Props) => {
    switch (keyStore.type) {
        case 'track_only':
        case 'private_key_store':
        case 'ledger':
        case 'trezor':
        case 'safe_4337':
            return null
        case 'secret_phrase_key':
            const status = recoveryKitStatus(keyStore)

            switch (status) {
                case 'configured':
                    return null
                case 'not_configured':
                    const bankTransfersActivated = isBankTransfersActivated(
                        bankTransferInfo,
                        account
                    )

                    const cardActivated = isCardActivated(cardConfig, account)

                    const portfolioBalance = isFunded({
                        portfolio,
                        currencyHiddenMap,
                        defaultCurrencyConfig,
                    })

                    if (
                        portfolioBalance ||
                        bankTransfersActivated ||
                        cardActivated
                    ) {
                        return (
                            <BannerSolid
                                rounded
                                variant="warning"
                                title={
                                    <FormattedMessage
                                        id="backup-banner.title"
                                        defaultMessage="Wallet not backed up"
                                    />
                                }
                                subtitle={
                                    <FormattedMessage
                                        id="backup-banner.risk_losing_funds"
                                        defaultMessage="Back up now or risk losing funds"
                                    />
                                }
                                right={
                                    <Tertiary
                                        size="regular"
                                        color="warning"
                                        onClick={() =>
                                            onMsg({
                                                type: 'on_recovery_kit_setup',
                                                address: account.address,
                                                keystore: keyStore,
                                            })
                                        }
                                    >
                                        {({
                                            color,
                                            textVariant,
                                            textWeight,
                                        }) => (
                                            <>
                                                <Text
                                                    color={color}
                                                    variant={textVariant}
                                                    weight={textWeight}
                                                >
                                                    <FormattedMessage
                                                        id="backup-banner.backup_now"
                                                        defaultMessage="Back up"
                                                    />
                                                </Text>
                                                <ForwardIcon
                                                    size={14}
                                                    color={color}
                                                />
                                            </>
                                        )}
                                    </Tertiary>
                                }
                            />
                        )
                    }
                    return null
                /* istanbul ignore next */
                default:
                    return notReachable(status)
            }
        /* istanbul ignore next */
        default:
            return notReachable(keyStore)
    }
}
