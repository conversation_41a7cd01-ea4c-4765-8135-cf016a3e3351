import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { KeyStore, KeyStoreMap } from '@zeal/domains/KeyStore'
import { UnsupportedSafeNetworkLayout } from '@zeal/domains/KeyStore/components/UnsupportedSafeNetworkLayout'
import { SignActionSource } from '@zeal/domains/Main'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import {
    EthSignTypedData,
    EthSignTypedDataV3,
    EthSignTypedDataV4,
    PersonalSign,
} from '@zeal/domains/RPCRequest'
import { FetchSimulatedSignMessage } from '@zeal/domains/RPCRequest/domains/SignMessageSimulation/api/fetchSimulatedSignMessage'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { CheckSafeDeployment } from './CheckSafeDeployment'
import { Signing, State as SigningVisualState } from './Signing'
// https://docs.metamask.io/wallet/how-to/sign-data/

type Props = {
    sessionPassword: string
    keyStore: KeyStore
    request:
        | PersonalSign
        | EthSignTypedData
        | EthSignTypedDataV3
        | EthSignTypedDataV4

    state: State

    account: Account
    network: Network
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    actionSource: SignActionSource

    accountsMap: AccountsMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    installationId: string
    keyStoreMap: KeyStoreMap
    portfolio: ServerPortfolio2 | null
    defaultCurrencyConfig: DefaultCurrencyConfig
    fetchSimulatedSignMessage: FetchSimulatedSignMessage
    onMsg: (msg: Msg) => void
}

type Msg =
    | MsgOf<typeof Signing>
    | MsgOf<typeof CheckSafeDeployment>
    | { type: 'on_wrong_network_accepted' }

export type State = SigningVisualState

export const Sign = ({
    sessionPassword,
    onMsg,
    account,
    keyStore,
    state,
    request,
    network,
    networkMap,
    networkRPCMap,
    accountsMap,
    feePresetMap,
    gasCurrencyPresetMap,
    installationId,
    keyStoreMap,
    portfolio,
    actionSource,
    defaultCurrencyConfig,
    fetchSimulatedSignMessage,
}: Props) => {
    switch (keyStore.type) {
        case 'track_only':
        case 'private_key_store':
        case 'ledger':
        case 'secret_phrase_key':
        case 'trezor':
            return (
                <Signing
                    networkRPCMap={networkRPCMap}
                    installationId={installationId}
                    account={account}
                    keyStore={keyStore}
                    network={network}
                    networkMap={networkMap}
                    request={request}
                    sessionPassword={sessionPassword}
                    state={state}
                    actionSource={actionSource}
                    onMsg={onMsg}
                    fetchSimulatedSignMessage={fetchSimulatedSignMessage}
                />
            )

        case 'safe_4337':
            switch (network.smartWalletSupport.type) {
                case 'supported':
                    return (
                        <CheckSafeDeployment
                            defaultCurrencyConfig={defaultCurrencyConfig}
                            accountsMap={accountsMap}
                            feePresetMap={feePresetMap}
                            gasCurrencyPresetMap={gasCurrencyPresetMap}
                            installationId={installationId}
                            keyStoreMap={keyStoreMap}
                            portfolio={portfolio}
                            account={account}
                            keyStore={keyStore}
                            network={network}
                            networkMap={networkMap}
                            request={request}
                            sessionPassword={sessionPassword}
                            state={state}
                            networkRPCMap={networkRPCMap}
                            actionSource={actionSource}
                            onMsg={onMsg}
                            fetchSimulatedSignMessage={
                                fetchSimulatedSignMessage
                            }
                        />
                    )
                case 'not_supported':
                    return (
                        <UnsupportedSafeNetworkLayout
                            installationId={installationId}
                            account={account}
                            network={network}
                            state={state}
                            actionSource={actionSource}
                            onMsg={onMsg}
                        />
                    )
                /* istanbul ignore next */
                default:
                    return notReachable(network.smartWalletSupport)
            }

        default:
            return notReachable(keyStore)
    }
}
