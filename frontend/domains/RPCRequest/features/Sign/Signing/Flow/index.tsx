import { FormattedMessage } from 'react-intl'

import { Column } from '@zeal/uikit/Column'
import { Content } from '@zeal/uikit/Content'
import { Screen } from '@zeal/uikit/Screen'

import { notReachable } from '@zeal/toolkit'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account } from '@zeal/domains/Account'
import { ConnectedMinimized } from '@zeal/domains/DApp/domains/ConnectionState/features/ConnectedMinimized'
import { KeyStore } from '@zeal/domains/KeyStore'
import { SignActionSource } from '@zeal/domains/Main'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import {
    EthSignTypedData,
    EthSignTypedDataV3,
    EthSignTypedDataV4,
    PersonalSign,
} from '@zeal/domains/RPCRequest'
import { FetchSimulatedSignMessage } from '@zeal/domains/RPCRequest/domains/SignMessageSimulation/api/fetchSimulatedSignMessage'
import { ActionBar } from '@zeal/domains/Transactions/components/ActionBar'

import { NoSimulation, State as FormVisualState } from './NoSimulation'
import { Simulation } from './Simulation'

type Props = {
    keyStore: KeyStore
    installationId: string
    request:
        | PersonalSign
        | EthSignTypedDataV4
        | EthSignTypedData
        | EthSignTypedDataV3

    isLoading: boolean

    account: Account
    network: Network
    networkMap: NetworkMap

    state: VisualState
    actionSource: SignActionSource

    networkRPCMap: NetworkRPCMap
    onMsg: (msg: Msg) => void
    fetchSimulatedSignMessage: FetchSimulatedSignMessage
}

export type VisualState = FormVisualState

type Msg =
    | MsgOf<typeof NoSimulation>
    | Extract<
          MsgOf<typeof Simulation>,
          {
              type:
                  | 'cancel_button_click'
                  | 'sign_click'
                  | 'import_keys_button_clicked'
          }
      >

export const Flow = ({
    account,
    keyStore,
    network,
    networkMap,
    onMsg,
    request,
    state,
    isLoading,
    actionSource,
    installationId,
    networkRPCMap,
    fetchSimulatedSignMessage,
}: Props) => {
    const [loadable] = useLoadableData(fetchSimulatedSignMessage, {
        type: 'loading',
        params: {
            request,
            network,
            dApp: actionSource.dAppSiteInfo,
            networkMap,
            networkRPCMap,
        },
    })

    switch (loadable.type) {
        case 'loading':
            switch (state.type) {
                case 'minimised':
                    return (
                        <ConnectedMinimized
                            installationId={installationId}
                            onMsg={onMsg}
                        />
                    )

                case 'maximised':
                    return (
                        <Screen
                            background="light"
                            padding="form"
                            onNavigateBack={() =>
                                onMsg({ type: 'on_minimize_click' })
                            }
                        >
                            {/* TODO @resetko-zeal No back button here in internal flows. But not sure if we do much internal signing now, hence should not be a big issue */}
                            <ActionBar
                                title={null}
                                account={account}
                                actionSourceType={actionSource.type}
                                network={null}
                                onMsg={onMsg}
                            />
                            <Column spacing={12} fill>
                                <Content>
                                    <Content.Splash
                                        onAnimationComplete={null}
                                        variant="spinner"
                                        title={
                                            <FormattedMessage
                                                id="Sign.Simuation.Skeleton.title"
                                                defaultMessage="Doing safety checks…"
                                            />
                                        }
                                    />
                                </Content>
                            </Column>
                        </Screen>
                    )

                default:
                    return notReachable(state)
            }

        case 'loaded': {
            switch (loadable.data.type) {
                case 'not_supported':
                case 'failed':
                    return (
                        <NoSimulation
                            simulatedResult={loadable.data}
                            installationId={installationId}
                            account={account}
                            isLoading={isLoading}
                            keyStore={keyStore}
                            onMsg={onMsg}
                            request={loadable.params.request}
                            state={state}
                            actionSource={actionSource}
                        />
                    )

                case 'simulated':
                    return (
                        <Simulation
                            installationId={installationId}
                            account={account}
                            request={loadable.params.request}
                            isLoading={isLoading}
                            keyStore={keyStore}
                            simulatedResult={loadable.data}
                            state={state}
                            networkMap={networkMap}
                            actionSource={actionSource}
                            onMsg={onMsg}
                        />
                    )

                default:
                    return notReachable(loadable.data)
            }
        }

        case 'error':
            return (
                <NoSimulation
                    simulatedResult={{ type: 'failed' }}
                    installationId={installationId}
                    account={account}
                    isLoading={isLoading}
                    keyStore={keyStore}
                    onMsg={onMsg}
                    request={loadable.params.request}
                    state={state}
                    actionSource={actionSource}
                />
            )

        default:
            return notReachable(loadable)
    }
}
