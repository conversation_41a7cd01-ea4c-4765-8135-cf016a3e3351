import { useEffect } from 'react'

import { noop, notReachable } from '@zeal/toolkit'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { useLazyLoadableData } from '@zeal/toolkit/LoadableData/LazyLoadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { useLiveRef } from '@zeal/toolkit/React'

import { Account } from '@zeal/domains/Account'
import { DAppSiteInfo } from '@zeal/domains/DApp'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { ErrorPopup as LedgerErrorPopup } from '@zeal/domains/Error/domains/Ledger/components/ErrorPopup'
import { ErrorPopup as TrezorErrorPopup } from '@zeal/domains/Error/domains/Trezor/components/ErrorPopup'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'
import { KeyStore, SigningKeyStore } from '@zeal/domains/KeyStore'
import { SignActionSource } from '@zeal/domains/Main'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { SignMessageRequest } from '@zeal/domains/RPCRequest'
import { SignMessageSimulationResult } from '@zeal/domains/RPCRequest/domains/SignMessageSimulation'
import { FetchSimulatedSignMessage } from '@zeal/domains/RPCRequest/domains/SignMessageSimulation/api/fetchSimulatedSignMessage'
import { signMessage } from '@zeal/domains/RPCRequest/helpers/signMessage'
import { SignOnHardwareWalletPopup } from '@zeal/domains/TransactionRequest/components/SignOnHardwareWalletPopup'

import { Flow, VisualState as FlowVisualState } from './Flow'

// https://docs.metamask.io/wallet/how-to/sign-data/

type Props = {
    installationId: string
    sessionPassword: string
    keyStore: KeyStore
    request: SignMessageRequest
    state: State

    account: Account
    network: Network
    networkMap: NetworkMap
    actionSource: SignActionSource

    networkRPCMap: NetworkRPCMap

    onMsg: (msg: Msg) => void
    fetchSimulatedSignMessage: FetchSimulatedSignMessage
}

export type Msg =
    | {
          type: 'message_signed'
          signature: Hexadecimal.Hexadecimal
          simulationResult: SignMessageSimulationResult
      }
    | Extract<
          MsgOf<typeof Flow>,
          {
              type:
                  | 'cancel_button_click'
                  | 'on_minimize_click'
                  | 'drag'
                  | 'on_expand_request'
                  | 'import_keys_button_clicked'
          }
      >

export type State = FlowVisualState

const fetch = (params: {
    request: SignMessageRequest
    dApp: DAppSiteInfo | null
    keyStore: SigningKeyStore
    sessionPassword: string
    network: Network

    simulationResult: SignMessageSimulationResult
}) => signMessage(params)

export const Signing = ({
    sessionPassword,
    onMsg,
    account,
    keyStore,
    state,
    request,
    network,
    networkMap,
    installationId,
    actionSource,
    networkRPCMap,
    fetchSimulatedSignMessage,
}: Props) => {
    const [loadable, setLoadable] = useLazyLoadableData(fetch)
    const liveOnMsg = useLiveRef(onMsg)

    useEffect(() => {
        switch (loadable.type) {
            case 'not_asked':
            case 'loading':
            case 'error':
                break
            case 'loaded':
                liveOnMsg.current({
                    type: 'message_signed',
                    signature: loadable.data,
                    simulationResult: loadable.params.simulationResult,
                })
                break
            /* istanbul ignore next */
            default:
                return notReachable(loadable)
        }
    }, [liveOnMsg, loadable])

    switch (loadable.type) {
        case 'not_asked':
            return (
                <Flow
                    networkRPCMap={networkRPCMap}
                    fetchSimulatedSignMessage={fetchSimulatedSignMessage}
                    installationId={installationId}
                    networkMap={networkMap}
                    state={state}
                    isLoading={false}
                    keyStore={keyStore}
                    request={request}
                    account={account}
                    network={network}
                    actionSource={actionSource}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'on_expand_request':
                            case 'drag':
                            case 'cancel_button_click':
                            case 'on_minimize_click':
                            case 'import_keys_button_clicked':
                                onMsg(msg)
                                break
                            case 'sign_click':
                                setLoadable({
                                    type: 'loading',
                                    params: {
                                        keyStore: msg.keyStore,
                                        sessionPassword,
                                        request: msg.request,
                                        simulationResult: msg.simulatedResult,
                                        network,
                                        dApp: actionSource.dAppSiteInfo,
                                    },
                                })
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'loading':
        case 'loaded':
            return (
                <>
                    <Flow
                        networkRPCMap={networkRPCMap}
                        fetchSimulatedSignMessage={fetchSimulatedSignMessage}
                        installationId={installationId}
                        networkMap={networkMap}
                        state={state}
                        isLoading
                        keyStore={keyStore}
                        request={request}
                        account={account}
                        network={network}
                        actionSource={actionSource}
                        onMsg={noop}
                    />
                    {(() => {
                        switch (loadable.params.keyStore.type) {
                            case 'secret_phrase_key':
                            case 'private_key_store':
                            case 'safe_4337':
                                return null

                            case 'trezor':
                            case 'ledger':
                                return (
                                    <SignOnHardwareWalletPopup
                                        onMsg={(msg) => {
                                            switch (msg.type) {
                                                case 'close':
                                                    setLoadable({
                                                        type: 'not_asked',
                                                    })
                                                    break
                                                /* istanbul ignore next */
                                                default:
                                                    return notReachable(
                                                        msg.type
                                                    )
                                            }
                                        }}
                                    />
                                )
                            /* istanbul ignore next */
                            default:
                                return notReachable(loadable.params.keyStore)
                        }
                    })()}
                </>
            )

        case 'error': {
            const error = parseAppError(loadable.error)
            return (
                <>
                    <Flow
                        networkRPCMap={networkRPCMap}
                        fetchSimulatedSignMessage={fetchSimulatedSignMessage}
                        installationId={installationId}
                        networkMap={networkMap}
                        state={state}
                        isLoading
                        keyStore={keyStore}
                        request={request}
                        account={account}
                        network={network}
                        actionSource={actionSource}
                        onMsg={noop}
                    />
                    {(() => {
                        switch (error.type) {
                            case 'trezor_connection_already_initialized':
                            case 'trezor_popup_closed':
                            case 'trezor_permissions_not_granted':
                            case 'trezor_method_cancelled':
                            case 'trezor_action_cancelled':
                            case 'trezor_pin_cancelled':
                            case 'trezor_device_used_elsewhere':
                                return (
                                    <TrezorErrorPopup
                                        installationId={installationId}
                                        error={error}
                                        onMsg={(msg) => {
                                            switch (msg.type) {
                                                case 'on_trezor_error_close':
                                                    onMsg({
                                                        type: 'cancel_button_click',
                                                    })
                                                    break
                                                case 'on_sync_trezor_click':
                                                    setLoadable({
                                                        type: 'loading',
                                                        params: loadable.params,
                                                    })
                                                    break
                                                /* istanbul ignore next */
                                                default:
                                                    return notReachable(msg)
                                            }
                                        }}
                                    />
                                )

                            case 'ledger_not_running_any_app':
                            case 'hardware_wallet_failed_to_open_device':
                            case 'ledger_blind_sign_not_enabled_or_running_non_eth_app':
                            case 'ledger_running_non_eth_app':
                            case 'ledger_is_locked':
                            case 'user_trx_denied_by_user':
                                return (
                                    <LedgerErrorPopup
                                        error={error}
                                        onMsg={(msg) => {
                                            switch (msg.type) {
                                                case 'on_ledger_error_close':
                                                    onMsg({
                                                        type: 'cancel_button_click',
                                                    })
                                                    break
                                                case 'on_sync_ledger_click':
                                                    setLoadable({
                                                        type: 'loading',
                                                        params: loadable.params,
                                                    })
                                                    break
                                                /* istanbul ignore next */
                                                default:
                                                    return notReachable(msg)
                                            }
                                        }}
                                    />
                                )

                            default:
                                return (
                                    <AppErrorPopup
                                        installationId={installationId}
                                        error={error}
                                        onMsg={(msg) => {
                                            switch (msg.type) {
                                                case 'close':
                                                    onMsg({
                                                        type: 'cancel_button_click',
                                                    })
                                                    break
                                                case 'try_again_clicked':
                                                    setLoadable({
                                                        type: 'loading',
                                                        params: loadable.params,
                                                    })
                                                    break
                                                /* istanbul ignore next */
                                                default:
                                                    return notReachable(msg)
                                            }
                                        }}
                                    />
                                )
                        }
                    })()}
                </>
            )
        }
        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}
