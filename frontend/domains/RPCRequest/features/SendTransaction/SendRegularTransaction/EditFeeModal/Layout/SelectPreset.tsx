import { FormattedMessage, useIntl } from 'react-intl'

import {
    FeeSelectorButton,
    FeeSelectorButtonSkeleton,
} from '@zeal/uikit/FeeSelectorButton'
import { BoldCheetah } from '@zeal/uikit/Icon/BoldCheetah'
import { BoldRabbit } from '@zeal/uikit/Icon/BoldRabbit'
import { BoldTurtle } from '@zeal/uikit/Icon/BoldTurtle'
import { Row } from '@zeal/uikit/Row'

import { notReachable } from '@zeal/toolkit'
import { useReadableDistance } from '@zeal/toolkit/Date/useReadableDistance'
import { PollableData } from '@zeal/toolkit/LoadableData/PollableData'

import { NetworkHexId } from '@zeal/domains/Network'
import { NotSigned } from '@zeal/domains/TransactionRequest'
import {
    FeeForecastRequest,
    FeeForecastResponse,
    ForecastDuration,
    PredefinedPreset,
} from '@zeal/domains/Transactions/domains/FeeForecast'
import { FormattedFee } from '@zeal/domains/Transactions/domains/FeeForecast/components/FormattedFee'

import { validateEditFormPresetValidationError } from '../../FeeForecastWidget/helpers/validation'

type Props = {
    pollableData: PollableData<FeeForecastResponse, FeeForecastRequest>
    transactionRequest: NotSigned
    onMsg: (msg: Msg) => void
}

export type Msg = {
    type: 'on_predefined_fee_preset_selected'
    preset: PredefinedPreset
    networkHexId: NetworkHexId
}

export const SelectPreset = ({
    pollableData,
    transactionRequest,
    onMsg,
}: Props) => {
    const validationData = validateEditFormPresetValidationError({
        pollableData,
        transactionRequest,
    })

    const { formatMessage } = useIntl()

    const sectionLabel = formatMessage({
        id: 'EditFeeModal.SelectPreset.ariaLabel',
        defaultMessage: 'Select fee preset',
    })

    switch (validationData.type) {
        case 'Failure': {
            const reason = validationData.reason
            switch (reason.type) {
                case 'pollable_failed_to_fetch':
                    return (
                        <Row
                            spacing={8}
                            aria-label={sectionLabel}
                            alignY="stretch"
                        >
                            <FeeSelectorButton
                                disabled
                                amount="$?"
                                icon={({ color }) => (
                                    <BoldTurtle size={20} color={color} />
                                )}
                                tabindex={1}
                                time={null}
                                title={
                                    <FormattedMessage
                                        id="EditFeeModal.SelectPreset.slow"
                                        defaultMessage="Slow"
                                    />
                                }
                            />

                            <FeeSelectorButton
                                disabled
                                amount="$?"
                                icon={({ color }) => (
                                    <BoldRabbit size={20} color={color} />
                                )}
                                tabindex={2}
                                time={null}
                                title={
                                    <FormattedMessage
                                        id="EditFeeModal.SelectPreset.normal"
                                        defaultMessage="Normal"
                                    />
                                }
                            />

                            <FeeSelectorButton
                                disabled
                                amount="$?"
                                icon={({ color }) => (
                                    <BoldCheetah size={20} color={color} />
                                )}
                                tabindex={3}
                                time={null}
                                title={
                                    <FormattedMessage
                                        id="EditFeeModal.SelectPreset.fast"
                                        defaultMessage="Fast"
                                    />
                                }
                            />
                        </Row>
                    )

                case 'pollable_data_loading':
                    return (
                        <Row spacing={8} alignY="stretch">
                            <FeeSelectorButtonSkeleton />

                            <FeeSelectorButtonSkeleton />

                            <FeeSelectorButtonSkeleton />
                        </Row>
                    )
                /* istanbul ignore next */
                default:
                    return notReachable(reason)
            }
        }

        case 'Success': {
            switch (validationData.data.pollable.data.type) {
                case 'FeesForecastResponseEip1559RecommendedFee': {
                    const request = validationData.data.pollable.params
                    const { fast } = validationData.data.pollable.data

                    return (
                        <Row
                            spacing={8}
                            aria-label={sectionLabel}
                            alignY="stretch"
                        >
                            <FeeSelectorButton
                                selected={
                                    request.selectedPreset.type === 'Fast'
                                }
                                amount={<FormattedFee fee={fast} />}
                                icon={() => null}
                                tabindex={3}
                                time={<Time duration={fast.forecastDuration} />}
                                title={
                                    <FormattedMessage
                                        id="EditFeeModal.SelectPreset.recommended"
                                        defaultMessage="Recommended"
                                    />
                                }
                                onClick={() =>
                                    onMsg({
                                        type: 'on_predefined_fee_preset_selected',
                                        networkHexId:
                                            transactionRequest.networkHexId,
                                        preset: { type: 'Fast' },
                                    })
                                }
                            />
                        </Row>
                    )
                }

                case 'FeesForecastResponseLegacyFee':
                case 'FeesForecastResponseEip1559Fee': {
                    const request = validationData.data.pollable.params
                    const { slow, normal, fast } =
                        validationData.data.pollable.data

                    return (
                        <Row
                            spacing={8}
                            aria-label={sectionLabel}
                            alignY="stretch"
                        >
                            <FeeSelectorButton
                                selected={
                                    request.selectedPreset.type === 'Slow'
                                }
                                amount={<FormattedFee fee={slow} />}
                                icon={({ color }) => (
                                    <BoldTurtle size={20} color={color} />
                                )}
                                tabindex={1}
                                time={<Time duration={slow.forecastDuration} />}
                                title={
                                    <FormattedMessage
                                        id="EditFeeModal.SelectPreset.slow"
                                        defaultMessage="Slow"
                                    />
                                }
                                onClick={() =>
                                    onMsg({
                                        type: 'on_predefined_fee_preset_selected',
                                        networkHexId:
                                            transactionRequest.networkHexId,
                                        preset: { type: 'Slow' },
                                    })
                                }
                            />

                            <FeeSelectorButton
                                selected={
                                    request.selectedPreset.type === 'Normal'
                                }
                                amount={<FormattedFee fee={normal} />}
                                icon={({ color }) => (
                                    <BoldRabbit size={20} color={color} />
                                )}
                                tabindex={2}
                                time={
                                    <Time duration={normal.forecastDuration} />
                                }
                                title={
                                    <FormattedMessage
                                        id="EditFeeModal.SelectPreset.normal"
                                        defaultMessage="Normal"
                                    />
                                }
                                onClick={() =>
                                    onMsg({
                                        type: 'on_predefined_fee_preset_selected',
                                        networkHexId:
                                            transactionRequest.networkHexId,
                                        preset: { type: 'Normal' },
                                    })
                                }
                            />

                            <FeeSelectorButton
                                selected={
                                    request.selectedPreset.type === 'Fast'
                                }
                                amount={<FormattedFee fee={fast} />}
                                icon={({ color }) => (
                                    <BoldCheetah size={20} color={color} />
                                )}
                                tabindex={3}
                                time={<Time duration={fast.forecastDuration} />}
                                title={
                                    <FormattedMessage
                                        id="EditFeeModal.SelectPreset.fast"
                                        defaultMessage="Fast"
                                    />
                                }
                                onClick={() =>
                                    onMsg({
                                        type: 'on_predefined_fee_preset_selected',
                                        networkHexId:
                                            transactionRequest.networkHexId,
                                        preset: { type: 'Fast' },
                                    })
                                }
                            />
                        </Row>
                    )
                }
                default:
                    return notReachable(validationData.data.pollable.data)
            }
        }

        /* istanbul ignore next */
        default:
            return notReachable(validationData)
    }
}

type TimeProps = {
    duration: ForecastDuration
}

export const Time = ({ duration }: TimeProps) => {
    const formatHumanReadableDistance = useReadableDistance()

    switch (duration.type) {
        case 'WithinForecast':
            return <>{formatHumanReadableDistance(duration.durationMs)}</>

        case 'OutsideOfForecast':
            return (
                <FormattedMessage
                    id="EditFeeModal.SelectPreset.Time.unknown"
                    defaultMessage="Time Unknown"
                />
            )

        /* istanbul ignore next */
        default:
            return notReachable(duration)
    }
}
