import { notReachable } from '@zeal/toolkit'
import { parse as parseJSON } from '@zeal/toolkit/JSON'
import { object, oneOf, Result, success } from '@zeal/toolkit/Result'

import { KnownCryptoCurrencies } from '@zeal/domains/Currency'
import { NetworkMap } from '@zeal/domains/Network'
import {
    EthSignTypedData,
    EthSignTypedDataV3,
    EthSignTypedDataV4,
} from '@zeal/domains/RPCRequest'

import { parseDaiPermitSignMessage } from './parseDaiPermitSignMessage'
import { parsePermit2SignMessage } from './parsePermit2SignMessage'
import { parsePermitSignMessage } from './parsePermitSignMessage'

import {
    DaiPermitSignMessage,
    Permit2SignMessage,
    PermitSignMessage,
    SimulatedSignMessage,
    UnknownSignMessage,
} from '..'

const _dontForgetTheParser: Record<SimulatedSignMessage['type'], true> = {
    Permit2SignMessage: true,
    DaiPermitSignMessage: true,
    OrderCardTopupSignMessage: true,
    OrderEarnDepositBridge: true,
    OrderBuySignMessage: true,
    PermitSignMessage: true,
    UnknownSignMessage: true,
}

export const parseSimulatedSignMessage = ({
    input,
    knownCurrencies,
    networkMap,
    now,
}: {
    input: EthSignTypedDataV4 | EthSignTypedData | EthSignTypedDataV3
    knownCurrencies: KnownCryptoCurrencies
    networkMap: NetworkMap
    now: number
}): Result<
    unknown,
    | Permit2SignMessage
    | PermitSignMessage
    | UnknownSignMessage
    | DaiPermitSignMessage
> => {
    switch (input.method) {
        case 'eth_signTypedData_v4':
        case 'eth_signTypedData':
        case 'eth_signTypedData_v3':
            const json = parseJSON(input.params[1])
            switch (json.type) {
                case 'Failure':
                    // https://grwth-lbs.sentry.io/issues/6624009022/?project=****************&query=is%3Aunresolved%20issues%20in%20SignMessage%20parsing&referrer=issue-stream
                    // sounds like invalid message at a time of writing params[1] is not a json but hex
                    return success({
                        type: 'UnknownSignMessage',
                        rawMessage: input.params[1],
                    })
                case 'Success':
                    return object(json.data).andThen((obj) =>
                        oneOf(obj, [
                            parsePermit2SignMessage({
                                input: obj,
                                knownCurrencies,
                                networkMap,
                                now,
                            }),

                            parsePermitSignMessage({
                                input: obj,
                                knownCurrencies,
                                networkMap,
                                now,
                            }),

                            parseDaiPermitSignMessage({
                                input: obj,
                                knownCurrencies,
                                networkMap,
                                now,
                            }),

                            success({
                                type: 'UnknownSignMessage' as const,
                                rawMessage: input.params[1],
                            }),
                        ])
                    )
                default:
                    return notReachable(json)
            }

        default:
            return notReachable(input)
    }
}
