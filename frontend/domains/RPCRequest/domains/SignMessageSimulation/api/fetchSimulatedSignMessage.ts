import { notReachable } from '@zeal/toolkit'

import { fetchStaticCurrencies } from '@zeal/domains/Currency/api/fetchCurrenciesMatrix'
import { DAppSiteInfo } from '@zeal/domains/DApp'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { Network, NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { SignMessageRequest } from '@zeal/domains/RPCRequest'
import { SignMessageSimulationResult } from '@zeal/domains/RPCRequest/domains/SignMessageSimulation'
import { fetchApprovalSpenderSaftyCheck } from '@zeal/domains/SafetyCheck/api/fetchApprovalSpenderSafetyCheckRequest'
import { fetchBlockaidSignRequestSaftyCheck } from '@zeal/domains/SafetyCheck/api/fetchBlockaidSignRequestSaftyCheck'
import { fetchTokenVerificationSafetyCheck } from '@zeal/domains/SafetyCheck/api/fetchTokenVerificationSafetyCheck'
import { calculateApprovalExpirationLimitCheck } from '@zeal/domains/SafetyCheck/helpers/calculateApprovalExpirationLimitCheck'

import { parseSimulatedSignMessage } from '../parsers/parseSimulatedSignMessage'

type Params = {
    request: SignMessageRequest
    network: Network
    networkRPCMap: NetworkRPCMap
    networkMap: NetworkMap
    dApp: DAppSiteInfo | null
}

export type FetchSimulatedSignMessage = (
    params: Params
) => Promise<SignMessageSimulationResult>

/**
 * FIXME :: @max implement dapp "service"
 * https://linear.app/zeal/issue/ZEAL-2719
 */

export const fetchSimulatedSignMessage = async ({
    network,
    request,
    dApp,
    networkMap,
    networkRPCMap,
}: Params): Promise<SignMessageSimulationResult> => {
    const currencies = await fetchStaticCurrencies()
    try {
        switch (network.type) {
            case 'predefined':
                switch (request.method) {
                    case 'personal_sign':
                        return { type: 'not_supported' }
                    case 'eth_signTypedData':
                    case 'eth_signTypedData_v3':
                    case 'eth_signTypedData_v4':
                        const parsedMessage = parseSimulatedSignMessage({
                            input: request,
                            knownCurrencies: currencies,
                            networkMap,
                            now: Date.now(),
                        }).getSuccessResultOrThrow(
                            'issues in SignMessage parsing'
                        )
                        switch (parsedMessage.type) {
                            case 'UnknownSignMessage': {
                                return {
                                    type: 'simulated',
                                    simulationResponse: {
                                        checks: await fetchBlockaidSignRequestSaftyCheck(
                                            {
                                                requestToCheck: request,
                                                dApp,
                                                // signal,
                                                network,
                                            }
                                        ),
                                        currencies,
                                        message: parsedMessage,
                                    },
                                }
                            }
                            case 'DaiPermitSignMessage':
                            case 'PermitSignMessage': {
                                const [
                                    blockid,
                                    approval,
                                    tokenVerification,
                                    expirationChecks,
                                ] = await Promise.all([
                                    fetchBlockaidSignRequestSaftyCheck({
                                        requestToCheck: request,
                                        dApp,
                                        // signal,
                                        network,
                                    }),
                                    fetchApprovalSpenderSaftyCheck({
                                        networkRPCMap,
                                        network,
                                        spenderAddress:
                                            parsedMessage.approveTo.address,
                                    }),
                                    fetchTokenVerificationSafetyCheck({
                                        currencyIds: [
                                            parsedMessage.allowance.amount
                                                .amount.currency.id,
                                        ],
                                    }),
                                    calculateApprovalExpirationLimitCheck({
                                        allowance: parsedMessage.allowance,
                                    }),
                                ])
                                return {
                                    type: 'simulated',
                                    simulationResponse: {
                                        checks: [
                                            ...blockid,
                                            approval,
                                            ...tokenVerification,
                                            expirationChecks,
                                        ],
                                        currencies,
                                        message: parsedMessage,
                                    },
                                }
                            }
                            case 'Permit2SignMessage': {
                                const [
                                    blockid,
                                    approval,
                                    tokenVerification,
                                    expirationChecks,
                                ] = await Promise.all([
                                    fetchBlockaidSignRequestSaftyCheck({
                                        requestToCheck: request,
                                        dApp,
                                        // signal,
                                        network,
                                    }),
                                    fetchApprovalSpenderSaftyCheck({
                                        networkRPCMap,
                                        network,
                                        spenderAddress:
                                            parsedMessage.approveTo.address,
                                    }),
                                    fetchTokenVerificationSafetyCheck({
                                        currencyIds:
                                            parsedMessage.allowances.map(
                                                (allowance) =>
                                                    allowance.amount.amount
                                                        .currency.id
                                            ),
                                    }),
                                    parsedMessage.allowances.map((allowance) =>
                                        calculateApprovalExpirationLimitCheck({
                                            allowance,
                                        })
                                    ),
                                ])
                                return {
                                    type: 'simulated',
                                    simulationResponse: {
                                        checks: [
                                            ...blockid,
                                            approval,
                                            ...tokenVerification,
                                            ...expirationChecks,
                                        ],

                                        currencies,
                                        message: parsedMessage,
                                    },
                                }
                            }

                            /* istanbul ignore next */
                            default:
                                return notReachable(parsedMessage)
                        }

                    default:
                        return notReachable(request)
                }

            case 'custom':
            case 'testnet':
                return { type: 'not_supported' }

            default:
                return notReachable(network)
        }
    } catch (e) {
        captureError(e)
        return { type: 'failed' }
    }
}
