import { KnownCurrencies } from '@zeal/domains/Currency'
import { SwapsIOQuote } from '@zeal/domains/Currency/domains/SwapsIO'
import { Taker, TakerApyMap, TakerPortfolioMap2 } from '@zeal/domains/Earn'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, FiatMoney } from '@zeal/domains/Money'
import { SignMessageSafetyCheck } from '@zeal/domains/SafetyCheck'
import { SmartContract } from '@zeal/domains/SmartContract'
import { ApprovalAmount } from '@zeal/domains/Transactions'

export type PermitSignMessage = {
    type: 'PermitSignMessage'
    allowance: PermitAllowance
    approveTo: SmartContract
}

export type Permit2SignMessage = {
    type: 'Permit2SignMessage'
    allowances: PermitAllowance[]
    approveTo: SmartContract
}

export type DaiPermitSignMessage = {
    type: 'DaiPermitSignMessage'
    allowance: PermitAllowance
    approveTo: SmartContract
}

export type OrderCardTopupSignMessage = {
    type: 'OrderCardTopupSignMessage'
    to: CryptoMoney
    from: CryptoMoney

    fromInDefaultCurrency: FiatMoney | null
    toInDefaultCurrency: FiatMoney | null
}

export type OrderBuySignMessage = {
    type: 'OrderBuySignMessage'
    quote: SwapsIOQuote
}

export type OrderEarnDepositBridge = {
    type: 'OrderEarnDepositBridge'
    swapsIOQuote: SwapsIOQuote
    taker: Taker
    takerPortfolioMap: TakerPortfolioMap2
    takerApyMap: TakerApyMap
}

export type UnknownSignMessage = {
    type: 'UnknownSignMessage'
    rawMessage: string
}

export type SimulatedSignMessage =
    | PermitSignMessage
    | DaiPermitSignMessage
    | Permit2SignMessage
    | OrderCardTopupSignMessage
    | OrderEarnDepositBridge
    | OrderBuySignMessage
    | UnknownSignMessage

export type SignMessageSimulationResponse = {
    checks: SignMessageSafetyCheck[]
    message: SimulatedSignMessage
    currencies: KnownCurrencies // FIXME @resetko-zeal we won't need that
}

type FiniteExpiration = {
    type: 'FiniteExpiration'
    timestamp: number
}

type InfiniteExpiration = {
    type: 'InfiniteExpiration'
}

export type PermitExpiration = FiniteExpiration | InfiniteExpiration

export type PermitAllowance = {
    expiration: PermitExpiration
    amount: ApprovalAmount
}

export type SignMessageSimulationResult =
    | SignMessageSimulationResultNoSimulated
    | SignMessageSimulationResultSimulated

export type SignMessageSimulationResultNoSimulated =
    | SignMessageSimulationResultNotSupported
    | { type: 'failed' }

export type SignMessageSimulationResultNotSupported = { type: 'not_supported' }

export type SignMessageSimulationResultSimulated = {
    type: 'simulated'
    simulationResponse: SignMessageSimulationResponse
}
