import { Taker, TakerPortfolioMap2 } from '@zeal/domains/Earn'
import { sumTakerPortfolio } from '@zeal/domains/Earn/helpers/sumEarn'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

export const sortTakersByBalance =
    ({
        takerPortfolioMap,
        defaultCurrencyConfig,
    }: {
        takerPortfolioMap: TakerPortfolioMap2
        defaultCurrencyConfig: DefaultCurrencyConfig
    }) =>
    (takerA: Taker, takerB: Taker): number => {
        const balanceA = sumTakerPortfolio({
            taker: takerA,
            takerPortfolioMap,
            defaultCurrencyConfig,
        })

        const balanceB = sumTakerPortfolio({
            taker: takerB,
            takerPortfolioMap,
            defaultCurrencyConfig,
        })

        return Number(balanceB.amount - balanceA.amount)
    }
