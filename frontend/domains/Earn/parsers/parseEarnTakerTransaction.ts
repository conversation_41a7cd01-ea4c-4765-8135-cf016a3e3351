import { notReachable } from '@zeal/toolkit'
import { Hexadecimal } from '@zeal/toolkit/Hexadecimal'
import { failure, oneOf, Result, success } from '@zeal/toolkit/Result'
import { staticFromString } from '@zeal/toolkit/Web3/address'

import { NULL_ADDRESS } from '@zeal/domains/Address/constants'
import { Taker } from '@zeal/domains/Earn'
import { CryptoMoney, Money2 } from '@zeal/domains/Money'
import { ERC20TransferLog } from '@zeal/domains/RPCRequest'
import { IndexedTransaction } from '@zeal/domains/Transactions'

export type EarnTakerTransaction =
    | KnownEarnTakerTransaction
    | UnknownTakerTransaction

export type KnownEarnTakerTransaction =
    | DepositTransaction
    | WithdrawalTransaction
    | CardRechargeTransaction

export type KnownEarnTakerTransactionWithUserCurrency =
    KnownEarnTakerTransaction & { amountInUserCurrency: Money2 }

type DepositTransaction = {
    type: 'deposit_transaction'
    amountInInvestmentCurrency: CryptoMoney
    blockNumber: bigint
    hash: Hexadecimal
}

type WithdrawalTransaction = {
    type: 'withdrawal_transaction'
    amountInInvestmentCurrency: CryptoMoney
    blockNumber: bigint
    hash: Hexadecimal
}

type CardRechargeTransaction = {
    type: 'card_recharge_transaction'
    amountInInvestmentCurrency: CryptoMoney
    blockNumber: bigint
    hash: Hexadecimal
}

type UnknownTakerTransaction = {
    type: 'unknown'
    blockNumber: bigint
    transactionData: IndexedTransaction
    hash: Hexadecimal
}

const COW_SWAP_SETTLEMENT_PROTOCOL = staticFromString(
    '******************************************'
)

export const parseEarnDepositTransaction = (
    transactionData: IndexedTransaction,
    taker: Taker
): Result<unknown, DepositTransaction> => {
    const transferLog = transactionData.logs.find(
        (log): log is ERC20TransferLog => {
            switch (log.type) {
                case 'unknown':
                case 'added_owner':
                case 'approval':
                case 'account_deployed':
                case 'threshold_updated':
                case 'set_allowance':
                case 'enable_module':
                case 'disable_module':
                case 'safe_module_transaction':
                case 'safe_received':
                case 'user_operation_event':
                case 'safe_module_transaction_for_native_fee_payment':
                case 'user_operation_revert_reason':
                case 'native_wrapper_deposit':
                case 'native_wrapper_withdraw':
                    return false
                case 'erc20_transfer':
                    return (
                        log.to === taker.address &&
                        log.from !== NULL_ADDRESS &&
                        log.currencyId === taker.cryptoCurrency.id
                    )
                /* istanbul ignore next */
                default:
                    return notReachable(log)
            }
        }
    )

    if (!transferLog) {
        return failure({ type: 'no_erc20_transfer_log' })
    }

    return success({
        type: 'deposit_transaction',
        blockNumber: transactionData.blockNumber,
        hash: transactionData.hash,
        amountInInvestmentCurrency: {
            amount: transferLog.amount,
            currency: taker.cryptoCurrency,
        },
    })
}

export const parseEarnWithdrawalTransaction = (
    transactionData: IndexedTransaction,
    taker: Taker
): Result<unknown, WithdrawalTransaction> => {
    const transferLog = transactionData.logs.find(
        (log): log is ERC20TransferLog => {
            switch (log.type) {
                case 'unknown':
                case 'added_owner':
                case 'approval':
                case 'account_deployed':
                case 'threshold_updated':
                case 'set_allowance':
                case 'enable_module':
                case 'disable_module':
                case 'safe_module_transaction':
                case 'safe_received':
                case 'user_operation_event':
                case 'safe_module_transaction_for_native_fee_payment':
                case 'user_operation_revert_reason':
                case 'native_wrapper_deposit':
                case 'native_wrapper_withdraw':
                    return false
                case 'erc20_transfer':
                    return (
                        log.from === taker.address &&
                        log.to !== COW_SWAP_SETTLEMENT_PROTOCOL &&
                        log.currencyId === taker.cryptoCurrency.id
                    )
                /* istanbul ignore next */
                default:
                    return notReachable(log)
            }
        }
    )

    if (!transferLog) {
        return failure({ type: 'no_erc20_transfer_log' })
    }

    return success({
        type: 'withdrawal_transaction',
        hash: transactionData.hash,
        blockNumber: transactionData.blockNumber,
        amountInInvestmentCurrency: {
            amount: transferLog.amount,
            currency: taker.cryptoCurrency,
        },
    })
}

export const parseCardRechargeTransaction = (
    transactionData: IndexedTransaction,
    taker: Taker
): Result<unknown, CardRechargeTransaction> => {
    const transferLog = transactionData.logs.find(
        (log): log is ERC20TransferLog => {
            switch (log.type) {
                case 'unknown':
                case 'added_owner':
                case 'approval':
                case 'account_deployed':
                case 'threshold_updated':
                case 'set_allowance':
                case 'enable_module':
                case 'disable_module':
                case 'safe_module_transaction':
                case 'safe_received':
                case 'safe_module_transaction_for_native_fee_payment':
                case 'user_operation_event':
                case 'user_operation_revert_reason':
                case 'native_wrapper_deposit':
                case 'native_wrapper_withdraw':
                    return false
                case 'erc20_transfer':
                    return (
                        log.from === taker.address &&
                        log.to === COW_SWAP_SETTLEMENT_PROTOCOL &&
                        log.currencyId === taker.cryptoCurrency.id
                    )
                /* istanbul ignore next */
                default:
                    return notReachable(log)
            }
        }
    )

    if (!transferLog) {
        return failure({ type: 'no_erc20_transfer_log' })
    }

    return success({
        type: 'card_recharge_transaction',
        hash: transactionData.hash,
        blockNumber: transactionData.blockNumber,
        amountInInvestmentCurrency: {
            amount: transferLog.amount,
            currency: taker.cryptoCurrency,
        },
    })
}

export const parseEarnTakerTransaction = (
    transactionData: IndexedTransaction,
    taker: Taker
): Result<unknown, EarnTakerTransaction> =>
    oneOf(transactionData, [
        parseEarnWithdrawalTransaction(transactionData, taker),
        parseCardRechargeTransaction(transactionData, taker),
        parseEarnDepositTransaction(transactionData, taker),
        success({
            type: 'unknown' as const,
            transactionData,
            blockNumber: transactionData.blockNumber,
            hash: transactionData.hash,
        }),
    ])
