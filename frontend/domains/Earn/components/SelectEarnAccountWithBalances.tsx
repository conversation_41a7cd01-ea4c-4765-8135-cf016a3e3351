import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Clickable } from '@zeal/uikit/Clickable'
import { Column } from '@zeal/uikit/Column'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { keys } from '@zeal/toolkit/Object'

import { Earn, Taker } from '@zeal/domains/Earn'
import { TakerSubtextListItem } from '@zeal/domains/Earn/components/TakerSubtextListItem'

import { TakerSubtextListItemNoBalance } from './TakerSubtextListItemNoBalance'

import { EARN_PRIMARY_INVESTMENT_ASSETS_MAP } from '../constants'

type Props = {
    earn: Earn
    onMsg: (msg: Msg) => void
}

export type Msg =
    | { type: 'close' }
    | { type: 'on_earn_account_selected'; taker: Taker }

export const SelectEarnAccountWithBalances = ({ onMsg, earn }: Props) => {
    const zealTakers = earn.takers.filter((taker) =>
        keys(EARN_PRIMARY_INVESTMENT_ASSETS_MAP).some(
            (takerType) => takerType === taker.type
        )
    )

    const deployedPartnerTakers = earn.takers.filter((taker) => {
        switch (taker.state) {
            case 'deployed':
                return !zealTakers.includes(taker)

            case 'not_deployed':
                return false

            /* istanbul ignore next */
            default:
                return notReachable(taker)
        }
    })

    return (
        <Screen
            padding="form"
            background="light"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <Column spacing={16}>
                <ActionBar
                    left={
                        <Clickable onClick={() => onMsg({ type: 'close' })}>
                            <Row spacing={4}>
                                <BackIcon size={24} color="iconDefault" />
                                <Text
                                    variant="title3"
                                    weight="semi_bold"
                                    color="textPrimary"
                                >
                                    <FormattedMessage
                                        id="earn.deposit.select_account.title"
                                        defaultMessage="Select Earn account"
                                    />
                                </Text>
                            </Row>
                        </Clickable>
                    }
                />

                <Column spacing={8}>
                    {[...zealTakers, ...deployedPartnerTakers].map((taker) => {
                        const takerPortfolio =
                            earn.takerPortfolioMap[taker.type]
                        return takerPortfolio ? (
                            <TakerSubtextListItem
                                takerPortfolio={takerPortfolio}
                                takerApyMap={earn.takerApyMap}
                                key={taker.type}
                                taker={taker}
                                onClick={() =>
                                    onMsg({
                                        type: 'on_earn_account_selected',
                                        taker,
                                    })
                                }
                            />
                        ) : (
                            <TakerSubtextListItemNoBalance
                                takerApyMap={earn.takerApyMap}
                                taker={taker}
                                key={taker.type}
                            />
                        )
                    })}
                </Column>
            </Column>
        </Screen>
    )
}
