import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { BannerSolid } from '@zeal/uikit/BannerSolid'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { Header } from '@zeal/uikit/Header'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { EarnEth } from '@zeal/uikit/Icon/EarnEth'
import { IconButton } from '@zeal/uikit/IconButton'
import { Screen } from '@zeal/uikit/Screen'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import {
    EarnTakerMetrics,
    Taker,
    TakerApyMap,
    TakerPortfolioMap2,
} from '@zeal/domains/Earn'
import { EthTakerBulletPointsGroup } from '@zeal/domains/Earn/components/EthTakerBulletPointsGroup'
import { TakerInvestmentDetails } from '@zeal/domains/Earn/features/TakerInvestmentDetails'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

type Props = {
    defaultCurrencyConfig: DefaultCurrencyConfig
    taker: Taker
    variant: 'just_information' | 'information_and_interaction'
    takerApyMap: TakerApyMap
    takerPortfolioMap: TakerPortfolioMap2
    earnTakerMetrics: EarnTakerMetrics
    installationId: string
    onMsg: (msg: Msg) => void
}

export type Msg =
    | { type: 'close' }
    | { type: 'on_earn_deposit_asset_click'; taker: Taker }
    | Extract<
          MsgOf<typeof TakerInvestmentDetails>,
          {
              type:
                  | 'close'
                  | 'on_usd_taker_metrics_loaded'
                  | 'on_eur_taker_metrics_loaded'
                  | 'on_chf_taker_metrics_loaded'
                  | 'on_earn_deposit_asset_click'
          }
      >

export const TakerInfoScreen = ({
    defaultCurrencyConfig,
    taker,
    takerApyMap,
    takerPortfolioMap,
    installationId,
    earnTakerMetrics,
    variant,
    onMsg,
}: Props) => {
    switch (taker.type) {
        case 'usd':
        case 'eur':
        case 'chf':
            return (
                <TakerInvestmentDetails
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    takerPortfolioMap={takerPortfolioMap}
                    taker={taker}
                    takerApyMap={takerApyMap}
                    earnTakerMetrics={earnTakerMetrics}
                    installationId={installationId}
                    location="earn_screen"
                    variant={variant}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                            case 'on_usd_taker_metrics_loaded':
                            case 'on_eur_taker_metrics_loaded':
                            case 'on_chf_taker_metrics_loaded':
                            case 'on_earn_deposit_asset_click':
                                onMsg(msg)
                                break
                            case 'on_earn_withdrawal_asset_click':
                                throw new ImperativeError(
                                    'Cannot withdraw from earn in Zero state'
                                )
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'eth':
            return (
                <Screen
                    padding="form"
                    background="light"
                    onNavigateBack={() => onMsg({ type: 'close' })}
                >
                    <ActionBar
                        left={
                            <IconButton
                                variant="on_light"
                                onClick={() => {
                                    onMsg({ type: 'close' })
                                }}
                            >
                                {({ color }) => (
                                    <BackIcon size={24} color={color} />
                                )}
                            </IconButton>
                        }
                    />
                    <Column spacing={8} fill shrink>
                        <Column spacing={16} fill shrink>
                            <Header
                                icon={({ size }) => <EarnEth size={size} />}
                                title={
                                    <FormattedMessage
                                        id="earn.currency.eth"
                                        defaultMessage="Earn Ethereum"
                                    />
                                }
                            />
                            <Column spacing={0} alignY="stretch">
                                <EthTakerBulletPointsGroup
                                    takerType={taker.type}
                                    takerApyMap={takerApyMap}
                                />
                                <BannerSolid
                                    variant="light"
                                    rounded
                                    title={
                                        <FormattedMessage
                                            id="earn.risk-banner.title"
                                            defaultMessage="Understand the risks"
                                        />
                                    }
                                    subtitle={
                                        <FormattedMessage
                                            id="earn.risk-banner.subtitle"
                                            defaultMessage="This is a self-custodial product with no regulatory protection against loss."
                                        />
                                    }
                                />
                            </Column>
                        </Column>
                        {(() => {
                            switch (variant) {
                                case 'just_information':
                                    return null
                                case 'information_and_interaction':
                                    return (
                                        <Actions variant="default">
                                            <Button
                                                variant="secondary"
                                                size="regular"
                                                onClick={() => {
                                                    onMsg({ type: 'close' })
                                                }}
                                            >
                                                <FormattedMessage
                                                    id="action.cancel"
                                                    defaultMessage="Cancel"
                                                />
                                            </Button>
                                            <Button
                                                variant="primary"
                                                size="regular"
                                                onClick={() => {
                                                    onMsg({
                                                        type: 'on_earn_deposit_asset_click',
                                                        taker,
                                                    })
                                                }}
                                            >
                                                <FormattedMessage
                                                    id="earn.confirm.currency.cta"
                                                    defaultMessage="Deposit"
                                                />
                                            </Button>
                                        </Actions>
                                    )
                                default:
                                    return notReachable(variant)
                            }
                        })()}
                    </Column>
                </Screen>
            )
        /* istanbul ignore next */
        default:
            return notReachable(taker.type)
    }
}
