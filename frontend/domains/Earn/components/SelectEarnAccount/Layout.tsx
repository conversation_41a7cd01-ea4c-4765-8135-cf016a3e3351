import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { HeaderV2 } from '@zeal/uikit/HeaderV2'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { NotSelected } from '@zeal/uikit/Icon/NotSelected'
import { Radio } from '@zeal/uikit/Icon/Radio'
import { IconButton } from '@zeal/uikit/IconButton'
import { SubtextListItem } from '@zeal/uikit/ListItem/SubtextListItem'
import { Screen } from '@zeal/uikit/Screen'
import { ScrollContainer } from '@zeal/uikit/ScrollContainer'
import { Text } from '@zeal/uikit/Text'
import { TextButton } from '@zeal/uikit/TextButton'

import { notReachable } from '@zeal/toolkit'
import { getFormattedPercentage } from '@zeal/toolkit/Percentage'

import { CardConfig } from '@zeal/domains/Card'
import { Earn, Taker } from '@zeal/domains/Earn'
import { TakerAvatar } from '@zeal/domains/Earn/components/TakerAvatar'
import { SupportButton } from '@zeal/domains/Support/features/SupportButton'

type Props = {
    earn: Earn
    installationId: string
    cardConfig: CardConfig
    selectedTaker: Taker
    onMsg: (msg: Msg) => void
}

type Msg =
    | {
          type: 'close'
      }
    | {
          type: 'on_earn_account_selected'
          taker: Taker
      }
    | { type: 'on_taker_info_clicked'; taker: Taker }

export const Layout = ({
    onMsg,
    earn,
    installationId,
    cardConfig,
    selectedTaker,
}: Props) => {
    return (
        <Screen
            background="light"
            padding="form"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <ActionBar
                left={
                    <IconButton
                        variant="on_light"
                        onClick={() => onMsg({ type: 'close' })}
                    >
                        {({ color }) => <BackIcon size={24} color={color} />}
                    </IconButton>
                }
                right={
                    <SupportButton
                        variant={{
                            type: 'intercom_and_zendesk',
                            cardConfig,
                        }}
                        layoutVariant="icon_button"
                        installationId={installationId}
                        location="select_earn_account"
                    />
                }
            />
            <Column spacing={8} shrink fill>
                <Column spacing={24} shrink fill>
                    <HeaderV2
                        size="large"
                        align="left"
                        title={
                            <FormattedMessage
                                id="selectEarnAccount2.title"
                                defaultMessage="Select account"
                            />
                        }
                        subtitle={null}
                    />
                    <ScrollContainer withFloatingActions={false}>
                        <Column spacing={8} shrink>
                            {earn.takers.map((taker) => {
                                const apy = earn.takerApyMap[taker.type]
                                const formattedApy = getFormattedPercentage(apy)

                                switch (taker.type) {
                                    case 'eur': {
                                        return (
                                            <SubtextListItem
                                                key={taker.type}
                                                variant="outline"
                                                size="large"
                                                disabled={false}
                                                wrapPrimaryText={true}
                                                onClick={() => {
                                                    onMsg({
                                                        type: 'on_earn_account_selected',
                                                        taker,
                                                    })
                                                }}
                                                avatar={({ size }) => (
                                                    <TakerAvatar
                                                        takerType={taker.type}
                                                        size={size}
                                                    />
                                                )}
                                                primaryText={
                                                    <FormattedMessage
                                                        id="selectEarnAccount2.eur.title"
                                                        defaultMessage="EUR · Earn {apy} per year"
                                                        values={{
                                                            apy: formattedApy,
                                                        }}
                                                    />
                                                }
                                                side={{
                                                    rightIcon: ({ size }) =>
                                                        selectedTaker.type ===
                                                        taker.type ? (
                                                            <Radio
                                                                size={size}
                                                                color="iconAccent2"
                                                            />
                                                        ) : (
                                                            <NotSelected
                                                                size={size}
                                                                color="iconDefault"
                                                            />
                                                        ),
                                                }}
                                                subItems={
                                                    <Text variant="footnote">
                                                        <FormattedMessage
                                                            id="selectEarnAccount.eur.description.steps"
                                                            defaultMessage="· Withdraw funds 24/7, no lockups {br}· Interest accrues every second {br}· Overcollateralised loans with <link>Aave</link>"
                                                            values={{
                                                                br: '\n',
                                                                link: (msg) => (
                                                                    <TextButton
                                                                        onClick={() =>
                                                                            onMsg(
                                                                                {
                                                                                    type: 'on_taker_info_clicked',
                                                                                    taker: taker,
                                                                                }
                                                                            )
                                                                        }
                                                                    >
                                                                        {msg}
                                                                    </TextButton>
                                                                ),
                                                            }}
                                                        />
                                                    </Text>
                                                }
                                            />
                                        )
                                    }
                                    case 'usd': {
                                        return (
                                            <SubtextListItem
                                                key={taker.type}
                                                variant="outline"
                                                size="large"
                                                disabled={false}
                                                wrapPrimaryText={true}
                                                onClick={() => {
                                                    onMsg({
                                                        type: 'on_earn_account_selected',
                                                        taker,
                                                    })
                                                }}
                                                avatar={({ size }) => (
                                                    <TakerAvatar
                                                        takerType={taker.type}
                                                        size={size}
                                                    />
                                                )}
                                                primaryText={
                                                    <FormattedMessage
                                                        id="selectEarnAccount2.usd.title"
                                                        defaultMessage="USD · Earn {apy} per year"
                                                        values={{
                                                            apy: formattedApy,
                                                        }}
                                                    />
                                                }
                                                side={{
                                                    rightIcon: ({ size }) =>
                                                        selectedTaker.type ===
                                                        taker.type ? (
                                                            <Radio
                                                                size={size}
                                                                color="iconAccent2"
                                                            />
                                                        ) : (
                                                            <NotSelected
                                                                size={size}
                                                                color="iconDefault"
                                                            />
                                                        ),
                                                }}
                                                subItems={
                                                    <Text variant="footnote">
                                                        <FormattedMessage
                                                            id="selectEarnAccount.usd.description.steps"
                                                            defaultMessage="· Withdraw funds 24/7, no lockups {br}· Interest accrues every second {br}· Overcollateralised deposits in <link>Sky</link>"
                                                            values={{
                                                                br: '\n',
                                                                link: (msg) => (
                                                                    <TextButton
                                                                        onClick={() =>
                                                                            onMsg(
                                                                                {
                                                                                    type: 'on_taker_info_clicked',
                                                                                    taker: taker,
                                                                                }
                                                                            )
                                                                        }
                                                                    >
                                                                        {msg}
                                                                    </TextButton>
                                                                ),
                                                            }}
                                                        />
                                                    </Text>
                                                }
                                            />
                                        )
                                    }
                                    case 'chf':
                                        return (
                                            <SubtextListItem
                                                key={taker.type}
                                                variant="outline"
                                                size="large"
                                                disabled={false}
                                                wrapPrimaryText={true}
                                                onClick={() => {
                                                    onMsg({
                                                        type: 'on_earn_account_selected',
                                                        taker,
                                                    })
                                                }}
                                                avatar={({ size }) => (
                                                    <TakerAvatar
                                                        takerType={taker.type}
                                                        size={size}
                                                    />
                                                )}
                                                primaryText={
                                                    <FormattedMessage
                                                        id="selectEarnAccount2.chf.title"
                                                        defaultMessage="CHF · Earn {apy} per year"
                                                        values={{
                                                            apy: formattedApy,
                                                        }}
                                                    />
                                                }
                                                side={{
                                                    rightIcon: ({ size }) =>
                                                        selectedTaker.type ===
                                                        taker.type ? (
                                                            <Radio
                                                                size={size}
                                                                color="iconAccent2"
                                                            />
                                                        ) : (
                                                            <NotSelected
                                                                size={size}
                                                                color="iconDefault"
                                                            />
                                                        ),
                                                }}
                                                subItems={
                                                    <Text variant="footnote">
                                                        <FormattedMessage
                                                            id="selectEarnAccount.chf.description.steps"
                                                            defaultMessage="· Withdraw funds 24/7, no lockups {br}· Interest accrues every second {br}· Overcollateralised deposits in <link>Frankencoin</link>"
                                                            values={{
                                                                br: '\n',
                                                                link: (msg) => (
                                                                    <TextButton
                                                                        onClick={() =>
                                                                            onMsg(
                                                                                {
                                                                                    type: 'on_taker_info_clicked',
                                                                                    taker: taker,
                                                                                }
                                                                            )
                                                                        }
                                                                    >
                                                                        {msg}
                                                                    </TextButton>
                                                                ),
                                                            }}
                                                        />
                                                    </Text>
                                                }
                                            />
                                        )
                                    case 'eth':
                                        return (
                                            <SubtextListItem
                                                key={taker.type}
                                                variant="outline"
                                                size="large"
                                                disabled={false}
                                                wrapPrimaryText={true}
                                                onClick={() => {
                                                    onMsg({
                                                        type: 'on_earn_account_selected',
                                                        taker,
                                                    })
                                                }}
                                                avatar={({ size }) => (
                                                    <TakerAvatar
                                                        takerType={taker.type}
                                                        size={size}
                                                    />
                                                )}
                                                primaryText={
                                                    <FormattedMessage
                                                        id="selectEarnAccount2.eth.title"
                                                        defaultMessage="ETH · Earn {apy} per year"
                                                        values={{
                                                            apy: formattedApy,
                                                        }}
                                                    />
                                                }
                                                side={{
                                                    rightIcon: ({ size }) =>
                                                        selectedTaker.type ===
                                                        taker.type ? (
                                                            <Radio
                                                                size={size}
                                                                color="iconAccent2"
                                                            />
                                                        ) : (
                                                            <NotSelected
                                                                size={size}
                                                                color="iconDefault"
                                                            />
                                                        ),
                                                }}
                                                subItems={
                                                    <Text variant="footnote">
                                                        <FormattedMessage
                                                            id="selectEarnAccount.eth.description.steps"
                                                            defaultMessage="· Withdraw funds 24/7, no lockups {br}· Interest accrues every second {br}· Staking yields from <link>Lido</link>"
                                                            values={{
                                                                br: '\n',
                                                                link: (msg) => (
                                                                    <TextButton
                                                                        onClick={() =>
                                                                            onMsg(
                                                                                {
                                                                                    type: 'on_taker_info_clicked',
                                                                                    taker: taker,
                                                                                }
                                                                            )
                                                                        }
                                                                    >
                                                                        {msg}
                                                                    </TextButton>
                                                                ),
                                                            }}
                                                        />
                                                    </Text>
                                                }
                                            />
                                        )
                                    // istanbul ignore next
                                    default:
                                        return notReachable(taker.type)
                                }
                            })}
                        </Column>
                    </ScrollContainer>
                </Column>
                <Actions variant="default">
                    <Button
                        variant="primary"
                        size="regular"
                        onClick={() =>
                            onMsg({
                                type: 'on_earn_account_selected',
                                taker: selectedTaker,
                            })
                        }
                    >
                        <FormattedMessage
                            id="action.select"
                            defaultMessage="Select"
                        />
                    </Button>
                </Actions>
            </Column>
        </Screen>
    )
}
