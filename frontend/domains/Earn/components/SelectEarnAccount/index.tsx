import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { CardConfig } from '@zeal/domains/Card'
import { Earn, EarnTakerMetrics, Taker } from '@zeal/domains/Earn'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { Layout } from './Layout'
import { Modal, State as ModalState } from './Modal'

type Props = {
    earn: Earn
    selectedTaker: Taker

    installationId: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    earnTakerMetrics: EarnTakerMetrics
    cardConfig: CardConfig
    onMsg: (msg: Msg) => void
}
type Msg =
    | Extract<
          MsgOf<typeof Layout>,
          { type: 'close' | 'on_earn_account_selected' }
      >
    | Extract<
          MsgOf<typeof Modal>,
          {
              type:
                  | 'on_usd_taker_metrics_loaded'
                  | 'on_eur_taker_metrics_loaded'
                  | 'on_chf_taker_metrics_loaded'
          }
      >

export const SelectEarnAccount = ({
    onMsg,
    earn,
    installationId,
    selectedTaker,
    cardConfig,
    defaultCurrencyConfig,
    earnTakerMetrics,
}: Props) => {
    const [modal, setModal] = useState<ModalState>({ type: 'closed' })
    return (
        <>
            <Layout
                earn={earn}
                installationId={installationId}
                cardConfig={cardConfig}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'on_earn_account_selected':
                        case 'close':
                            onMsg(msg)
                            break
                        case 'on_taker_info_clicked':
                            setModal({
                                type: 'taker_investment_details',
                                taker: msg.taker,
                            })
                            break

                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
                selectedTaker={selectedTaker}
            />
            <Modal
                state={modal}
                defaultCurrencyConfig={defaultCurrencyConfig}
                installationId={installationId}
                earn={earn}
                earnTakerMetrics={earnTakerMetrics}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'close':
                            setModal({ type: 'closed' })
                            break
                        case 'on_usd_taker_metrics_loaded':
                        case 'on_eur_taker_metrics_loaded':
                        case 'on_chf_taker_metrics_loaded':
                            onMsg(msg)
                            break

                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
            />
        </>
    )
}
