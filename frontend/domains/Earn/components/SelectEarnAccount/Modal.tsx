import { Modal as UIModal } from '@zeal/uikit/Modal'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Earn, EarnTakerMetrics, Taker } from '@zeal/domains/Earn'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { TakerInfoScreen } from '../TakerInfoScreen'

type Props = {
    state: State
    earn: Earn
    defaultCurrencyConfig: DefaultCurrencyConfig
    earnTakerMetrics: EarnTakerMetrics
    installationId: string
    onMsg: (msg: Msg) => void
}
export type State =
    | { type: 'closed' }
    | { type: 'taker_investment_details'; taker: Taker }

type Msg = Extract<
    MsgOf<typeof TakerInfoScreen>,
    {
        type:
            | 'close'
            | 'on_usd_taker_metrics_loaded'
            | 'on_eur_taker_metrics_loaded'
            | 'on_chf_taker_metrics_loaded'
    }
>

export const Modal = ({
    onMsg,
    state,
    defaultCurrencyConfig,
    installationId,
    earn,
    earnTakerMetrics,
}: Props) => {
    switch (state.type) {
        case 'closed':
            return null
        case 'taker_investment_details':
            return (
                <UIModal>
                    <TakerInfoScreen
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        installationId={installationId}
                        earnTakerMetrics={earnTakerMetrics}
                        takerPortfolioMap={earn.takerPortfolioMap}
                        taker={state.taker}
                        takerApyMap={earn.takerApyMap}
                        variant="just_information"
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                case 'on_usd_taker_metrics_loaded':
                                case 'on_eur_taker_metrics_loaded':
                                case 'on_chf_taker_metrics_loaded':
                                    onMsg(msg)
                                    break
                                case 'on_earn_deposit_asset_click':
                                    throw new ImperativeError(
                                        'not possible to deposit to earn from select account just info screen'
                                    )
                                /* istanbul ignore next */
                                default:
                                    notReachable(msg)
                            }
                        }}
                    />
                </UIModal>
            )

        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
