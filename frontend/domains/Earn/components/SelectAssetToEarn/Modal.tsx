import { Modal as UIModal } from '@zeal/uikit/Modal'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import {
    EarnTakerMetrics,
    Taker,
    TakerApyMap,
    TakerPortfolioMap2,
} from '@zeal/domains/Earn'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { TakerInfoScreen } from '../TakerInfoScreen'

type Props = {
    defaultCurrencyConfig: DefaultCurrencyConfig
    state: State
    earnTakerMetrics: EarnTakerMetrics
    takerPortfolioMap: TakerPortfolioMap2
    installationId: string
    onMsg: (msg: Msg) => void
}

export type Msg = { type: 'close' } | MsgOf<typeof TakerInfoScreen>

export type State =
    | { type: 'closed' }
    | {
          type: 'taker_info_screen'
          taker: Taker
          takerApyMap: TakerApyMap
      }

export const Modal = ({
    defaultCurrencyConfig,
    state,
    earnTakerMetrics,
    takerPortfolioMap,
    installationId,
    onMsg,
}: Props) => {
    switch (state.type) {
        case 'closed':
            return null
        case 'taker_info_screen':
            return (
                <UIModal>
                    <TakerInfoScreen
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        installationId={installationId}
                        earnTakerMetrics={earnTakerMetrics}
                        takerPortfolioMap={takerPortfolioMap}
                        taker={state.taker}
                        takerApyMap={state.takerApyMap}
                        variant="information_and_interaction"
                        onMsg={onMsg}
                    />
                </UIModal>
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
