import { FormattedMessage } from 'react-intl'

import { notReachable } from '@zeal/toolkit'

import { TakerType } from '@zeal/domains/Earn'

type Props = {
    takerType: TakerType
}

export const TakerTitleShort = ({ takerType }: Props) => {
    switch (takerType) {
        case 'usd':
            return (
                <FormattedMessage
                    id="earn.takerListItemShort.earnUSD.title"
                    defaultMessage="Sky USD"
                />
            )
        case 'eur':
            return (
                <FormattedMessage
                    id="earn.takerListItemShort.earnEURO.title"
                    defaultMessage="Aave EUR"
                />
            )
        case 'eth':
            return (
                <FormattedMessage
                    id="earn.takerListItemShort.earnETH.title"
                    defaultMessage="ETH Earn"
                />
            )

        case 'chf':
            return (
                <FormattedMessage
                    id="earn.takerListItemShort.earnCHF.title"
                    defaultMessage="Frankencoin CHF"
                />
            )

        /* istanbul ignore next */
        default:
            return notReachable(takerType)
    }
}
