import { notReachable } from '@zeal/toolkit'
import { isNonEmptyArray } from '@zeal/toolkit/NonEmptyArray'

import { ConfiguredEarn, DeployedTaker } from '@zeal/domains/Earn'
import { getTakerLabel } from '@zeal/domains/Earn/helpers/getTakerLabel'
import { sortTakersByBalance } from '@zeal/domains/Earn/helpers/sortTakersByBalance'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

export const InvestmentAssetsLabel = ({
    earn,
    defaultCurrencyConfig,
}: {
    earn: ConfiguredEarn
    defaultCurrencyConfig: DefaultCurrencyConfig
}) => {
    const deployedTakers = earn.takers
        .filter((taker): taker is DeployedTaker => {
            switch (taker.state) {
                case 'not_deployed':
                    return false
                case 'deployed':
                    return true

                /* istanbul ignore next */
                default:
                    return notReachable(taker)
            }
        })
        .toSorted(
            sortTakersByBalance({
                takerPortfolioMap: earn.takerPortfolioMap,
                defaultCurrencyConfig,
            })
        )

    const [lastLabel, ...labels] = deployedTakers
        .map((taker) => getTakerLabel(taker.type))
        .toReversed()

    return (
        (isNonEmptyArray(labels) ? `${labels.join(', ')} & ` : '') + lastLabel
    )
}
