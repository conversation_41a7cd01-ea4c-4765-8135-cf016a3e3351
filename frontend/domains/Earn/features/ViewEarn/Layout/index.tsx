import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Button } from '@zeal/uikit/Button'
import { Clickable } from '@zeal/uikit/Clickable'
import { Column } from '@zeal/uikit/Column'
import { FancyButton } from '@zeal/uikit/FancyButton'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { BoldAdd } from '@zeal/uikit/Icon/BoldAdd'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { ScrollContainer } from '@zeal/uikit/ScrollContainer'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { PollableData } from '@zeal/toolkit/LoadableData/PollableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { keys } from '@zeal/toolkit/Object'

import { Account } from '@zeal/domains/Account'
import { CardConfig } from '@zeal/domains/Card'
import {
    ConfiguredEarn,
    Taker,
    TakerPortfolioMap2,
    TotalEarningsInDefaultCurrencyMap,
} from '@zeal/domains/Earn'
import { TakerTransactionsMap } from '@zeal/domains/Earn/api/fetchTakerTransactionsMap'
import { TakerSubtextListItemNoBalance } from '@zeal/domains/Earn/components/TakerSubtextListItemNoBalance'
import { TakerSubtextListItemWithEarnings } from '@zeal/domains/Earn/components/TakerSubtextListItemWithEarnings'
import { EARN_PRIMARY_INVESTMENT_ASSETS_MAP } from '@zeal/domains/Earn/constants'
import { sortTakersByBalance } from '@zeal/domains/Earn/helpers/sortTakersByBalance'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { Header } from './Header'

type Props = {
    owner: Account
    earn: ConfiguredEarn
    cardConfig: CardConfig
    installationId: string
    pollable: PollableData<
        {
            takerPortfolioMap: TakerPortfolioMap2
            takerTransactionsMap: TakerTransactionsMap
        },
        unknown
    >
    totalEarningsInDefaultCurrencyMap: TotalEarningsInDefaultCurrencyMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    keyStoreMap: KeyStoreMap
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | { type: 'on_add_another_asset_click' }
    | {
          type: 'on_earn_taker_list_item_click'
          taker: Taker
      }
    | { type: 'on_earn_deposit_asset_click'; taker: Taker }
    | { type: 'on_earn_withdraw_asset_click'; taker: Taker }
    | MsgOf<typeof Header>

export const Layout = ({
    owner,
    earn,
    installationId,
    cardConfig,
    pollable,
    defaultCurrencyConfig,
    totalEarningsInDefaultCurrencyMap,
    keyStoreMap,
    onMsg,
}: Props) => {
    const [takerWithMostValue] = earn.takers.toSorted(
        sortTakersByBalance({
            takerPortfolioMap: earn.takerPortfolioMap,
            defaultCurrencyConfig,
        })
    )

    const deployedTakers = earn.takers.filter((taker) => {
        switch (taker.state) {
            case 'deployed':
                return true
            case 'not_deployed':
                return false
            /* istanbul ignore next */
            default:
                return notReachable(taker)
        }
    })

    const zealDeployedTakers = deployedTakers.filter((taker) => {
        return keys(EARN_PRIMARY_INVESTMENT_ASSETS_MAP).some(
            (takerType) => takerType === taker.type
        )
    })

    return (
        <Screen
            padding="form"
            background="light"
            onNavigateBack={() => {
                onMsg({ type: 'close' })
            }}
        >
            <Column spacing={8} fill shrink>
                <Column fill shrink spacing={16}>
                    <ActionBar
                        left={
                            <Clickable onClick={() => onMsg({ type: 'close' })}>
                                <Row spacing={4}>
                                    <BackIcon size={24} color="iconDefault" />
                                    <Text
                                        variant="title3"
                                        weight="semi_bold"
                                        color="textPrimary"
                                    >
                                        <FormattedMessage
                                            id="earn.asset_view.title"
                                            defaultMessage="Earn"
                                        />
                                    </Text>
                                </Row>
                            </Clickable>
                        }
                        right={
                            zealDeployedTakers.length <
                                keys(EARN_PRIMARY_INVESTMENT_ASSETS_MAP)
                                    .length && (
                                <FancyButton
                                    right={null}
                                    color="secondary"
                                    rounded
                                    left={() => (
                                        <Row spacing={2}>
                                            <Text
                                                color="gray20"
                                                variant="callout"
                                                weight="regular"
                                            >
                                                <FormattedMessage
                                                    id="earn.add_asset"
                                                    defaultMessage="Add asset"
                                                />
                                            </Text>
                                            <BoldAdd size={20} color="gray20" />
                                        </Row>
                                    )}
                                    onClick={() =>
                                        onMsg({
                                            type: 'on_add_another_asset_click',
                                        })
                                    }
                                />
                            )
                        }
                    />
                    <ScrollContainer withFloatingActions={false}>
                        <Column shrink spacing={8}>
                            <Header
                                onMsg={onMsg}
                                earn={earn}
                                account={owner}
                                cardConfig={cardConfig}
                                defaultCurrencyConfig={defaultCurrencyConfig}
                                pollable={pollable}
                                keyStoreMap={keyStoreMap}
                                totalEarningsInDefaultCurrencyMap={
                                    totalEarningsInDefaultCurrencyMap
                                }
                            />

                            {deployedTakers.map((taker) => {
                                const takerPortfolioMap = (() => {
                                    switch (pollable.type) {
                                        case 'loaded':
                                        case 'reloading':
                                        case 'subsequent_failed':
                                            return pollable.data
                                                .takerPortfolioMap

                                        case 'loading':
                                        case 'error':
                                            return earn.takerPortfolioMap
                                        /* istanbul ignore next */
                                        default:
                                            return notReachable(pollable)
                                    }
                                })()

                                const takerPortfolio =
                                    takerPortfolioMap[taker.type]

                                return takerPortfolio ? (
                                    <TakerSubtextListItemWithEarnings
                                        transactionsPollable={pollable}
                                        takerPortfolio={takerPortfolio}
                                        key={taker.type}
                                        taker={taker}
                                        onClick={() =>
                                            onMsg({
                                                type: 'on_earn_taker_list_item_click',
                                                taker,
                                            })
                                        }
                                    />
                                ) : (
                                    <TakerSubtextListItemNoBalance
                                        takerApyMap={earn.takerApyMap}
                                        taker={taker}
                                        key={taker.type}
                                    />
                                )
                            })}
                        </Column>
                    </ScrollContainer>
                </Column>

                <Actions variant="default" direction="row">
                    <Button
                        variant="secondary"
                        size="regular"
                        onClick={() => {
                            postUserEvent({
                                type: 'EarnWithdrawEarnFlowEnteredEvent',
                                location: 'earn_screen',
                                asset: takerWithMostValue.type,
                                installationId,
                            })
                            onMsg({
                                type: 'on_earn_withdraw_asset_click',
                                taker: takerWithMostValue,
                            })
                        }}
                    >
                        <FormattedMessage
                            id="earn.withdraw.withdraw"
                            defaultMessage="Withdraw"
                        />
                    </Button>
                    <Button
                        variant="primary"
                        size="regular"
                        onClick={() => {
                            onMsg({
                                type: 'on_earn_deposit_asset_click',
                                taker: takerWithMostValue,
                            })
                        }}
                    >
                        <FormattedMessage
                            id="earn.deposit.deposit"
                            defaultMessage="Deposit"
                        />
                    </Button>
                </Actions>
            </Column>
        </Screen>
    )
}
