import { FormattedMessage } from 'react-intl'

import { CardWidgetTag } from '@zeal/uikit/CardWidget/CardWidgetTag'
import { RechargeLightning } from '@zeal/uikit/Icon/RechargeLightning'
import { Text } from '@zeal/uikit/Text'

import { CardRechargeEnabled, ConfiguredEarn } from '@zeal/domains/Earn'

type Msg = {
    type: 'on_enabled_recharge_card_tag_clicked'
    cardRecharge: CardRechargeEnabled
    earn: ConfiguredEarn
}

type Props = {
    cardRecharge: CardRechargeEnabled
    earn: ConfiguredEarn
    onMsg: (msg: Msg) => void
}

export const RechargeOnTag = ({ onMsg, cardRecharge, earn }: Props) => {
    return (
        <CardWidgetTag
            variant="neutral"
            onClick={() =>
                onMsg({
                    type: 'on_enabled_recharge_card_tag_clicked',
                    cardRecharge,
                    earn,
                })
            }
        >
            <Text variant="paragraph" weight="regular" color="gray20">
                <FormattedMessage
                    defaultMessage="Recharge"
                    id="earn.recharge_card_tag.recharge"
                />
            </Text>
            <Text variant="paragraph" weight="regular" color="teal40">
                <FormattedMessage
                    defaultMessage="On"
                    id="earn.recharge_card_tag.on"
                />
            </Text>
            <RechargeLightning size={16} color="teal40" />
        </CardWidgetTag>
    )
}
