import { Modal as UIModal } from '@zeal/uikit/Modal'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { Earn, EarnDepositWithSwapRequest } from '@zeal/domains/Earn'
import { SelectEarnAccountWithBalances } from '@zeal/domains/Earn/components/SelectEarnAccountWithBalances'
import { SelectToken } from '@zeal/domains/Earn/components/SelectToken'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { DeployAndSubmitDeposit } from './DeployAndSubmitDeposit'

import { Pollable } from '../validation'

type Props = {
    state: State
    pollable: Pollable
    fromCurrencies: CryptoCurrency[]
    fromAccountPortfolio: ServerPortfolio2

    earn: Earn
    earnOwner: Account
    sessionPassword: string
    accountsMap: AccountsMap
    keystoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    feePresetMap: FeePresetMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    installationId: string
    gasCurrencyPresetMap: GasCurrencyPresetMap
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | MsgOf<typeof SelectToken>
    | MsgOf<typeof DeployAndSubmitDeposit>
    | MsgOf<typeof SelectEarnAccountWithBalances>

export type State =
    | { type: 'closed' }
    | { type: 'select_from_currency' }
    | { type: 'submit_deposit'; earnDepositRequest: EarnDepositWithSwapRequest }
    | { type: 'select_earn_account' }

export const Modal = ({
    state,
    earnOwner,
    portfolioMap,
    fromCurrencies,
    pollable,
    gasCurrencyPresetMap,
    accountsMap,
    feePresetMap,
    earn,
    fromAccountPortfolio,
    installationId,
    keystoreMap,
    networkMap,
    networkRPCMap,
    sessionPassword,
    currencyHiddenMap,
    currencyPinMap,
    defaultCurrencyConfig,
    onMsg,
}: Props) => {
    switch (state.type) {
        case 'closed':
            return null
        case 'select_earn_account':
            return (
                <UIModal>
                    <SelectEarnAccountWithBalances earn={earn} onMsg={onMsg} />
                </UIModal>
            )

        case 'select_from_currency':
            return (
                <UIModal>
                    <SelectToken
                        currencyHiddenMap={currencyHiddenMap}
                        currencyPinMap={currencyPinMap}
                        fromCurrency={pollable.params.fromCurrency}
                        networkMap={networkMap}
                        fromAccount={pollable.params.fromAccount}
                        fromCurrencies={fromCurrencies}
                        fromAccountPortfolio={fromAccountPortfolio}
                        onMsg={onMsg}
                    />
                </UIModal>
            )

        case 'submit_deposit':
            return (
                <UIModal>
                    <DeployAndSubmitDeposit
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        earn={earn}
                        earnOwner={earnOwner}
                        taker={pollable.params.taker}
                        accountsMap={accountsMap}
                        feePresetMap={feePresetMap}
                        gasCurrencyPresetMap={gasCurrencyPresetMap}
                        installationId={installationId}
                        keyStoreMap={keystoreMap}
                        networkMap={networkMap}
                        portfolioMap={portfolioMap}
                        networkRPCMap={networkRPCMap}
                        sessionPassword={sessionPassword}
                        onMsg={onMsg}
                        earnDepositRequest={state.earnDepositRequest}
                    />
                </UIModal>
            )

        default:
            return notReachable(state)
    }
}
