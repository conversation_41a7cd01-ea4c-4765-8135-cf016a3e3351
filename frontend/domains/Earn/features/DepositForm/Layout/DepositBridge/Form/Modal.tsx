import { Modal as UIModal } from '@zeal/uikit/Modal'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
} from '@zeal/domains/Currency'
import { Earn } from '@zeal/domains/Earn'
import { SelectEarnAccountWithBalances } from '@zeal/domains/Earn/components/SelectEarnAccountWithBalances'
import { SelectToken } from '@zeal/domains/Earn/components/SelectToken'
import { NetworkMap } from '@zeal/domains/Network'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { Pollable } from './validation'

type Props = {
    state: State
    pollable: Pollable
    fromCurrencies: CryptoCurrency[]
    fromAccountPortfolio: ServerPortfolio2

    earn: Earn
    networkMap: NetworkMap
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | MsgOf<typeof SelectToken>
    | MsgOf<typeof SelectEarnAccountWithBalances>

export type State =
    | { type: 'closed' }
    | { type: 'select_from_currency' }
    | { type: 'select_earn_account' }

export const Modal = ({
    state,
    fromCurrencies,
    pollable,
    earn,
    fromAccountPortfolio,
    networkMap,
    currencyHiddenMap,
    currencyPinMap,
    onMsg,
}: Props) => {
    switch (state.type) {
        case 'closed':
            return null
        case 'select_earn_account':
            return (
                <UIModal>
                    <SelectEarnAccountWithBalances earn={earn} onMsg={onMsg} />
                </UIModal>
            )

        case 'select_from_currency':
            return (
                <UIModal>
                    <SelectToken
                        currencyHiddenMap={currencyHiddenMap}
                        currencyPinMap={currencyPinMap}
                        fromCurrency={pollable.params.fromCurrency}
                        networkMap={networkMap}
                        fromAccount={pollable.params.fromAccount}
                        fromCurrencies={fromCurrencies}
                        fromAccountPortfolio={fromAccountPortfolio}
                        onMsg={onMsg}
                    />
                </UIModal>
            )

        default:
            return notReachable(state)
    }
}
