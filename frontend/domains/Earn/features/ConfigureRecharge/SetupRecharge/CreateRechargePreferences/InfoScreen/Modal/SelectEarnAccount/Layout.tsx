import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Avatar } from '@zeal/uikit/Avatar'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { HeaderV2 } from '@zeal/uikit/HeaderV2'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { NoEarn } from '@zeal/uikit/Icon/NoEarn'
import { NotSelected } from '@zeal/uikit/Icon/NotSelected'
import { Radio } from '@zeal/uikit/Icon/Radio'
import { IconButton } from '@zeal/uikit/IconButton'
import { SubtextListItem } from '@zeal/uikit/ListItem/SubtextListItem'
import { Screen } from '@zeal/uikit/Screen'
import { ScrollContainer } from '@zeal/uikit/ScrollContainer'
import { Text } from '@zeal/uikit/Text'
import { TextButton } from '@zeal/uikit/TextButton'

import { notReachable } from '@zeal/toolkit'
import { getFormattedPercentage } from '@zeal/toolkit/Percentage'

import { ReadonlySignerSelectedOnboardedCardConfig } from '@zeal/domains/Card'
import {
    CHFTakerType,
    EURTakerType,
    NotConfiguredEarn,
    NotDeployedTaker,
    USDTakerType,
} from '@zeal/domains/Earn'
import { TakerAvatar } from '@zeal/domains/Earn/components/TakerAvatar'
import { SupportButton } from '@zeal/domains/Support/features/SupportButton'

type Props = {
    earn: NotConfiguredEarn
    installationId: string
    onMsg: (msg: Msg) => void
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    defaultSelectedTakerType: USDTakerType | EURTakerType | CHFTakerType | null
}

type Msg =
    | {
          type: 'on_earn_account_selected'
          takerType: USDTakerType | EURTakerType | CHFTakerType
          earn: NotConfiguredEarn
      }
    | {
          type: 'on_zero_percent_per_year_selected'
          earn: NotConfiguredEarn
      }
    | {
          type: 'close'
      }
    | { type: 'on_taker_info_clicked'; taker: NotDeployedTaker }

export const Layout = ({
    earn,
    installationId,
    cardConfig,
    onMsg,
    defaultSelectedTakerType,
}: Props) => {
    return (
        <Screen
            background="light"
            padding="form"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <ActionBar
                left={
                    <IconButton
                        variant="on_light"
                        onClick={() => onMsg({ type: 'close' })}
                    >
                        {({ color }) => <BackIcon size={24} color={color} />}
                    </IconButton>
                }
                right={
                    <SupportButton
                        variant={{
                            type: 'intercom_and_zendesk',
                            cardConfig,
                        }}
                        layoutVariant="icon_button"
                        installationId={installationId}
                        location="gnosis_card_order"
                    />
                }
            />
            <Column spacing={8} shrink>
                <ScrollContainer withFloatingActions={false}>
                    <Column spacing={24} shrink>
                        <HeaderV2
                            size="large"
                            align="left"
                            title={
                                <FormattedMessage
                                    id="selectEarnAccount.title"
                                    defaultMessage="Select Currency"
                                />
                            }
                            subtitle={
                                <FormattedMessage
                                    id="selectEarnAccount.subtitle"
                                    defaultMessage="You can change it anytime"
                                />
                            }
                        />
                        <Column spacing={8} shrink>
                            {earn.takers.map((taker) => {
                                const apy = earn.takerApyMap[taker.type]
                                const formattedApy = getFormattedPercentage(apy)

                                switch (taker.type) {
                                    case 'eur': {
                                        return (
                                            <SubtextListItem
                                                key={taker.type}
                                                variant="outline"
                                                size="large"
                                                disabled={false}
                                                wrapPrimaryText={true}
                                                onClick={() => {
                                                    onMsg({
                                                        type: 'on_earn_account_selected',
                                                        takerType: 'eur',
                                                        earn,
                                                    })
                                                }}
                                                avatar={({ size }) => (
                                                    <TakerAvatar
                                                        takerType={taker.type}
                                                        size={size}
                                                    />
                                                )}
                                                primaryText={
                                                    <FormattedMessage
                                                        id="selectEarnAccount.eur.title"
                                                        defaultMessage="{apy} per year in EUR"
                                                        values={{
                                                            apy: formattedApy,
                                                        }}
                                                    />
                                                }
                                                side={{
                                                    rightIcon: ({ size }) =>
                                                        defaultSelectedTakerType ===
                                                        taker.type ? (
                                                            <Radio
                                                                size={size}
                                                                color="iconAccent2"
                                                            />
                                                        ) : (
                                                            <NotSelected
                                                                size={size}
                                                                color="iconDefault"
                                                            />
                                                        ),
                                                }}
                                                subItems={
                                                    <Text variant="footnote">
                                                        <FormattedMessage
                                                            id="selectEarnAccount.eur.description.steps"
                                                            defaultMessage="· Withdraw funds 24/7, no lockups {br}· Interest accrues every second {br}· Overcollateralised loans with <link>Aave</link>"
                                                            values={{
                                                                br: '\n',
                                                                link: (msg) => (
                                                                    <TextButton
                                                                        onClick={() =>
                                                                            onMsg(
                                                                                {
                                                                                    type: 'on_taker_info_clicked',
                                                                                    taker: taker,
                                                                                }
                                                                            )
                                                                        }
                                                                    >
                                                                        {msg}
                                                                    </TextButton>
                                                                ),
                                                            }}
                                                        />
                                                    </Text>
                                                }
                                            />
                                        )
                                    }
                                    case 'usd': {
                                        return (
                                            <SubtextListItem
                                                key={taker.type}
                                                variant="outline"
                                                size="large"
                                                disabled={false}
                                                wrapPrimaryText={true}
                                                onClick={() => {
                                                    onMsg({
                                                        type: 'on_earn_account_selected',
                                                        takerType: 'usd',
                                                        earn,
                                                    })
                                                }}
                                                avatar={({ size }) => (
                                                    <TakerAvatar
                                                        takerType={taker.type}
                                                        size={size}
                                                    />
                                                )}
                                                primaryText={
                                                    <FormattedMessage
                                                        id="selectEarnAccount.usd.title"
                                                        defaultMessage="{apy} per year in USD"
                                                        values={{
                                                            apy: formattedApy,
                                                        }}
                                                    />
                                                }
                                                side={{
                                                    rightIcon: ({ size }) =>
                                                        defaultSelectedTakerType ===
                                                        taker.type ? (
                                                            <Radio
                                                                size={size}
                                                                color="iconAccent2"
                                                            />
                                                        ) : (
                                                            <NotSelected
                                                                size={size}
                                                                color="iconDefault"
                                                            />
                                                        ),
                                                }}
                                                subItems={
                                                    <Text variant="footnote">
                                                        <FormattedMessage
                                                            id="selectEarnAccount.usd.description.steps"
                                                            defaultMessage="· Withdraw funds 24/7, no lockups {br}· Interest accrues every second {br}· Overcollateralised deposits in <link>Sky</link>"
                                                            values={{
                                                                br: '\n',
                                                                link: (msg) => (
                                                                    <TextButton
                                                                        onClick={() =>
                                                                            onMsg(
                                                                                {
                                                                                    type: 'on_taker_info_clicked',
                                                                                    taker: taker,
                                                                                }
                                                                            )
                                                                        }
                                                                    >
                                                                        {msg}
                                                                    </TextButton>
                                                                ),
                                                            }}
                                                        />
                                                    </Text>
                                                }
                                            />
                                        )
                                    }
                                    case 'chf':
                                        return (
                                            <SubtextListItem
                                                key={taker.type}
                                                variant="outline"
                                                size="large"
                                                disabled={false}
                                                wrapPrimaryText={true}
                                                onClick={() => {
                                                    onMsg({
                                                        type: 'on_earn_account_selected',
                                                        takerType: 'chf',
                                                        earn,
                                                    })
                                                }}
                                                avatar={({ size }) => (
                                                    <TakerAvatar
                                                        takerType={taker.type}
                                                        size={size}
                                                    />
                                                )}
                                                primaryText={
                                                    <FormattedMessage
                                                        id="selectEarnAccount.chf.title"
                                                        defaultMessage="{apy} per year in CHF"
                                                        values={{
                                                            apy: formattedApy,
                                                        }}
                                                    />
                                                }
                                                side={{
                                                    rightIcon: ({ size }) =>
                                                        defaultSelectedTakerType ===
                                                        taker.type ? (
                                                            <Radio
                                                                size={size}
                                                                color="iconAccent2"
                                                            />
                                                        ) : (
                                                            <NotSelected
                                                                size={size}
                                                                color="iconDefault"
                                                            />
                                                        ),
                                                }}
                                                subItems={
                                                    <Text variant="footnote">
                                                        <FormattedMessage
                                                            id="selectEarnAccount.chf.description.steps"
                                                            defaultMessage="· Withdraw funds 24/7, no lockups {br}· Interest accrues every second {br}· Overcollateralised deposits in <link>Frankencoin</link>"
                                                            values={{
                                                                br: '\n',
                                                                link: (msg) => (
                                                                    <TextButton
                                                                        onClick={() =>
                                                                            onMsg(
                                                                                {
                                                                                    type: 'on_taker_info_clicked',
                                                                                    taker: taker,
                                                                                }
                                                                            )
                                                                        }
                                                                    >
                                                                        {msg}
                                                                    </TextButton>
                                                                ),
                                                            }}
                                                        />
                                                    </Text>
                                                }
                                            />
                                        )
                                    case 'eth':
                                        return null
                                    // istanbul ignore next
                                    default:
                                        return notReachable(taker.type)
                                }
                            })}
                            <SubtextListItem
                                variant="outline"
                                size="large"
                                disabled={false}
                                wrapPrimaryText={true}
                                onClick={() => {
                                    onMsg({
                                        type: 'on_zero_percent_per_year_selected',
                                        earn,
                                    })
                                }}
                                avatar={({ size }) => (
                                    <Avatar size={size}>
                                        <NoEarn
                                            size={size}
                                            color="iconDefault"
                                        />
                                    </Avatar>
                                )}
                                primaryText={
                                    <FormattedMessage
                                        id="selectEarnAccount.zero.title"
                                        defaultMessage="0% per year"
                                    />
                                }
                                side={{
                                    rightIcon: ({ size }) =>
                                        !defaultSelectedTakerType ? (
                                            <Radio
                                                size={size}
                                                color="iconAccent2"
                                            />
                                        ) : (
                                            <NotSelected
                                                size={size}
                                                color="iconDefault"
                                            />
                                        ),
                                }}
                                subItems={
                                    <Text
                                        variant="footnote"
                                        weight="regular"
                                        color="textSecondary"
                                    >
                                        <FormattedMessage
                                            id="selectEarnAccount.zero.description_general"
                                            defaultMessage="Hold digital cash without earning interest"
                                        />
                                    </Text>
                                }
                            />
                        </Column>
                    </Column>
                </ScrollContainer>
                <Actions variant="default">
                    <Button
                        variant="primary"
                        size="regular"
                        onClick={() => {
                            if (defaultSelectedTakerType) {
                                onMsg({
                                    type: 'on_earn_account_selected',
                                    takerType: defaultSelectedTakerType,
                                    earn,
                                })
                            } else {
                                onMsg({
                                    type: 'on_zero_percent_per_year_selected',
                                    earn,
                                })
                            }
                        }}
                    >
                        <FormattedMessage
                            id="action.select"
                            defaultMessage="Select"
                        />
                    </Button>
                </Actions>
            </Column>
        </Screen>
    )
}
