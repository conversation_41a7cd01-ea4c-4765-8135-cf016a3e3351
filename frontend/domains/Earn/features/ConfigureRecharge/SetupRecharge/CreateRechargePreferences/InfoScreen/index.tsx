import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import {
    CardBalance,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { EarnTakerMetrics } from '@zeal/domains/Earn'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { Layout, RechargeEarnPreferences } from './Layout'
import { Modal, State as ModalState } from './Modal'

type Props = {
    initialRechargeEarnPreferences: RechargeEarnPreferences
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    cardBalance: CardBalance | null
    installationId: string
    onMsg: (msg: Msg) => void
    defaultCurrencyConfig: DefaultCurrencyConfig
    earnTakerMetrics: EarnTakerMetrics
}

type Msg =
    | Extract<
          MsgOf<typeof Layout>,
          {
              type:
                  | 'close'
                  | 'on_continue_clicked'
                  | 'on_dont_link_account_clicked'
          }
      >
    | Extract<
          MsgOf<typeof Modal>,
          {
              type:
                  | 'on_eur_taker_metrics_loaded'
                  | 'on_usd_taker_metrics_loaded'
                  | 'on_chf_taker_metrics_loaded'
          }
      >

export const InfoScreen = ({
    onMsg,
    initialRechargeEarnPreferences,
    cardBalance,
    cardConfig,
    installationId,
    defaultCurrencyConfig,
    earnTakerMetrics,
}: Props) => {
    const [modal, setModal] = useState<ModalState>({ type: 'closed' })
    const [rechargeEarnPreferences, setRechargeEarnPreferences] =
        useState<RechargeEarnPreferences>(initialRechargeEarnPreferences)
    return (
        <>
            <Layout
                rechargeEarnPreferences={rechargeEarnPreferences}
                cardConfig={cardConfig}
                cardBalance={cardBalance}
                installationId={installationId}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'on_choose_earn_clicked':
                            setModal({
                                type: 'select_earn_account',
                                earn: msg.earn,
                                takerType: msg.takerType,
                            })
                            break
                        case 'on_continue_clicked':
                        case 'on_dont_link_account_clicked':
                        case 'close':
                            onMsg(msg)
                            break

                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
            />
            <Modal
                state={modal}
                cardConfig={cardConfig}
                installationId={installationId}
                onMsg={(msg) => {
                    switch (msg.type) {
                        case 'on_earn_account_selected':
                            setModal({ type: 'closed' })
                            setRechargeEarnPreferences({
                                type: 'taker_for_not_configured_earn',
                                takerType: msg.takerType,
                                earn: msg.earn,
                            })
                            break
                        case 'on_zero_percent_per_year_selected':
                            setModal({ type: 'closed' })
                            setRechargeEarnPreferences({
                                type: 'taker_for_not_configured_earn',
                                takerType: null,
                                earn: msg.earn,
                            })
                            break
                        case 'close':
                            setModal({ type: 'closed' })
                            break
                        case 'on_eur_taker_metrics_loaded':
                        case 'on_usd_taker_metrics_loaded':
                        case 'on_chf_taker_metrics_loaded':
                            onMsg(msg)
                            break
                        /* istanbul ignore next */
                        default:
                            notReachable(msg)
                    }
                }}
                defaultCurrencyConfig={defaultCurrencyConfig}
                earnTakerMetrics={earnTakerMetrics}
            />
        </>
    )
}
