import { Modal as UIModal } from '@zeal/uikit/Modal'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { ReadonlySignerSelectedOnboardedCardConfig } from '@zeal/domains/Card'
import {
    CHFTakerType,
    EarnTakerMetrics,
    EURTakerType,
    NotConfiguredEarn,
    USDTakerType,
} from '@zeal/domains/Earn'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { SelectEarnAccount } from './SelectEarnAccount'

type Props = {
    state: State
    onMsg: (msg: Msg) => void
    installationId: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    earnTakerMetrics: EarnTakerMetrics
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
}
export type State =
    | { type: 'closed' }
    | {
          type: 'select_earn_account'
          earn: NotConfiguredEarn
          takerType: USDTakerType | EURTakerType | CHFTakerType | null
      }

type Msg = MsgOf<typeof SelectEarnAccount>

export const Modal = ({
    state,
    onMsg,
    cardConfig,
    installationId,
    defaultCurrencyConfig,
    earnTakerMetrics,
}: Props) => {
    switch (state.type) {
        case 'closed':
            return null
        case 'select_earn_account':
            return (
                <UIModal>
                    <SelectEarnAccount
                        earn={state.earn}
                        installationId={installationId}
                        onMsg={onMsg}
                        cardConfig={cardConfig}
                        defaultSelectedTakerType={state.takerType}
                        defaultCurrencyConfig={defaultCurrencyConfig}
                        earnTakerMetrics={earnTakerMetrics}
                    />
                </UIModal>
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
