import { FormattedMessage } from 'react-intl'

import { But<PERSON> } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { BoldTickMedium } from '@zeal/uikit/Icon/BoldTickMedium'
import { InfoCircleOutline } from '@zeal/uikit/Icon/InfoCircleOutline'
import { MarketingCard } from '@zeal/uikit/MarketingCard'
import { Row } from '@zeal/uikit/Row'
import { Spacer } from '@zeal/uikit/Spacer'
import { Text } from '@zeal/uikit/Text'

import { Taker, TakerApyMap } from '@zeal/domains/Earn'
import { TakerAPYTitle } from '@zeal/domains/Earn/components/TakerAPYTitle'
import { TakerAvatar } from '@zeal/domains/Earn/components/TakerAvatar'
import { TakerHistoricalReturnsChart } from '@zeal/domains/Earn/components/TakerHistoricalReturnsChart'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

type Props = {
    taker: Taker
    takerApyMap: TakerApyMap
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'on_taker_investment_tile_click'; taker: Taker }
    | { type: 'on_taker_investment_tile_deposit_click'; taker: Taker }

export const FrankencoinTakerInvestmentTile = ({
    taker,
    takerApyMap,
    installationId,
    onMsg,
}: Props) => {
    return (
        <MarketingCard
            variant="earn_promo"
            onClick={() =>
                onMsg({ type: 'on_taker_investment_tile_click', taker })
            }
        >
            <Column spacing={20}>
                <Column spacing={16}>
                    <Column spacing={4}>
                        <Row spacing={6}>
                            <TakerAPYTitle
                                takerApyMap={takerApyMap}
                                taker={taker}
                            />
                            <Spacer />
                            <InfoCircleOutline size={32} />
                        </Row>
                    </Column>
                    <Column spacing={4}>
                        <Row spacing={6}>
                            <TakerAvatar takerType={taker.type} size={20} />
                            <Text
                                variant="callout"
                                weight="regular"
                                color="gray20"
                            >
                                <FormattedMessage
                                    id="taker-metadata.earn.chf"
                                    defaultMessage="Earn in digital CHF"
                                />
                            </Text>
                        </Row>
                        <Row spacing={6}>
                            <BoldTickMedium size={20} color="gray20" />
                            <Text
                                variant="callout"
                                weight="regular"
                                color="gray20"
                            >
                                <FormattedMessage
                                    id="taker-metadata.earn.chf.cashout24"
                                    defaultMessage="Cash out instantly, 24/7"
                                />
                            </Text>
                        </Row>
                        <Row spacing={6}>
                            <BoldTickMedium size={20} color="gray20" />
                            <Text
                                variant="callout"
                                weight="regular"
                                color="gray20"
                            >
                                <FormattedMessage
                                    id="taker-metadata.earn.chf.yield"
                                    defaultMessage="Yield accrues every second"
                                />
                            </Text>
                        </Row>
                        <Row spacing={6}>
                            <BoldTickMedium size={20} color="gray20" />
                            <Text
                                variant="callout"
                                weight="regular"
                                color="gray20"
                            >
                                <FormattedMessage
                                    id="taker-metadata.earn.chf.trusted"
                                    defaultMessage="Trusted with Fr. 28M"
                                />
                            </Text>
                        </Row>
                    </Column>
                </Column>
                <Row spacing={0}>
                    <Button
                        variant="primary"
                        size="regular"
                        onClick={() => {
                            postUserEvent({
                                type: 'EarnDepositButtonClickedEvent',
                                asset: 'chf',
                                location: 'investment_tile',
                                installationId,
                            })
                            onMsg({
                                type: 'on_taker_investment_tile_deposit_click',
                                taker,
                            })
                        }}
                    >
                        <FormattedMessage id="test" defaultMessage="Deposit" />
                    </Button>
                </Row>
                <TakerHistoricalReturnsChart taker={taker} />
            </Column>
        </MarketingCard>
    )
}
