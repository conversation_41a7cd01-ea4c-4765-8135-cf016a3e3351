import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Column } from '@zeal/uikit/Column'
import { CloseCross } from '@zeal/uikit/Icon/CloseCross'
import { IconButton } from '@zeal/uikit/IconButton'
import { Popup } from '@zeal/uikit/Popup'
import { Text } from '@zeal/uikit/Text'

type Props = {
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'close' }

export const HowIsSkyBacked = ({ onMsg }: Props) => (
    <Popup.Layout onMsg={onMsg}>
        <Popup.Content>
            <ActionBar
                left={null}
                right={
                    <IconButton
                        variant="on_light"
                        onClick={() =>
                            onMsg({
                                type: 'close',
                            })
                        }
                    >
                        {({ color }) => <CloseCross size={24} color={color} />}
                    </IconButton>
                }
            />
            <Column spacing={12}>
                <Text variant="title3" weight="semi_bold" color="gray5">
                    <FormattedMessage
                        id="earn.usd.how-is-sky-backed-popup.title"
                        defaultMessage="How is Sky USD backed, and what happens to my money if Zeal goes bankrupt?"
                    />
                </Text>
                <Text variant="callout" weight="regular" color="gray20">
                    <FormattedMessage
                        id="earn.usd.how-is-sky-backed-popup.text"
                        defaultMessage="Sky USD is fully backed and over-collateralised by a combination of digital assets held in secure smart contracts and real-world assets like US Treasuries. Reserves can be audited in real-time onchain even from within Zeal, providing transparency and security. In the unlikely event Zeal shuts down, your assets remain secured onchain, fully under your control, and accessible through other compatible wallets."
                    />
                </Text>
            </Column>
        </Popup.Content>
    </Popup.Layout>
)
