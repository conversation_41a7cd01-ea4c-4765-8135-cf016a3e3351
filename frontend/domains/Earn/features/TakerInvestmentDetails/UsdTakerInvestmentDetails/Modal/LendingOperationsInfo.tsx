import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Column } from '@zeal/uikit/Column'
import { CloseCross } from '@zeal/uikit/Icon/CloseCross'
import { IconButton } from '@zeal/uikit/IconButton'
import { Popup } from '@zeal/uikit/Popup'
import { Text } from '@zeal/uikit/Text'

type Props = {
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'close' }

export const LendingOperationsInfo = ({ onMsg }: Props) => (
    <Popup.Layout onMsg={onMsg}>
        <Popup.Content>
            <ActionBar
                left={null}
                right={
                    <IconButton
                        variant="on_light"
                        onClick={() =>
                            onMsg({
                                type: 'close',
                            })
                        }
                    >
                        {({ color }) => <CloseCross size={24} color={color} />}
                    </IconButton>
                }
            />
            <Column spacing={12}>
                <Text variant="title3" weight="semi_bold" color="gray5">
                    <FormattedMessage
                        id="earn.usd.lending-operations-popup.title"
                        defaultMessage="Lending operations"
                    />
                </Text>
                <Text variant="callout" weight="regular" color="gray20">
                    <FormattedMessage
                        id="earn.usd.lending-operations-popup.text"
                        defaultMessage="Sky USD generates yield by lending stablecoins through decentralised lending markets like Morpho and Spark. Your stablecoins are loaned out to borrowers who deposit significantly more collateral—such as ETH or BTC—than the value of their loan. This approach, called overcollateralisation, ensures there’s always sufficient collateral to cover loans, greatly reducing risk. The collected interest and occasional liquidation fees paid by borrowers provide reliable, transparent, and secure returns."
                    />
                </Text>
            </Column>
        </Popup.Content>
    </Popup.Layout>
)
