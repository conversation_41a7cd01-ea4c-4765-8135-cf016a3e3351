import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Column } from '@zeal/uikit/Column'
import { CloseCross } from '@zeal/uikit/Icon/CloseCross'
import { IconButton } from '@zeal/uikit/IconButton'
import { Popup } from '@zeal/uikit/Popup'
import { Text } from '@zeal/uikit/Text'

type Props = {
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'close' }

export const CanILoseMyPrincipal = ({ onMsg }: Props) => (
    <Popup.Layout onMsg={onMsg}>
        <Popup.Content>
            <ActionBar
                left={null}
                right={
                    <IconButton
                        variant="on_light"
                        onClick={() =>
                            onMsg({
                                type: 'close',
                            })
                        }
                    >
                        {({ color }) => <CloseCross size={24} color={color} />}
                    </IconButton>
                }
            />
            <Column spacing={12}>
                <Text variant="title3" weight="semi_bold" color="gray5">
                    <FormattedMessage
                        id="earn.usd.can-I-lose-my-principal-popup.title"
                        defaultMessage="Can I realistically lose my principal, and under what circumstances?"
                    />
                </Text>
                <Text variant="callout" weight="regular" color="gray20">
                    <FormattedMessage
                        id="earn.usd.can-I-lose-my-principal-popup.text"
                        defaultMessage="While extremely rare, it’s theoretically possible. Your funds are protected by strict risk management and high collateralisation. The realistic worst-case scenario would involve unprecedented market conditions, such as multiple stablecoins losing their peg simultaneously—something that has never happened before."
                    />
                </Text>
            </Column>
        </Popup.Content>
    </Popup.Layout>
)
