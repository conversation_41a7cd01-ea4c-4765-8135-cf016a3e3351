import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Column } from '@zeal/uikit/Column'
import { CloseCross } from '@zeal/uikit/Icon/CloseCross'
import { IconButton } from '@zeal/uikit/IconButton'
import { Popup } from '@zeal/uikit/Popup'
import { Text } from '@zeal/uikit/Text'

type Props = {
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'close' }

export const MarketMakingOperationsInfo = ({ onMsg }: Props) => (
    <Popup.Layout onMsg={onMsg}>
        <Popup.Content>
            <ActionBar
                left={null}
                right={
                    <IconButton
                        variant="on_light"
                        onClick={() =>
                            onMsg({
                                type: 'close',
                            })
                        }
                    >
                        {({ color }) => <CloseCross size={24} color={color} />}
                    </IconButton>
                }
            />
            <Column spacing={12}>
                <Text variant="title3" weight="semi_bold" color="gray5">
                    <FormattedMessage
                        id="earn.usd.market-making-operations-popup.title"
                        defaultMessage="Market Making Operations"
                    />
                </Text>
                <Text variant="callout" weight="regular" color="gray20">
                    <FormattedMessage
                        id="earn.usd.market-making-operations-popup.text"
                        defaultMessage="Sky USD earns additional yield by participating in decentralised exchanges (AMMs) such as Curve or Uniswap. By providing liquidity—placing your stablecoins into pools that facilitate crypto trading—Sky USD captures fees generated from trades. These liquidity pools are selected carefully to minimise volatility, primarily using stablecoin-to-stablecoin pairs to significantly reduce risks like impermanent loss, keeping your assets both safe and accessible."
                    />
                </Text>
            </Column>
        </Popup.Content>
    </Popup.Layout>
)
