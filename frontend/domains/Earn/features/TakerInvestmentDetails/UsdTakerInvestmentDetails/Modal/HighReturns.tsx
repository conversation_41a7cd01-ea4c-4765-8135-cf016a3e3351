import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Column } from '@zeal/uikit/Column'
import { CloseCross } from '@zeal/uikit/Icon/CloseCross'
import { IconButton } from '@zeal/uikit/IconButton'
import { Popup } from '@zeal/uikit/Popup'
import { Text } from '@zeal/uikit/Text'

type Props = {
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'close' }

export const HighReturns = ({ onMsg }: Props) => (
    <Popup.Layout onMsg={onMsg}>
        <Popup.Content>
            <ActionBar
                left={null}
                right={
                    <IconButton
                        variant="on_light"
                        onClick={() =>
                            onMsg({
                                type: 'close',
                            })
                        }
                    >
                        {({ color }) => <CloseCross size={24} color={color} />}
                    </IconButton>
                }
            />
            <Column spacing={12}>
                <Text variant="title3" weight="semi_bold" color="gray5">
                    <FormattedMessage
                        id="earn.usd.high-returns-popup.title"
                        defaultMessage="How can the returns be so high, especially compared to traditional banks?"
                    />
                </Text>
                <Text variant="callout" weight="regular" color="gray20">
                    <FormattedMessage
                        id="earn.usd.high-returns-popup.text"
                        defaultMessage="Sky USD generates yields primarily through decentralised finance (DeFi) protocols, which automate peer-to-peer lending and liquidity provision, removing traditional banking overhead and intermediaries. These efficiencies, combined with robust risk controls, allow significantly higher returns compared to traditional banks."
                    />
                </Text>
            </Column>
        </Popup.Content>
    </Popup.Layout>
)
