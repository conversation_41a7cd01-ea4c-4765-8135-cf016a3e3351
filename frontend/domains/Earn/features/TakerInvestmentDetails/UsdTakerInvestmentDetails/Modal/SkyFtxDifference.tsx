import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Column } from '@zeal/uikit/Column'
import { CloseCross } from '@zeal/uikit/Icon/CloseCross'
import { IconButton } from '@zeal/uikit/IconButton'
import { Popup } from '@zeal/uikit/Popup'
import { Text } from '@zeal/uikit/Text'

type Props = {
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'close' }

export const SkyFtxDifference = ({ onMsg }: Props) => (
    <Popup.Layout onMsg={onMsg}>
        <Popup.Content>
            <ActionBar
                left={null}
                right={
                    <IconButton
                        variant="on_light"
                        onClick={() =>
                            onMsg({
                                type: 'close',
                            })
                        }
                    >
                        {({ color }) => <CloseCross size={24} color={color} />}
                    </IconButton>
                }
            />
            <Column spacing={12}>
                <Text variant="title3" weight="semi_bold" color="gray5">
                    <FormattedMessage
                        id="earn.usd.ftx-difference-popup.title"
                        defaultMessage="How is this different from FTX, Celsius, BlockFi, or Luna?"
                    />
                </Text>
                <Text variant="callout" weight="regular" color="gray20">
                    <FormattedMessage
                        id="earn.usd.ftx-difference-popup.text"
                        defaultMessage="Sky is fundamentally different. Unlike FTX, Celsius, BlockFi, or Luna—which relied heavily on centralised custody, opaque asset management, and risky leveraged positions—Sky USD utilises transparent, audited, decentralised smart contracts and maintains full onchain transparency. You retain complete self-custodial control, significantly reducing counterparty risks associated with centralised failures."
                    />
                </Text>
            </Column>
        </Popup.Content>
    </Popup.Layout>
)
