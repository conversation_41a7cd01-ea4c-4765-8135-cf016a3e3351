import { FormattedMessage, useIntl } from 'react-intl'

import { ActionBar as UIActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Button } from '@zeal/uikit/Button'
import { Clickable } from '@zeal/uikit/Clickable'
import { Column } from '@zeal/uikit/Column'
import { Divider } from '@zeal/uikit/Divider'
import { FancyButton } from '@zeal/uikit/FancyButton'
import { Group } from '@zeal/uikit/Group'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { CreditCard } from '@zeal/uikit/Icon/CreditCard'
import { LightArrowDown2 } from '@zeal/uikit/Icon/LightArrowDown2'
import { IconButton } from '@zeal/uikit/IconButton'
import { AmountInput } from '@zeal/uikit/Input/AmountInput'
import { ListItem } from '@zeal/uikit/ListItem'
import { NextStepSeparator } from '@zeal/uikit/NextStepSeparator'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { ScrollContainer } from '@zeal/uikit/ScrollContainer'
import { Text } from '@zeal/uikit/Text'

import { noop } from '@zeal/toolkit'
import { notReachable } from '@zeal/toolkit/notReachable'

import { AvatarWithoutBadge as AccountAvatar } from '@zeal/domains/Account/components/Avatar'
import { CardBalance } from '@zeal/domains/Card'
import { Avatar as CurrencyAvatar } from '@zeal/domains/Currency/components/Avatar'
import { MaxButton } from '@zeal/domains/Currency/components/MaxButton'
import { SwapsIOQuote } from '@zeal/domains/Currency/domains/SwapsIO'
import { FormattedMoneyPrecise } from '@zeal/domains/Money/components/FormattedMoneyPrecise'
import { useMoneyFormat } from '@zeal/domains/Money/hooks/useMoneyFormat'
import { NetworkMap } from '@zeal/domains/Network'
import { Badge } from '@zeal/domains/Network/components/Badge'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { Portfolio2 } from '@zeal/domains/Portfolio'

import { Banner } from './Banner'
import {
    getMaxBalance,
    MaxBalanceLoadable,
    Pollable,
    SubmitErrors,
    validate,
} from './validation'

type Props = {
    networkMap: NetworkMap
    pollable: Pollable
    senderPortfolio: Portfolio2
    cardBalance: CardBalance
    installationId: string
    maxBalanceLoadable: MaxBalanceLoadable
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | {
          type: 'on_amount_change'
          amount: string | null
      }
    | {
          type: 'on_from_currency_clicked'
      }
    | { type: 'on_submit_clicked'; quote: SwapsIOQuote }
    | { type: 'on_from_account_click' }

export const Layout = ({
    pollable,
    senderPortfolio,
    networkMap,
    installationId,
    cardBalance,
    maxBalanceLoadable,
    onMsg,
}: Props) => {
    const { formattedCryptoMoneyPrecise } = useMoneyFormat()
    const { formatMessage } = useIntl()

    const validationResult = validate({
        pollable,
        senderPortfolio,
        maxBalanceLoadable,
    })

    const errors = validationResult.getFailureReason() || {}

    return (
        <Screen
            padding="form"
            background="light"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <Column spacing={12} fill>
                <UIActionBar
                    left={
                        <Clickable onClick={() => onMsg({ type: 'close' })}>
                            <Row spacing={4}>
                                <BackIcon size={24} color="iconDefault" />
                                <UIActionBar.Header>
                                    <FormattedMessage
                                        id="send-card-token.form.title"
                                        defaultMessage="Add cash to Card"
                                    />
                                </UIActionBar.Header>
                            </Row>
                        </Clickable>
                    }
                />

                <Column spacing={16} shrink alignY="stretch">
                    <ScrollContainer withFloatingActions={false}>
                        <Column spacing={4}>
                            <AmountInput
                                top={
                                    <Column spacing={0}>
                                        <FancyButton
                                            color="secondary"
                                            rounded
                                            left={() => (
                                                <Row grow shrink spacing={4}>
                                                    <AccountAvatar
                                                        size={12}
                                                        account={
                                                            pollable.params
                                                                .sender
                                                        }
                                                    />
                                                    <Text
                                                        variant="caption1"
                                                        color="gray40"
                                                        weight="medium"
                                                        ellipsis
                                                    >
                                                        {
                                                            pollable.params
                                                                .sender.label
                                                        }
                                                    </Text>
                                                </Row>
                                            )}
                                            right={({ color }) => (
                                                <LightArrowDown2
                                                    size={16}
                                                    color={color}
                                                />
                                            )}
                                            onClick={() => {
                                                onMsg({
                                                    type: 'on_from_account_click',
                                                })
                                            }}
                                        />
                                        <Divider variant="secondary" />
                                    </Column>
                                }
                                content={{
                                    topLeft: (
                                        <IconButton
                                            variant="on_light"
                                            onClick={() => {
                                                onMsg({
                                                    type: 'on_from_currency_clicked',
                                                })
                                            }}
                                        >
                                            {({ color }) => (
                                                <Row spacing={4}>
                                                    <CurrencyAvatar
                                                        key={
                                                            pollable.params
                                                                .fromCurrency.id
                                                        }
                                                        currency={
                                                            pollable.params
                                                                .fromCurrency
                                                        }
                                                        rightBadge={({
                                                            size,
                                                        }) => (
                                                            <Badge
                                                                size={size}
                                                                network={findNetworkByHexChainId(
                                                                    pollable
                                                                        .params
                                                                        .fromCurrency
                                                                        .networkHexChainId,
                                                                    networkMap
                                                                )}
                                                            />
                                                        )}
                                                        size={24}
                                                    />
                                                    <Text
                                                        variant="title3"
                                                        color="textPrimary"
                                                        weight="medium"
                                                    >
                                                        {
                                                            pollable.params
                                                                .fromCurrency
                                                                .code
                                                        }
                                                    </Text>

                                                    <LightArrowDown2
                                                        size={18}
                                                        color={color}
                                                    />
                                                </Row>
                                            )}
                                        </IconButton>
                                    ),
                                    topRight: ({ onBlur, onFocus }) => (
                                        <AmountInput.Input
                                            onFocus={onFocus}
                                            onBlur={onBlur}
                                            label={formatMessage({
                                                id: 'send-card-token.form.send-amount',
                                                defaultMessage: 'Top-up amount',
                                            })}
                                            fraction={
                                                pollable.params.fromCurrency
                                                    .fraction
                                            }
                                            autoFocus
                                            readOnly={false}
                                            prefix=""
                                            amount={pollable.params.amount}
                                            onChange={(value) => {
                                                onMsg({
                                                    type: 'on_amount_change',
                                                    amount: value,
                                                })
                                            }}
                                            onSubmitEditing={() =>
                                                validationResult.tap(
                                                    (quote) => {
                                                        onMsg({
                                                            type: 'on_submit_clicked',
                                                            quote,
                                                        })
                                                    }
                                                )
                                            }
                                        />
                                    ),

                                    bottomLeft: (
                                        <MaxButton
                                            installationId={installationId}
                                            location="card_add_cash"
                                            balance={getMaxBalance({
                                                maxBalanceLoadable,
                                                senderPortfolio,
                                            })}
                                            onMsg={onMsg}
                                            state={(() => {
                                                switch (
                                                    maxBalanceLoadable.type
                                                ) {
                                                    case 'loading':
                                                        return 'loading'
                                                    case 'error':
                                                    case 'loaded':
                                                        return errors.amount
                                                            ? 'error'
                                                            : 'normal'
                                                    default:
                                                        return notReachable(
                                                            maxBalanceLoadable
                                                        )
                                                }
                                            })()}
                                        />
                                    ),
                                    bottomRight: (
                                        <BalanceInDefaultCurrency
                                            variant="from"
                                            pollable={pollable}
                                        />
                                    ),
                                }}
                                state={errors.amount ? 'error' : 'normal'}
                            />

                            <NextStepSeparator />

                            <AmountInput
                                content={{
                                    topLeft: (
                                        <IconButton
                                            variant="on_light"
                                            onClick={noop}
                                        >
                                            {() => (
                                                <Row spacing={4}>
                                                    <CurrencyAvatar
                                                        key={
                                                            pollable.params
                                                                .toCurrency.id
                                                        }
                                                        currency={
                                                            pollable.params
                                                                .toCurrency
                                                        }
                                                        rightBadge={({
                                                            size,
                                                        }) => (
                                                            <Badge
                                                                size={size}
                                                                network={findNetworkByHexChainId(
                                                                    pollable
                                                                        .params
                                                                        .toCurrency
                                                                        .networkHexChainId,
                                                                    networkMap
                                                                )}
                                                            />
                                                        )}
                                                        size={24}
                                                    />
                                                    <Text
                                                        variant="title3"
                                                        color="textPrimary"
                                                        weight="medium"
                                                    >
                                                        {
                                                            pollable.params
                                                                .toCurrency.code
                                                        }
                                                    </Text>
                                                </Row>
                                            )}
                                        </IconButton>
                                    ),

                                    topRight: () => {
                                        switch (pollable.type) {
                                            case 'loaded':
                                            case 'reloading':
                                            case 'subsequent_failed': {
                                                const data =
                                                    pollable.data.getSuccessResult()
                                                return data ? (
                                                    <AmountInput.Input
                                                        onBlur={noop}
                                                        onChange={noop}
                                                        onSubmitEditing={noop}
                                                        onFocus={noop}
                                                        label={formatMessage({
                                                            id: 'card.title',
                                                            defaultMessage:
                                                                'Card',
                                                        })}
                                                        fraction={
                                                            pollable.params
                                                                .toCurrency
                                                                .fraction
                                                        }
                                                        autoFocus
                                                        readOnly
                                                        prefix=""
                                                        amount={formattedCryptoMoneyPrecise(
                                                            {
                                                                money: data.to,
                                                                withSymbol:
                                                                    false,
                                                                sign: null,
                                                            }
                                                        )}
                                                    />
                                                ) : null
                                            }
                                            case 'loading':
                                            case 'error':
                                                return null

                                            /* istanbul ignore next */
                                            default:
                                                return notReachable(pollable)
                                        }
                                    },

                                    bottomLeft: (
                                        <Text
                                            variant="paragraph"
                                            color="textSecondary"
                                        >
                                            <FormattedMessage
                                                id="currency.balance_label"
                                                defaultMessage="Balance: {amount}"
                                                values={{
                                                    amount: (
                                                        <FormattedMoneyPrecise
                                                            withSymbol={false}
                                                            sign={null}
                                                            money={
                                                                cardBalance.total
                                                            }
                                                        />
                                                    ),
                                                }}
                                            />
                                        </Text>
                                    ),
                                    bottomRight: (
                                        <BalanceInDefaultCurrency
                                            variant="to"
                                            pollable={pollable}
                                        />
                                    ),
                                }}
                                state="normal"
                            />
                            <NextStepSeparator />
                            <Group variant="default">
                                <ListItem
                                    size="large"
                                    aria-current={false}
                                    avatar={({ size }) => (
                                        <CreditCard
                                            size={size}
                                            color="gray20"
                                        />
                                    )}
                                    primaryText={
                                        <FormattedMessage
                                            id="card.list_item.title"
                                            defaultMessage="Card"
                                        />
                                    }
                                />
                            </Group>
                        </Column>
                    </ScrollContainer>
                    <Column spacing={12}>
                        <Banner pollable={pollable} error={errors.banner} />

                        <Actions variant="default">
                            <Button
                                size="regular"
                                variant="primary"
                                disabled={!!errors.submit}
                                onClick={() =>
                                    validationResult.tap((quote) => {
                                        onMsg({
                                            type: 'on_submit_clicked',
                                            quote,
                                        })
                                    })
                                }
                            >
                                <SubmitButtonLabel
                                    submitError={errors.submit}
                                />
                            </Button>
                        </Actions>
                    </Column>
                </Column>
            </Column>
        </Screen>
    )
}

const SubmitButtonLabel = ({ submitError }: { submitError?: SubmitErrors }) => {
    if (!submitError) {
        return (
            <FormattedMessage id="action.continue" defaultMessage="Continue" />
        )
    }
    switch (submitError.type) {
        case 'max_balance_still_loading':
        case 'pollable_not_loaded':
            return (
                <FormattedMessage
                    id="action.continue"
                    defaultMessage="Continue"
                />
            )
        case 'not_enough_balance':
            return (
                <FormattedMessage
                    id="submit.error.not_enough_balance"
                    defaultMessage="Not enough balance"
                />
            )
        case 'above_maximum_limit':
            return (
                <FormattedMessage
                    id="submit.error.amount_hight"
                    defaultMessage="Amount too high"
                />
            )
        case 'below_minimum_limit':
            return (
                <FormattedMessage
                    id="submit.error.amount_low"
                    defaultMessage="Amount too low"
                />
            )

        case 'amount_required':
            return (
                <FormattedMessage
                    id="submit.error.amount_required"
                    defaultMessage="Enter Amount"
                />
            )
        case 'routes_not_found':
            return (
                <FormattedMessage
                    id="submit.error.routes_not_found"
                    defaultMessage="Routes not found"
                />
            )
        default:
            return notReachable(submitError)
    }
}

const BalanceInDefaultCurrency = ({
    pollable,
    variant,
}: {
    pollable: Pollable
    variant: 'from' | 'to'
}) => {
    switch (pollable.type) {
        case 'loaded':
        case 'reloading':
        case 'subsequent_failed': {
            const successData = pollable.data.getSuccessResult()
            if (!successData) {
                return null
            }
            switch (variant) {
                case 'from':
                    return successData.fromInDefaultCurrency ? (
                        <Text>
                            <FormattedMoneyPrecise
                                withSymbol
                                sign={null}
                                money={successData.fromInDefaultCurrency}
                            />
                        </Text>
                    ) : null
                case 'to':
                    return successData.toInDefaultCurrency ? (
                        <Text>
                            <FormattedMoneyPrecise
                                withSymbol
                                sign={null}
                                money={successData.toInDefaultCurrency}
                            />
                        </Text>
                    ) : null

                /* istanbul ignore next */
                default:
                    return notReachable(variant)
            }
        }
        case 'loading':
        case 'error':
            return null

        /* istanbul ignore next */
        default:
            return notReachable(pollable)
    }
}
