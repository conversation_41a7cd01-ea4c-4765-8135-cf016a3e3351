import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Avatar } from '@zeal/uikit/Avatar'
import { CardWidget } from '@zeal/uikit/CardWidget'
import { Column } from '@zeal/uikit/Column'
import { HeaderV2 } from '@zeal/uikit/HeaderV2'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { SolidInterfacePlus } from '@zeal/uikit/Icon/SolidInterfacePlus'
import { IconButton } from '@zeal/uikit/IconButton'
import { ListItemButton } from '@zeal/uikit/ListItem'
import { Screen } from '@zeal/uikit/Screen'
import { Skeleton as UISkeleton } from '@zeal/uikit/Skeleton'
import { Text } from '@zeal/uikit/Text'

import { noop, notReachable } from '@zeal/toolkit'
import { LoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import { ZealMobilePlatform } from '@zeal/toolkit/OS/ZealPlatform'
import { openExternalURL } from '@zeal/toolkit/Window'

import {
    CardDetails,
    GnosisPayAccountOnboardedState,
    ReadonlySignerSelectedCardConfig,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { AddToWalletTitle } from '@zeal/domains/Card/components/AddToWalletTitle'
import { CopyDetailsButton } from '@zeal/domains/Card/components/CopyDetailsButton'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { SupportButton } from '@zeal/domains/Support/features/SupportButton'
import { AddToPaySystemFlowOpenedEvent } from '@zeal/domains/UserEvents'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

type Props = {
    platform: ZealMobilePlatform
    location: AddToPaySystemFlowOpenedEvent['location']
    installationId: string
    cardDetailsLoadable: LoadableData<
        CardDetails | null,
        {
            gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
            signal?: AbortSignal
        }
    >
    cardConfig:
        | ReadonlySignerSelectedCardConfig
        | ReadonlySignerSelectedOnboardedCardConfig
    onMsg: (msg: Msg) => void
}

type Msg = {
    type: 'close'
}

const ANDROID_WALLET_URL = `android-app://com.google.android.apps.walletnfcrel`
const IOS_WALLET_URL = 'shoebox://'

export const WalletDetails = ({
    platform,
    cardDetailsLoadable,
    installationId,
    cardConfig,
    location,
    onMsg,
}: Props) => {
    return (
        <Screen
            padding="form"
            background="default"
            onNavigateBack={() => {
                onMsg({ type: 'close' })
            }}
        >
            <Column shrink spacing={16}>
                <ActionBar
                    right={
                        <SupportButton
                            variant={{
                                type: 'intercom_and_zendesk',
                                cardConfig,
                            }}
                            layoutVariant="icon_button"
                            installationId={installationId}
                            location="add_to_wallet"
                        />
                    }
                    left={
                        <IconButton
                            variant="on_light"
                            onClick={() => onMsg({ type: 'close' })}
                        >
                            {({ color }) => (
                                <BackIcon size={24} color={color} />
                            )}
                        </IconButton>
                    }
                />
                <HeaderV2
                    title={<AddToWalletTitle platform={platform} />}
                    subtitle={null}
                    size="large"
                    align="left"
                />

                <Column spacing={8}>
                    <Column spacing={0}>
                        <ListItemButton
                            variant="outline_transparent"
                            aria-current={false}
                            avatar={({ size }) => (
                                <Avatar size={size} border="borderSecondary">
                                    <Text
                                        variant="callout"
                                        weight="medium"
                                        color="textPrimary"
                                        align="center"
                                    >
                                        {1}
                                    </Text>
                                </Avatar>
                            )}
                            disabled={false}
                            background="surface"
                            primaryText={
                                <FormattedMessage
                                    id="card.add-to-wallet.copy-card-number"
                                    defaultMessage="Copy card number below"
                                />
                            }
                            onClick={noop}
                        />
                        <CardDetailsContent
                            cardDetailsLoadable={cardDetailsLoadable}
                        />
                    </Column>

                    <ListItemButton
                        variant="outline"
                        aria-current={false}
                        avatar={({ size }) => (
                            <Avatar size={size} border="borderSecondary">
                                <Text
                                    variant="callout"
                                    weight="medium"
                                    color="textPrimary"
                                    align="center"
                                >
                                    {2}
                                </Text>
                            </Avatar>
                        )}
                        disabled={false}
                        background="surface"
                        primaryText={<AddToWalletTitle platform={platform} />}
                        side={{
                            rightIcon: ({ size }) => (
                                <Avatar size={size} border="borderSecondary">
                                    <SolidInterfacePlus
                                        size={size}
                                        color="iconDefault"
                                    />
                                </Avatar>
                            ),
                        }}
                        onClick={() => {
                            try {
                                switch (platform.OS) {
                                    case 'android':
                                        openExternalURL(ANDROID_WALLET_URL)

                                        break
                                    case 'ios':
                                        openExternalURL(IOS_WALLET_URL)

                                        break

                                    /* istanbul ignore next */
                                    default:
                                        notReachable(platform)
                                }

                                postUserEvent({
                                    type: 'AddToPaySystemFlowOpenedEvent',
                                    installationId,
                                    location,
                                })
                            } catch (error) {
                                captureError(error)
                            }
                        }}
                    />
                </Column>
            </Column>
        </Screen>
    )
}

const CardDetailsContent = ({
    cardDetailsLoadable,
}: {
    cardDetailsLoadable: LoadableData<
        CardDetails | null,
        {
            gnosisPayAccountOnboardedState: GnosisPayAccountOnboardedState
            signal?: AbortSignal
        }
    >
}) => {
    switch (cardDetailsLoadable.type) {
        case 'loaded':
            return cardDetailsLoadable.data ? (
                <CardWidget
                    settingsIcon={null}
                    side="back"
                    variant="active"
                    onClick={noop}
                    cardNumber={
                        <>
                            <Text
                                variant="footnote"
                                weight="regular"
                                color="textSecondary"
                            >
                                <FormattedMessage
                                    id="cards.card_number"
                                    defaultMessage="Card number"
                                />
                            </Text>
                            <CopyDetailsButton
                                cardDetails={cardDetailsLoadable.data}
                            />
                        </>
                    }
                    expiryDate={
                        <>
                            <Text
                                variant="footnote"
                                weight="regular"
                                color="textSecondary"
                            >
                                <FormattedMessage
                                    id="cards.card_expiry_date.label"
                                    defaultMessage="Expiry date"
                                />
                            </Text>
                            <Text
                                color="darkActionSecondaryDefault"
                                variant="callout"
                                weight="regular"
                            >
                                {cardDetailsLoadable.data.expiryMonth}/
                                {cardDetailsLoadable.data.expiryYear}
                            </Text>
                        </>
                    }
                    cvv={
                        <>
                            <Text
                                variant="footnote"
                                weight="regular"
                                color="textSecondary"
                            >
                                <FormattedMessage
                                    id="cards.card_cvv.label"
                                    defaultMessage="CVV"
                                />
                            </Text>
                            <Text
                                color="darkActionSecondaryDefault"
                                variant="callout"
                                weight="regular"
                            >
                                {cardDetailsLoadable.data.cvv}
                            </Text>
                        </>
                    }
                />
            ) : (
                <Skeleton />
            )

        case 'loading':
        case 'error':
            return <Skeleton />

        default:
            return notReachable(cardDetailsLoadable)
    }
}

const Skeleton = () => {
    return (
        <CardWidget
            side="back"
            variant="active"
            onClick={noop}
            settingsIcon={null}
            cardNumber={
                <>
                    <Text
                        variant="footnote"
                        weight="regular"
                        color="textSecondary"
                    >
                        <FormattedMessage
                            id="cards.card_number"
                            defaultMessage="Card number"
                        />
                    </Text>

                    <UISkeleton width={181} height={20} variant="transparent" />
                </>
            }
            expiryDate={
                <>
                    <Text
                        variant="footnote"
                        weight="regular"
                        color="textSecondary"
                    >
                        <FormattedMessage
                            id="cards.card_expiry_date.label"
                            defaultMessage="Expiry date"
                        />
                    </Text>
                    <UISkeleton width={55} height={20} variant="transparent" />
                </>
            }
            cvv={
                <>
                    <Text
                        variant="footnote"
                        weight="regular"
                        color="textSecondary"
                    >
                        <FormattedMessage
                            id="cards.card_cvv.label"
                            defaultMessage="CVV"
                        />
                    </Text>
                    <UISkeleton width={45} height={20} variant="transparent" />
                </>
            }
        />
    )
}
