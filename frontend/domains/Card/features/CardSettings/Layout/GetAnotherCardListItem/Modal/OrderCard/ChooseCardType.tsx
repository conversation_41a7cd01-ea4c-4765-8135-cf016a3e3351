import { useEffect } from 'react'
import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { HeaderV2 } from '@zeal/uikit/HeaderV2'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { NavCardIconOutline } from '@zeal/uikit/Icon/NavCardIconOutline'
import { VirtualCard } from '@zeal/uikit/Icon/VirtualCard'
import { IconButton } from '@zeal/uikit/IconButton'
import { SubtextListItem } from '@zeal/uikit/ListItem/SubtextListItem'
import { Screen } from '@zeal/uikit/Screen'
import { Text } from '@zeal/uikit/Text'

import { ReadonlySignerSelectedOnboardedCardConfig } from '@zeal/domains/Card'
import { SupportButton } from '@zeal/domains/Support/features/SupportButton'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

type Props = {
    installationId: string
    cardConfig: ReadonlySignerSelectedOnboardedCardConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'on_virtual_card_order_clicked' }
    | { type: 'on_physical_card_order_clicked' }
    | { type: 'close' }

export const ChooseCardType = ({
    installationId,
    cardConfig,
    onMsg,
}: Props) => {
    useEffect(() => {
        postUserEvent({
            installationId: installationId,
            type: 'CardOrderFlowEnteredEvent',
            state: 'existing',
        })
    }, [installationId])

    return (
        <Screen
            padding="form"
            background="default"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <Column spacing={8} fill>
                <Column spacing={0} fill>
                    <ActionBar
                        left={
                            <IconButton
                                variant="on_light"
                                onClick={() => onMsg({ type: 'close' })}
                            >
                                {({ color }) => (
                                    <BackIcon size={24} color={color} />
                                )}
                            </IconButton>
                        }
                        right={
                            <SupportButton
                                variant={{
                                    type: 'intercom_and_zendesk',
                                    cardConfig,
                                }}
                                layoutVariant="icon_button"
                                installationId={installationId}
                                location="card_settings"
                            />
                        }
                    />
                    <Column spacing={24}>
                        <HeaderV2
                            size="large"
                            align="left"
                            title={
                                <FormattedMessage
                                    id="setup-card.order.title"
                                    defaultMessage="What type of card?"
                                />
                            }
                            subtitle={
                                <FormattedMessage
                                    id="setup-card.order.subtitle1"
                                    defaultMessage="You can use several cards at the same time"
                                />
                            }
                        />
                        <Column spacing={8}>
                            <SubtextListItem
                                variant="outline"
                                size="large"
                                primaryText={
                                    <FormattedMessage
                                        id="setup-card.order.virtualCard"
                                        defaultMessage="Virtual card"
                                    />
                                }
                                avatar={({ size }) => (
                                    <VirtualCard size={size} color="teal40" />
                                )}
                                side={{
                                    rightIcon: () => (
                                        <SubtextListItem.SideButton
                                            variant="regular"
                                            onClick={() => {
                                                onMsg({
                                                    type: 'on_virtual_card_order_clicked',
                                                })
                                            }}
                                        >
                                            <FormattedMessage
                                                id="setup-card.getCard"
                                                defaultMessage="Get card"
                                            />
                                        </SubtextListItem.SideButton>
                                    ),
                                }}
                                onClick={() => {
                                    onMsg({
                                        type: 'on_virtual_card_order_clicked',
                                    })
                                }}
                                subItems={
                                    <Text variant="footnote">
                                        <FormattedMessage
                                            id="setup-card.order.virtual_card.steps"
                                            defaultMessage="· Digital Visa Gnosis Pay card {br}· Use instantly for online payments {br}· Add to Apple/Google Wallet (only for supported countries)"
                                            values={{ br: '\n' }}
                                        />
                                    </Text>
                                }
                            />

                            <SubtextListItem
                                variant="outline"
                                size="large"
                                primaryText={
                                    <FormattedMessage
                                        id="setup-card.order.physicalCard"
                                        defaultMessage="Physical card"
                                    />
                                }
                                avatar={({ size }) => (
                                    <NavCardIconOutline
                                        size={size}
                                        color="teal40"
                                    />
                                )}
                                side={{
                                    rightIcon: () => (
                                        <SubtextListItem.SideButton
                                            variant="regular"
                                            onClick={() => {
                                                postUserEvent({
                                                    type: 'ClickOrderCardButtonEvent',
                                                    installationId:
                                                        installationId,
                                                    location: 'card_settings',
                                                })
                                                onMsg({
                                                    type: 'on_physical_card_order_clicked',
                                                })
                                            }}
                                        >
                                            <FormattedMessage
                                                id="setup-card.orderCard"
                                                defaultMessage="Order card"
                                            />
                                        </SubtextListItem.SideButton>
                                    ),
                                }}
                                onClick={() => {
                                    postUserEvent({
                                        type: 'ClickOrderCardButtonEvent',
                                        installationId: installationId,
                                        location: 'card_settings',
                                    })
                                    onMsg({
                                        type: 'on_physical_card_order_clicked',
                                    })
                                }}
                                subItems={
                                    <Text variant="footnote">
                                        <FormattedMessage
                                            id="setup-card.order.physicalCard.steps"
                                            defaultMessage="· A physical Visa Gnosis Pay card {br}· Takes up to 3 weeks to ship to you {br}· Use for in-person payments and ATMs. {br}· Add to Apple/Google Wallet (only for supported countries)"
                                            values={{ br: '\n' }}
                                        />
                                    </Text>
                                }
                            />
                        </Column>
                    </Column>
                </Column>

                <Actions variant="default" direction="column">
                    <Button
                        variant="secondary"
                        size="regular"
                        onClick={() =>
                            onMsg({
                                type: 'close',
                            })
                        }
                    >
                        <FormattedMessage
                            id="action.cancel"
                            defaultMessage="Cancel"
                        />
                    </Button>
                </Actions>
            </Column>
        </Screen>
    )
}
