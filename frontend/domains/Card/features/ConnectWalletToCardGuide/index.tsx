import { useEffect, useState } from 'react'
import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Avatar } from '@zeal/uikit/Avatar'
import { But<PERSON> } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { Header } from '@zeal/uikit/Header'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { Video } from '@zeal/uikit/Icon/Video'
import { ZealCard } from '@zeal/uikit/Icon/ZealCard'
import { IconButton } from '@zeal/uikit/IconButton'
import { SubtextListItem } from '@zeal/uikit/ListItem/SubtextListItem'
import { Screen } from '@zeal/uikit/Screen'
import { Text } from '@zeal/uikit/Text'

import { withDelay, withRetries } from '@zeal/toolkit/Function'
import { useLazyLoadableData } from '@zeal/toolkit/LoadableData/LazyLoadableData'
import { notReachable } from '@zeal/toolkit/notReachable'
import { useLiveRef } from '@zeal/toolkit/React/useLiveRef'
import { openExternalURL } from '@zeal/toolkit/Window'

import { Account } from '@zeal/domains/Account'
import { CardSlientSignKeyStore } from '@zeal/domains/Card'
import { fetchAddOwnerEvents } from '@zeal/domains/Card/api/fetchAddOwnerEvents'
import {
    CARD_NETWORK,
    GNOSIS_PAY_ADD_CARD_OWNER_HELP,
} from '@zeal/domains/Card/constants'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { NetworkRPCMap } from '@zeal/domains/Network'
import { fetchBlockNumber } from '@zeal/domains/RPCRequest/api/fetchBlockNumber'

import { CopyAddressListItem } from './CopyAddressListItem'

type Props = {
    account: Account
    keyStore: CardSlientSignKeyStore
    installationId: string
    networkRPCMap: NetworkRPCMap
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | {
          type: 'on_add_card_owner_event_detected'
          account: Account
          keyStore: CardSlientSignKeyStore
      }

const MAX_BLOCK_FETCH_RETRIES = 3
const BLOCK_FETCH_RETRY_DELAY_MS = 2000
const OWNER_CHECK_INTERVAL_MS = 6000

const checkIsWalletAddedToGnosisPaySafe = async ({
    account,
    signal,
    networkRPCMap,
    nextBlockToCheck,
}: {
    account: Account
    signal?: AbortSignal
    networkRPCMap: NetworkRPCMap
    nextBlockToCheck: number | 'latest'
}): Promise<{ isAdded: boolean; blockNumberFetched: number }> => {
    const blockToFetch =
        nextBlockToCheck === 'latest'
            ? await fetchBlockNumber({
                  signal,
                  networkRPCMap,
                  network: CARD_NETWORK,
              })
            : nextBlockToCheck

    const addOwnerEvents = await fetchAddOwnerEvents({
        networkRPCMap,
        fromBlock: blockToFetch,
        toBlock: blockToFetch,
        signal,
    })
    const isAdded = addOwnerEvents.some(
        (event) => event.owner === account.address
    )
    return { isAdded, blockNumberFetched: blockToFetch }
}

export const ConnectWalletToCardGuide = ({
    onMsg,
    keyStore,
    account,
    installationId,
    networkRPCMap,
}: Props) => {
    const [nextBlockToCheck, setNextBlockToCheck] = useState<number | 'latest'>(
        'latest'
    )

    const [loadable, setLoadable] = useLazyLoadableData(
        withRetries({
            retries: MAX_BLOCK_FETCH_RETRIES,
            delayMs: BLOCK_FETCH_RETRY_DELAY_MS,
            fn: withDelay(
                checkIsWalletAddedToGnosisPaySafe,
                OWNER_CHECK_INTERVAL_MS
            ),
        })
    )

    useEffect(() => {
        setLoadable({
            type: 'loading',
            params: { account, networkRPCMap, nextBlockToCheck },
        })
    }, [account, networkRPCMap, nextBlockToCheck, setLoadable])

    const onMsgLive = useLiveRef(onMsg)

    useEffect(() => {
        switch (loadable.type) {
            case 'not_asked':
            case 'loading':
                break
            case 'error':
                captureError(loadable.error)
                break
            case 'loaded':
                if (loadable.data.isAdded) {
                    onMsgLive.current({
                        type: 'on_add_card_owner_event_detected',
                        account,
                        keyStore,
                    })
                } else {
                    setNextBlockToCheck(loadable.data.blockNumberFetched + 1)
                }
                break
            /* istanbul ignore next */
            default:
                return notReachable(loadable)
        }
    }, [account, keyStore, loadable, onMsgLive])

    return (
        <Screen
            background="light"
            onNavigateBack={() => onMsg({ type: 'close' })}
            padding="form"
        >
            <ActionBar
                left={
                    <IconButton
                        variant="on_light"
                        onClick={() => onMsg({ type: 'close' })}
                    >
                        {({ color }) => <BackIcon size={24} color={color} />}
                    </IconButton>
                }
            />
            <Column spacing={12} alignY="stretch">
                <Column spacing={24}>
                    <Header
                        title={
                            <FormattedMessage
                                id="card.connectWalletToCardGuide.header"
                                defaultMessage="Connect {account} to Gnosis Pay Card"
                                values={{ account: account.label }}
                            />
                        }
                        icon={({ size }) => (
                            <Avatar variant="rounded" size={size}>
                                <ZealCard size={size} />
                            </Avatar>
                        )}
                    />
                    <Column spacing={8}>
                        <CopyAddressListItem
                            account={account}
                            installationId={installationId}
                        />
                        <SubtextListItem
                            size="large"
                            primaryText={
                                <FormattedMessage
                                    id="card.connectWalletToCardGuide.addGnosisPayOwner"
                                    defaultMessage="Add Gnosis Pay Owner"
                                />
                            }
                            avatar={({ size }) => (
                                <Avatar size={size} border="borderSecondary">
                                    <Text
                                        variant="caption1"
                                        weight="medium"
                                        color="textPrimary"
                                        align="center"
                                    >
                                        {2}
                                    </Text>
                                </Avatar>
                            )}
                            side={{
                                rightIcon: () => (
                                    <Video size={24} color="gray40" />
                                ),
                            }}
                            onClick={() => {
                                openExternalURL(GNOSIS_PAY_ADD_CARD_OWNER_HELP)
                            }}
                            subItems={
                                <Text variant="footnote">
                                    <FormattedMessage
                                        id="card.connectWalletToCardGuide.addGnosisPayOwner.steps"
                                        defaultMessage="1. Open GnosisPay.com with your other wallet{br}2. Click “Account”{br}3. Click “Account details”{br}4. Click “Edit”, next to “Account Owner”, and{br}5. Click “Add address”{br}6. Paste your Zeal address and click save"
                                        values={{ br: '\n' }}
                                    />
                                </Text>
                            }
                        />
                    </Column>
                </Column>
                <Actions variant="default">
                    <Button
                        size="regular"
                        variant="secondary"
                        onClick={() => onMsg({ type: 'close' })}
                    >
                        <FormattedMessage
                            id="action.cancel"
                            defaultMessage="Cancel"
                        />
                    </Button>
                    <Button size="regular" variant="primary" disabled>
                        <FormattedMessage
                            id="action.update-gnosis-pay-owner.complete"
                            defaultMessage="Complete steps"
                        />
                    </Button>
                </Actions>
            </Column>
        </Screen>
    )
}
