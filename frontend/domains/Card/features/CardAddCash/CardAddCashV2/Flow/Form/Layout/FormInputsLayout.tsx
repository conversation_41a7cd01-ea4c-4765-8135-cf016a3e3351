import { FormattedMessage, useIntl } from 'react-intl'

import { Column } from '@zeal/uikit/Column'
import { Divider } from '@zeal/uikit/Divider'
import { FancyButton } from '@zeal/uikit/FancyButton'
import { CreditCard } from '@zeal/uikit/Icon/CreditCard'
import { LightArrowDown2 } from '@zeal/uikit/Icon/LightArrowDown2'
import { IconButton } from '@zeal/uikit/IconButton'
import { AmountInput } from '@zeal/uikit/Input/AmountInput'
import { NextStepSeparator } from '@zeal/uikit/NextStepSeparator'
import { Row } from '@zeal/uikit/Row'
import { Color, Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { fromFixedWithFraction } from '@zeal/toolkit/BigInt'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { values } from '@zeal/toolkit/Object'
import { ZealPlatform } from '@zeal/toolkit/OS/ZealPlatform'
import { getFormattedPercentage } from '@zeal/toolkit/Percentage'

import { AccountsMap } from '@zeal/domains/Account'
import { AvatarWithoutBadge as AccountAvatar } from '@zeal/domains/Account/components/Avatar'
import { CardBalance } from '@zeal/domains/Card'
import { CryptoCurrency } from '@zeal/domains/Currency'
import { Avatar as CurrencyAvatar } from '@zeal/domains/Currency/components/Avatar'
import { MaxButton } from '@zeal/domains/Currency/components/MaxButton'
import { convertStableCoinCurrencyToFiatCurrency } from '@zeal/domains/Currency/helpers/convertStableCoinCurrencyToFiatCurrency'
import { DeployedTaker, Earn } from '@zeal/domains/Earn'
import { TakerAvatar } from '@zeal/domains/Earn/components/TakerAvatar'
import { TakerTitleWithSuffix } from '@zeal/domains/Earn/components/TakerTitleWithSuffix'
import { applyRate2 } from '@zeal/domains/FXRate/helpers/applyRate'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { FiatMoney, Money2, MoneyByCurrency } from '@zeal/domains/Money'
import { FormattedMoneyPrecise } from '@zeal/domains/Money/components/FormattedMoneyPrecise'
import { convertStableCoinToFiat } from '@zeal/domains/Money/helpers/convertStableCoinToFiat'
import { NetworkMap } from '@zeal/domains/Network'
import { Badge } from '@zeal/domains/Network/components/Badge'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { getBalanceByCryptoCurrency2 } from '@zeal/domains/Portfolio/helpers/getBalanceByCryptoCurrency'
import { getTokenByCryptoCurrency3 } from '@zeal/domains/Portfolio/helpers/getTokenByCryptoCurrency'

import { getPercentageLoss } from '../../../helpers/getPercentageLoss'
import { QuotePollable } from '../../../types'
import { ConfirmStageError, EditStageFormError } from '../../../validation'
import { RechargeListItem } from '../../RechargeListItem'

type Props = {
    keyStoreMap: KeyStoreMap
    cardOwnerEarn: Earn
    accountsMap: AccountsMap
    quotePollable: QuotePollable
    cardBalance: CardBalance
    toAmountInDefaultCurrencyError:
        | EditStageFormError['toAmountInDefaultCurrencyError']
        | ConfirmStageError['toAmountInDefaultCurrencyError']
        | null
    maxBalanceError: EditStageFormError['maxBalanceError'] | null
    installationId: string

    onMsg: (msg: Msg) => void
}

type Msg =
    | {
          type: 'on_source_asset_clicked'
      }
    | { type: 'on_sender_account_clicked' }
    | { type: 'on_from_amount_change'; amount: string | null }
    | { type: 'on_from_amount_input_clicked' }
    | { type: 'on_continue_clicked' }
    | MsgOf<typeof RechargeListItem>

const getMaxBalanceState = (
    error: EditStageFormError['maxBalanceError'] | null
): 'normal' | 'error' | 'loading' => {
    if (!error) {
        return 'normal'
    }

    switch (error.type) {
        case 'not_enough_balance':
            return 'error'
        case 'pollable_errored':
        case 'pollable_still_loading':
            return 'loading'
        default:
            return notReachable(error)
    }
}

const getToAmountInDefaultCurrencyColour = (
    toAmountInDefaultCurrencyError:
        | EditStageFormError['toAmountInDefaultCurrencyError']
        | ConfirmStageError['toAmountInDefaultCurrencyError']
        | null
): Color => {
    if (!toAmountInDefaultCurrencyError) {
        return 'gray40'
    }

    switch (toAmountInDefaultCurrencyError.type) {
        case 'pollable_still_loading':
        case 'pollable_errored':
        case 'no_routes':
        case 'from_amount_zero':
            return 'gray40'
        case 'value_loss_error':
            return 'orange30'
        default:
            return notReachable(toAmountInDefaultCurrencyError)
    }
}

const getMaxBalance = (quotePollable: QuotePollable): Money2 => {
    switch (quotePollable.type) {
        case 'loading':
        case 'error':
            const { form, cardConfig, senderPortfolio, senderEarn } =
                quotePollable.params
            switch (form.type) {
                case 'send':
                    return getBalanceByCryptoCurrency2({
                        serverPortfolio: senderPortfolio,
                        currency: cardConfig.currency,
                    })
                case 'swap':
                    return getBalanceByCryptoCurrency2({
                        serverPortfolio: senderPortfolio,
                        currency: form.fromCurrency,
                    })
                case 'earn':
                    const takerPortfolio =
                        senderEarn.takerPortfolioMap[form.taker.type]

                    return applyRate2({
                        baseAmount: takerPortfolio.assetBalance,
                        rate: takerPortfolio.userCurrencyRate,
                    })
                default:
                    return notReachable(form)
            }
        case 'loaded':
        case 'reloading':
        case 'subsequent_failed':
            return quotePollable.data.maxBalance
        default:
            return notReachable(quotePollable)
    }
}

const getFromAmountInDefaultCurrency = (
    quotePollable: QuotePollable
): FiatMoney | null => {
    const { form, cardConfig, senderPortfolio, senderEarn } =
        quotePollable.params

    switch (form.type) {
        case 'send':
            const cardToken = getTokenByCryptoCurrency3({
                serverPortfolio: senderPortfolio,
                currency: cardConfig.currency,
            })

            return cardToken.rate
                ? applyRate2({
                      baseAmount: {
                          amount: fromFixedWithFraction(
                              form.amount,
                              cardConfig.currency.fraction
                          ),
                          currency: cardConfig.currency,
                      },
                      rate: cardToken.rate,
                  })
                : null
        case 'swap':
            const fromToken = getTokenByCryptoCurrency3({
                serverPortfolio: senderPortfolio,
                currency: form.fromCurrency,
            })

            return fromToken.rate
                ? applyRate2({
                      baseAmount: {
                          amount: fromFixedWithFraction(
                              form.amount,
                              form.fromCurrency.fraction
                          ),
                          currency: form.fromCurrency,
                      },
                      rate: fromToken.rate,
                  })
                : null
        case 'earn':
            const takerPortfolio = senderEarn.takerPortfolioMap[form.taker.type]
            const rate = takerPortfolio.userCurrencyToDefaultCurrencyRate
            const userCurrency = takerPortfolio.userCurrencyRate.quote

            return rate
                ? applyRate2({
                      baseAmount: {
                          amount: fromFixedWithFraction(
                              form.amount,
                              userCurrency.fraction
                          ),
                          currency: userCurrency,
                      } as MoneyByCurrency<typeof userCurrency>,
                      rate,
                  })
                : null
        default:
            return notReachable(form)
    }
}

const EarnTakerButton = ({
    taker,
    onClick,
}: {
    taker: DeployedTaker
    onClick: () => void
}) => (
    <IconButton variant="on_light" onClick={onClick}>
        {({ color }) => (
            <Row spacing={4}>
                <TakerAvatar size={24} takerType={taker.type} />
                <Text variant="title3" color="textPrimary" weight="medium">
                    <TakerTitleWithSuffix takerType={taker.type} />
                </Text>
                <LightArrowDown2 size={18} color={color} />
            </Row>
        )}
    </IconButton>
)

const TokenButton = ({
    currency,
    onClick,
    networkMap,
}: {
    currency: CryptoCurrency
    networkMap: NetworkMap
    onClick: () => void
}) => (
    <IconButton variant="on_light" onClick={onClick}>
        {({ color }) => (
            <Row spacing={4}>
                <CurrencyAvatar
                    key={currency.id}
                    currency={currency}
                    rightBadge={({ size }) => (
                        <Badge
                            size={size}
                            network={findNetworkByHexChainId(
                                currency.networkHexChainId,
                                networkMap
                            )}
                        />
                    )}
                    size={24}
                />
                <Text variant="title3" color="textPrimary" weight="medium">
                    {currency.code}
                </Text>
                <LightArrowDown2 size={18} color={color} />
            </Row>
        )}
    </IconButton>
)

export const FormInputsLayout = ({
    keyStoreMap,
    cardOwnerEarn,
    onMsg,
    quotePollable,
    accountsMap,
    cardBalance,
    toAmountInDefaultCurrencyError,
    maxBalanceError,
    installationId,
}: Props) => {
    const { formatMessage } = useIntl()

    const sender = accountsMap[quotePollable.params.senderAddress]
    const { form, cardConfig, networkMap, senderEarn } = quotePollable.params

    const fromAmountInDefaultCurrency =
        getFromAmountInDefaultCurrency(quotePollable) // we have this in pollable data as well, but we don't want to wait for it to load

    return (
        <Column spacing={4} shrink>
            <AmountInput
                top={
                    values(accountsMap).length > 1 && (
                        <Column spacing={0}>
                            <FancyButton
                                color="secondary"
                                rounded
                                left={() => (
                                    <Row grow shrink spacing={4}>
                                        <AccountAvatar
                                            size={12}
                                            account={sender}
                                        />
                                        <Text
                                            variant="caption1"
                                            color="gray40"
                                            weight="medium"
                                            ellipsis
                                        >
                                            {sender.label}
                                        </Text>
                                    </Row>
                                )}
                                right={({ color }) => (
                                    <LightArrowDown2 size={16} color={color} />
                                )}
                                onClick={() => {
                                    onMsg({
                                        type: 'on_sender_account_clicked',
                                    })
                                }}
                            />
                            <Divider variant="secondary" />
                        </Column>
                    )
                }
                content={{
                    topLeft: (() => {
                        switch (form.type) {
                            case 'send':
                                return (
                                    <TokenButton
                                        currency={cardConfig.currency}
                                        networkMap={networkMap}
                                        onClick={() =>
                                            onMsg({
                                                type: 'on_source_asset_clicked',
                                            })
                                        }
                                    />
                                )
                            case 'swap':
                                return (
                                    <TokenButton
                                        currency={form.fromCurrency}
                                        networkMap={networkMap}
                                        onClick={() =>
                                            onMsg({
                                                type: 'on_source_asset_clicked',
                                            })
                                        }
                                    />
                                )
                            case 'earn':
                                return (
                                    <EarnTakerButton
                                        taker={form.taker}
                                        onClick={() =>
                                            onMsg({
                                                type: 'on_source_asset_clicked',
                                            })
                                        }
                                    />
                                )
                            default:
                                return notReachable(form)
                        }
                    })(),
                    topRight: ({ onBlur, onFocus }) => (
                        <AmountInput.Input
                            onBlur={onBlur}
                            onPressIn={() =>
                                onMsg({ type: 'on_from_amount_input_clicked' })
                            }
                            onFocus={onFocus}
                            onChange={(value) =>
                                onMsg({
                                    type: 'on_from_amount_change',
                                    amount: value,
                                })
                            }
                            label={formatMessage({
                                id: 'card.add-cash.amount-to-withdraw',
                                defaultMessage: 'Top-up amount',
                            })}
                            prefix=""
                            fraction={(() => {
                                switch (form.type) {
                                    case 'send':
                                        return cardConfig.currency.fraction
                                    case 'swap':
                                        return form.fromCurrency.fraction
                                    case 'earn':
                                        const userCurrency =
                                            senderEarn.takerPortfolioMap[
                                                form.taker.type
                                            ].userCurrencyRate.quote

                                        return userCurrency.fraction
                                    default:
                                        return notReachable(form)
                                }
                            })()}
                            readOnly={(() => {
                                switch (ZealPlatform.OS) {
                                    case 'web':
                                        return false
                                    case 'android':
                                    case 'ios':
                                        return true
                                    default:
                                        return notReachable(ZealPlatform)
                                }
                            })()}
                            autoFocus
                            amount={form.amount || null}
                            onSubmitEditing={() => {
                                onMsg({ type: 'on_continue_clicked' })
                            }}
                        />
                    ),
                    bottomLeft: (
                        <MaxButton
                            installationId={installationId}
                            location="card_add_cash"
                            state={getMaxBalanceState(maxBalanceError)}
                            balance={getMaxBalance(quotePollable)}
                            onMsg={(msg) => {
                                switch (msg.type) {
                                    case 'on_amount_change':
                                        onMsg({
                                            type: 'on_from_amount_change',
                                            amount: msg.amount,
                                        })
                                        break
                                    default:
                                        return notReachable(msg.type)
                                }
                            }}
                        />
                    ),
                    bottomRight: fromAmountInDefaultCurrency && (
                        <Text
                            variant="paragraph"
                            color="gray40"
                            weight="medium"
                        >
                            <FormattedMoneyPrecise
                                withSymbol
                                sign={null}
                                money={fromAmountInDefaultCurrency}
                            />
                        </Text>
                    ),
                }}
                state="normal"
            />

            <NextStepSeparator />

            <AmountInput
                content={{
                    topLeft: (
                        <Row spacing={8}>
                            <CreditCard size={32} color="gray20" />
                            <Text
                                variant="title3"
                                color="textPrimary"
                                weight="medium"
                            >
                                <FormattedMessage
                                    id="card"
                                    defaultMessage="Card"
                                />
                            </Text>
                        </Row>
                    ),
                    bottomLeft: (
                        <Text
                            variant="paragraph"
                            color="gray40"
                            weight="medium"
                        >
                            <FormattedMessage
                                id="card-balance"
                                defaultMessage="Balance: {balance}"
                                values={{
                                    balance: (
                                        <FormattedMoneyPrecise
                                            withSymbol
                                            sign={null}
                                            money={cardBalance.total}
                                        />
                                    ),
                                }}
                            />
                        </Text>
                    ),
                    topRight: () => {
                        switch (quotePollable.type) {
                            case 'loading':
                            case 'error':
                            case 'reloading':
                            case 'subsequent_failed':
                                return <AmountInput.InputSkeleton />
                            case 'loaded':
                                const quote =
                                    quotePollable.data.quote.getSuccessResult()

                                return (
                                    <Row spacing={0} alignX="end">
                                        <Text
                                            variant="title3"
                                            weight="medium"
                                            color="textPrimary"
                                        >
                                            <FormattedMoneyPrecise
                                                withSymbol
                                                sign={null}
                                                money={
                                                    quote
                                                        ? convertStableCoinToFiat(
                                                              {
                                                                  money: quote.toAmount,
                                                              }
                                                          )
                                                        : {
                                                              amount: 0n,
                                                              currency:
                                                                  convertStableCoinCurrencyToFiatCurrency(
                                                                      {
                                                                          cryptoCurrency:
                                                                              cardConfig.currency,
                                                                      }
                                                                  ),
                                                          }
                                                }
                                            />
                                        </Text>
                                    </Row>
                                )
                            default:
                                return notReachable(quotePollable)
                        }
                    },
                    bottomRight: (() => {
                        switch (quotePollable.type) {
                            case 'loading':
                            case 'error':
                            case 'reloading':
                            case 'subsequent_failed':
                                return <AmountInput.InputSkeleton />
                            case 'loaded':
                                const quote =
                                    quotePollable.data.quote.getSuccessResult()

                                if (!quote?.toAmountInDefaultCurrency) {
                                    return null
                                }

                                if (fromAmountInDefaultCurrency) {
                                    const percentageLoss = getPercentageLoss({
                                        fromAmount: fromAmountInDefaultCurrency,
                                        toAmount:
                                            quote.toAmountInDefaultCurrency,
                                    })

                                    if (percentageLoss > 0) {
                                        return (
                                            <Row spacing={0} alignX="end">
                                                <Text
                                                    variant="paragraph"
                                                    color={getToAmountInDefaultCurrencyColour(
                                                        toAmountInDefaultCurrencyError
                                                    )}
                                                    weight="medium"
                                                >
                                                    <FormattedMessage
                                                        id="card.addCash.to-amount-percentage-loss"
                                                        defaultMessage="(-{percentageLoss}) {toAmount}"
                                                        values={{
                                                            percentageLoss:
                                                                getFormattedPercentage(
                                                                    percentageLoss
                                                                ),
                                                            toAmount: (
                                                                <FormattedMoneyPrecise
                                                                    withSymbol
                                                                    sign={null}
                                                                    money={
                                                                        quote.toAmountInDefaultCurrency
                                                                    }
                                                                />
                                                            ),
                                                        }}
                                                    />
                                                </Text>
                                            </Row>
                                        )
                                    }
                                }
                                return (
                                    <Text
                                        variant="paragraph"
                                        color="gray40"
                                        weight="medium"
                                    >
                                        <FormattedMoneyPrecise
                                            withSymbol
                                            sign={null}
                                            money={
                                                quote.toAmountInDefaultCurrency
                                            }
                                        />
                                    </Text>
                                )
                            default:
                                return notReachable(quotePollable)
                        }
                    })(),
                }}
                state="normal"
            />

            <RechargeListItem
                cardOwnerEarn={cardOwnerEarn}
                keyStoreMap={keyStoreMap}
                cardConfig={cardConfig}
                disabled={false}
                onMsg={onMsg}
            />
        </Column>
    )
}
