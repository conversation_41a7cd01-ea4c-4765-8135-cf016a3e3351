import { FormattedMessage, useIntl } from 'react-intl'

import { Actions } from '@zeal/uikit/Actions'
import { Avatar as UIAvatar } from '@zeal/uikit/Avatar'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { Divider } from '@zeal/uikit/Divider'
import { FancyButton } from '@zeal/uikit/FancyButton'
import { BoldThickRechargeLightning } from '@zeal/uikit/Icon/BoldThickRechargeLightning'
import { CreditCard } from '@zeal/uikit/Icon/CreditCard'
import { LightArrowDown2 } from '@zeal/uikit/Icon/LightArrowDown2'
import { AmountInput } from '@zeal/uikit/Input/AmountInput'
import { KeyPadFloatIntut } from '@zeal/uikit/Input/FloatInput'
import { ListItemButton } from '@zeal/uikit/ListItem'
import { NextStepSeparator } from '@zeal/uikit/NextStepSeparator'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { Skeleton as UISkeleton } from '@zeal/uikit/Skeleton'
import { Text } from '@zeal/uikit/Text'

import { noop, notReachable } from '@zeal/toolkit'
import { values } from '@zeal/toolkit/Object'
import { ZealPlatform } from '@zeal/toolkit/OS/ZealPlatform'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { AvatarWithoutBadge as AccountAvatar } from '@zeal/domains/Account/components/Avatar'
import { CryptoCurrency } from '@zeal/domains/Currency'
import { Avatar as CurrencyAvatar } from '@zeal/domains/Currency/components/Avatar'
import { NetworkMap } from '@zeal/domains/Network'
import { Badge } from '@zeal/domains/Network/components/Badge'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'

type Props = {
    sender: Account
    accountsMap: AccountsMap
    networkMap: NetworkMap
    amount: string | null
    initialTopUpCurrency: CryptoCurrency | null
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | { type: 'on_sender_account_clicked' }
    | { type: 'on_amount_changed'; amount: string | null }

export const LoadingLayout = ({
    onMsg,
    sender,
    networkMap,
    amount,
    accountsMap,
    initialTopUpCurrency,
}: Props) => {
    const { formatMessage } = useIntl()

    return (
        <Screen
            background="light"
            padding="form"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <Column spacing={12} shrink alignY="stretch">
                <Column spacing={4} shrink>
                    <AmountInput
                        top={
                            values(accountsMap).length > 1 && (
                                <Column spacing={0}>
                                    <FancyButton
                                        color="secondary"
                                        rounded
                                        left={() => (
                                            <Row grow shrink spacing={4}>
                                                <AccountAvatar
                                                    size={12}
                                                    account={sender}
                                                />
                                                <Text
                                                    variant="caption1"
                                                    color="gray40"
                                                    weight="medium"
                                                    ellipsis
                                                >
                                                    {sender.label}
                                                </Text>
                                            </Row>
                                        )}
                                        right={({ color }) => (
                                            <LightArrowDown2
                                                size={16}
                                                color={color}
                                            />
                                        )}
                                        onClick={() => {
                                            onMsg({
                                                type: 'on_sender_account_clicked',
                                            })
                                        }}
                                    />
                                    <Divider variant="secondary" />
                                </Column>
                            )
                        }
                        content={{
                            topLeft: initialTopUpCurrency ? (
                                <Row spacing={4}>
                                    <CurrencyAvatar
                                        key={initialTopUpCurrency.id}
                                        currency={initialTopUpCurrency}
                                        rightBadge={({ size }) => (
                                            <Badge
                                                size={size}
                                                network={findNetworkByHexChainId(
                                                    initialTopUpCurrency.networkHexChainId,
                                                    networkMap
                                                )}
                                            />
                                        )}
                                        size={24}
                                    />
                                    <Text
                                        variant="title3"
                                        color="textPrimary"
                                        weight="medium"
                                    >
                                        {initialTopUpCurrency.code}
                                    </Text>
                                </Row>
                            ) : (
                                <Row spacing={4}>
                                    <UISkeleton
                                        variant="default"
                                        width={24}
                                        height={24}
                                    />
                                    <UISkeleton
                                        variant="default"
                                        width={75}
                                        height={24}
                                    />
                                </Row>
                            ),
                            topRight: ({ onBlur, onFocus }) => (
                                <AmountInput.Input
                                    onBlur={onBlur}
                                    onFocus={onFocus}
                                    onSubmitEditing={noop}
                                    onChange={(value) =>
                                        onMsg({
                                            type: 'on_amount_changed',
                                            amount: value,
                                        })
                                    }
                                    label={formatMessage({
                                        id: 'card.add-cash.amount-to-withdraw',
                                        defaultMessage: 'Top-up amount',
                                    })}
                                    prefix=""
                                    fraction={
                                        initialTopUpCurrency?.fraction || 18
                                    }
                                    readOnly={(() => {
                                        switch (ZealPlatform.OS) {
                                            case 'web':
                                                return false
                                            case 'android':
                                            case 'ios':
                                                return true
                                            default:
                                                return notReachable(
                                                    ZealPlatform
                                                )
                                        }
                                    })()}
                                    autoFocus
                                    amount={amount}
                                />
                            ),
                            bottomLeft: (
                                <Row spacing={4}>
                                    <Text
                                        color="gray40"
                                        variant="paragraph"
                                        weight="regular"
                                        textDecorationLine="underline"
                                    >
                                        <FormattedMessage
                                            id="currency.max_loading"
                                            defaultMessage="Max:"
                                        />
                                    </Text>
                                    <UISkeleton
                                        variant="default"
                                        width={60}
                                        height={18}
                                    />
                                </Row>
                            ),
                            bottomRight: (
                                <UISkeleton
                                    variant="default"
                                    width={60}
                                    height={18}
                                />
                            ),
                        }}
                        state="normal"
                    />

                    <NextStepSeparator />

                    <AmountInput
                        content={{
                            topLeft: (
                                <Row spacing={8}>
                                    <CreditCard size={32} color="gray20" />
                                    <Text
                                        variant="title3"
                                        color="textPrimary"
                                        weight="medium"
                                    >
                                        <FormattedMessage
                                            id="card"
                                            defaultMessage="Card"
                                        />
                                    </Text>
                                </Row>
                            ),
                            bottomLeft: (
                                <UISkeleton
                                    variant="default"
                                    width={60}
                                    height={18}
                                />
                            ),
                            topRight: () => <AmountInput.InputSkeleton />,
                            bottomRight: (
                                <UISkeleton
                                    variant="default"
                                    width={60}
                                    height={18}
                                />
                            ),
                        }}
                        state="normal"
                    />

                    <ListItemButton
                        aria-current={false}
                        variant="default"
                        background="surface"
                        disabled={true}
                        avatar={({ size }) => (
                            <UIAvatar
                                size={size}
                                variant="round"
                                backgroundColor="gray100"
                            >
                                <BoldThickRechargeLightning
                                    size={size}
                                    color="gray20"
                                />
                            </UIAvatar>
                        )}
                        primaryText={
                            <UISkeleton
                                variant="default"
                                width={150}
                                height={20}
                            />
                        }
                        side={{
                            rightIcon: () => (
                                <UISkeleton
                                    variant="default"
                                    width={40}
                                    height={20}
                                />
                            ),
                        }}
                    />
                </Column>

                <Column spacing={12} alignY="end" fill>
                    {(() => {
                        switch (ZealPlatform.OS) {
                            case 'web':
                                return null
                            case 'android':
                            case 'ios':
                                return (
                                    <KeyPadFloatIntut
                                        disabled={false}
                                        fraction={18}
                                        value={amount}
                                        onChange={(amount) =>
                                            onMsg({
                                                type: 'on_amount_changed',
                                                amount,
                                            })
                                        }
                                    />
                                )
                            /* istanbul ignore next */
                            default:
                                return notReachable(ZealPlatform)
                        }
                    })()}
                    <Actions variant="default" direction="row">
                        <Button
                            size="regular"
                            variant="secondary"
                            onClick={() => {
                                onMsg({ type: 'close' })
                            }}
                        >
                            <FormattedMessage
                                id="card-add-cash.edit-stage.cta.cancel"
                                defaultMessage="Cancel"
                            />
                        </Button>
                        <Button size="regular" variant="primary" disabled>
                            <FormattedMessage
                                id="card-add-cash.edit-stage.cta.continue"
                                defaultMessage="Continue"
                            />
                        </Button>
                    </Actions>
                </Column>
            </Column>
        </Screen>
    )
}
