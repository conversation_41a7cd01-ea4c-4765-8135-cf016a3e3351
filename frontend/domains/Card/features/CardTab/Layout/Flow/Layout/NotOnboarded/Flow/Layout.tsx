import { FormattedMessage } from 'react-intl'

import { ActionBar as UIActionBar } from '@zeal/uikit/ActionBar'
import { Actions as UIActions } from '@zeal/uikit/Actions'
import { Button } from '@zeal/uikit/Button'
import { CardWidget } from '@zeal/uikit/CardWidget'
import { Column } from '@zeal/uikit/Column'
import { LightArrowRight2 } from '@zeal/uikit/Icon/LightArrowRight2'
import { Screen } from '@zeal/uikit/Screen'
import { Spacer } from '@zeal/uikit/Spacer'
import { Tertiary } from '@zeal/uikit/Tertiary'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { ActionBarAccountIndicator } from '@zeal/domains/Account/components/ActionBarAccountIndicator'
import {
    CardSlientSignKeyStore,
    GnosisPayAccountNotOnboardedState,
    GnosisPayCardCreatedFromOrderState,
    GnosisPayCardOrderPendingPaymentState,
    GnosisPayCardOrderReadyState,
    GnosisPayPostKYCApprovedState,
    GnosisPayPreKYCApprovedState,
    ReadonlySignerSelectedCardConfig,
} from '@zeal/domains/Card'
import { OnboardingWidget } from '@zeal/domains/Card/components/OnboardingWidget'
import { ActivateMoneriumBankTransfersBanner } from '@zeal/domains/Card/domains/MoneriumBankTransfer/features/ActivateMoneriumBankTransfersBanner'
import { CurrencyHiddenMap, GasCurrencyPresetMap } from '@zeal/domains/Currency'
import { EarnTakerMetrics } from '@zeal/domains/Earn'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { KYCApprovedLayout } from './KYCApprovedLayout'

type Props = {
    gnosisPayAccountState: GnosisPayAccountNotOnboardedState

    cardConfig: ReadonlySignerSelectedCardConfig
    cardReadOnlySigner: Account
    keyStore: CardSlientSignKeyStore

    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    accountsMap: AccountsMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    installationId: string
    currencyHiddenMap: CurrencyHiddenMap
    keyStoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    sessionPassword: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    installationCampaign: string | null
    earnTakerMetrics: EarnTakerMetrics
    onMsg: (msg: Msg) => void
}

type Msg =
    | MsgOf<typeof KYCApprovedLayout>
    | { type: 'on_card_disconnected'; cardReadonlySigner: Account }
    | {
          type: 'on_activate_card_clicked'
          gnosisPayAccountState: GnosisPayCardCreatedFromOrderState
      }
    | {
          type: 'on_fully_configured_safe_activate_card_clicked'
          gnosisPayAccountState: GnosisPayCardCreatedFromOrderState
      }
    | {
          type: 'on_continue_kyc_clicked'
          gnosisPayState: GnosisPayPreKYCApprovedState
      }
    | {
          type: 'on_status_widget_clicked'
          state: GnosisPayPreKYCApprovedState | GnosisPayPostKYCApprovedState
      }
    | {
          type: 'on_virtual_order_card_clicked'
          gnosisPayAccountState: GnosisPayCardCreatedFromOrderState
      }
    | {
          type: 'on_continue_physical_card_order_clicked'
          gnosisPayAccountState:
              | GnosisPayCardOrderPendingPaymentState
              | GnosisPayCardOrderReadyState
      }
    | MsgOf<typeof ActivateMoneriumBankTransfersBanner>

export const Layout = ({
    cardReadOnlySigner,
    gnosisPayAccountState,
    defaultCurrencyConfig,
    cardConfig,
    currencyHiddenMap,
    networkMap,
    networkRPCMap,
    portfolioMap,
    installationCampaign,
    sessionPassword,
    feePresetMap,
    keyStore,
    keyStoreMap,
    accountsMap,
    gasCurrencyPresetMap,
    installationId,
    earnTakerMetrics,
    onMsg,
}: Props) => {
    switch (gnosisPayAccountState.state) {
        case 'terms_not_accepted':
        case 'terms_accepted_kyc_not_started':
        case 'kyc_started_documents_requested':
        case 'kyc_started_verification_in_progress':
        case 'kyc_started_resubmission_requested':
        case 'kyc_failed':
        case 'card_order_pending_payment':
        case 'card_order_ready':
        case 'card_created_from_order':
            return (
                <Content
                    installationCampaign={installationCampaign}
                    currencyHiddenMap={currencyHiddenMap}
                    gnosisPayAccountState={gnosisPayAccountState}
                    cardConfig={cardConfig}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    portfolioMap={portfolioMap}
                    keyStoreMap={keyStoreMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    accountsMap={accountsMap}
                    feePresetMap={feePresetMap}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    installationId={installationId}
                    keyStore={keyStore}
                    cardReadOnlySigner={cardReadOnlySigner}
                    sessionPassword={sessionPassword}
                    earnTakerMetrics={earnTakerMetrics}
                    onMsg={onMsg}
                />
            )
        case 'kyc_approved': {
            return (
                <KYCApprovedLayout
                    installationId={installationId}
                    onMsg={onMsg}
                    gnosisPayAccountState={gnosisPayAccountState}
                />
            )
        }

        default:
            return notReachable(gnosisPayAccountState)
    }
}

const Content = ({
    cardReadOnlySigner,
    gnosisPayAccountState,
    defaultCurrencyConfig,
    cardConfig,
    networkMap,
    networkRPCMap,
    currencyHiddenMap,
    portfolioMap,
    sessionPassword,
    feePresetMap,
    keyStore,
    keyStoreMap,
    accountsMap,
    installationCampaign,
    gasCurrencyPresetMap,
    installationId,
    earnTakerMetrics,
    onMsg,
}: Omit<Props, 'gnosisPayAccountState'> & {
    gnosisPayAccountState:
        | GnosisPayPreKYCApprovedState
        | GnosisPayPostKYCApprovedState
}) => {
    return (
        <Screen padding="form" background="light" onNavigateBack={null}>
            <UIActionBar
                left={
                    <ActionBarAccountIndicator account={cardReadOnlySigner} />
                }
            />
            <Column spacing={8} fill alignY="stretch">
                <CardWidget
                    side="front"
                    cardType="physical"
                    variant="active"
                    settingsIcon={null}
                    rechargeTag={null}
                    balancePending={null}
                    balanceSpendable={null}
                    shortCardNumber={null}
                />
                <Column spacing={8} alignX="center">
                    {(() => {
                        switch (gnosisPayAccountState.state) {
                            case 'terms_not_accepted':
                            case 'terms_accepted_kyc_not_started':
                            case 'kyc_started_documents_requested':
                            case 'kyc_started_verification_in_progress':
                            case 'kyc_started_resubmission_requested':
                            case 'kyc_failed':
                                return null
                            case 'card_order_pending_payment':
                            case 'card_order_ready':
                            case 'card_created_from_order':
                                return (
                                    <ActivateMoneriumBankTransfersBanner
                                        installationCampaign={
                                            installationCampaign
                                        }
                                        currencyHiddenMap={currencyHiddenMap}
                                        cardConfig={cardConfig}
                                        defaultCurrencyConfig={
                                            defaultCurrencyConfig
                                        }
                                        cardReadOnlySigner={cardReadOnlySigner}
                                        keyStore={keyStore}
                                        networkMap={networkMap}
                                        networkRPCMap={networkRPCMap}
                                        accountsMap={accountsMap}
                                        feePresetMap={feePresetMap}
                                        gasCurrencyPresetMap={
                                            gasCurrencyPresetMap
                                        }
                                        installationId={installationId}
                                        keyStoreMap={keyStoreMap}
                                        portfolioMap={portfolioMap}
                                        sessionPassword={sessionPassword}
                                        onMsg={onMsg}
                                    />
                                )
                            default:
                                return notReachable(gnosisPayAccountState)
                        }
                    })()}
                    <OnboardingWidget
                        cardConfig={cardConfig}
                        state={gnosisPayAccountState.state}
                        onClick={() =>
                            onMsg({
                                type: 'on_status_widget_clicked',
                                state: gnosisPayAccountState,
                            })
                        }
                    />
                    <Tertiary
                        size="regular"
                        color="on_color"
                        onClick={() => {
                            onMsg({
                                type: 'on_card_disconnected',
                                cardReadonlySigner: cardReadOnlySigner,
                            })
                        }}
                    >
                        {({ color, textVariant, textWeight }) => (
                            <>
                                <Text
                                    variant={textVariant}
                                    weight={textWeight}
                                    color={color}
                                >
                                    <FormattedMessage
                                        id="card.disconnect-account.title"
                                        defaultMessage="Disconnect account"
                                    />
                                </Text>
                                <LightArrowRight2 size={14} color={color} />
                            </>
                        )}
                    </Tertiary>
                </Column>
                <Spacer />
                {(() => {
                    switch (gnosisPayAccountState.state) {
                        case 'kyc_failed':
                            return null
                        case 'card_order_pending_payment':
                        case 'card_order_ready':
                            return (
                                <UIActions variant="default">
                                    <Button
                                        variant="primary"
                                        size="regular"
                                        onClick={() => {
                                            onMsg({
                                                type: 'on_continue_physical_card_order_clicked',
                                                gnosisPayAccountState,
                                            })
                                        }}
                                    >
                                        <FormattedMessage
                                            id="card.order.continue"
                                            defaultMessage="Continue card order"
                                        />
                                    </Button>
                                </UIActions>
                            )

                        case 'terms_not_accepted':
                        case 'terms_accepted_kyc_not_started':
                        case 'kyc_started_documents_requested':
                        case 'kyc_started_verification_in_progress':
                            return (
                                <UIActions variant="default">
                                    <Button
                                        variant="primary"
                                        size="regular"
                                        onClick={() => {
                                            onMsg({
                                                type: 'on_continue_kyc_clicked',
                                                gnosisPayState:
                                                    gnosisPayAccountState,
                                            })
                                        }}
                                    >
                                        <FormattedMessage
                                            id="card.kyc.continue"
                                            defaultMessage="Continue setup"
                                        />
                                    </Button>
                                </UIActions>
                            )

                        case 'card_created_from_order':
                            return (
                                <UIActions variant="default" direction="column">
                                    <Button
                                        variant="secondary"
                                        size="regular"
                                        onClick={() => {
                                            onMsg({
                                                type: 'on_virtual_order_card_clicked',
                                                gnosisPayAccountState,
                                            })
                                        }}
                                    >
                                        <FormattedMessage
                                            id="card.order.free_virtual_card"
                                            defaultMessage="Get free virtual card now"
                                        />
                                    </Button>

                                    <Button
                                        variant="primary"
                                        size="regular"
                                        onClick={() => {
                                            postUserEvent({
                                                type: 'ClickActivateCardButtonEvent',
                                                installationId: installationId,
                                            })
                                            onMsg({
                                                type: 'on_activate_card_clicked',
                                                gnosisPayAccountState:
                                                    gnosisPayAccountState,
                                            })
                                        }}
                                    >
                                        <FormattedMessage
                                            id="card.activation.activate_physical_card"
                                            defaultMessage="Activate physical card"
                                        />
                                    </Button>
                                </UIActions>
                            )
                        case 'kyc_started_resubmission_requested':
                            return (
                                <UIActions variant="default">
                                    <Button
                                        variant="primary"
                                        size="regular"
                                        onClick={() => {
                                            onMsg({
                                                type: 'on_continue_kyc_clicked',
                                                gnosisPayState:
                                                    gnosisPayAccountState,
                                            })
                                        }}
                                    >
                                        <FormattedMessage
                                            id="action.retry"
                                            defaultMessage="Retry"
                                        />
                                    </Button>
                                </UIActions>
                            )

                        default:
                            return notReachable(gnosisPayAccountState)
                    }
                })()}
            </Column>
        </Screen>
    )
}
