import { useEffect } from 'react'
import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { HeaderV2 } from '@zeal/uikit/HeaderV2'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { ExternalLink } from '@zeal/uikit/Icon/ExternalLink'
import { GnosisPayIcon } from '@zeal/uikit/Icon/GnosisPayIcon'
import { IconButton } from '@zeal/uikit/IconButton'
import { Screen } from '@zeal/uikit/Screen'
import { Spacer } from '@zeal/uikit/Spacer'

import { useLiveRef } from '@zeal/toolkit/React'

import {
    ReadonlySignerSelectedCardConfig,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { GnosisPayThereIsAlreadyPendingCardOrder } from '@zeal/domains/Error/domains/GnosisPay'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { SupportButton } from '@zeal/domains/Support/features/SupportButton'

type Msg =
    | {
          type: 'close'
      }
    | {
          type: 'on_card_order_redirect_to_gnosis_pay_clicked'
      }

type Props = {
    error: GnosisPayThereIsAlreadyPendingCardOrder
    installationId: string
    cardConfig:
        | ReadonlySignerSelectedCardConfig
        | ReadonlySignerSelectedOnboardedCardConfig
    onMsg: (msg: Msg) => void
}

export const AlreadyHasPendingCardOrderErrorScreen = ({
    error,
    installationId,
    cardConfig,
    onMsg,
}: Props) => {
    const liveError = useLiveRef(error)

    useEffect(() => {
        captureError(liveError.current)
    }, [liveError])

    return (
        <Screen
            padding="form"
            background="default"
            onNavigateBack={() =>
                onMsg({
                    type: 'close',
                })
            }
        >
            <Column spacing={12} fill>
                <ActionBar
                    right={
                        <SupportButton
                            variant={{
                                type: 'intercom_and_zendesk',
                                cardConfig,
                            }}
                            layoutVariant="icon_button"
                            installationId={installationId}
                            location="gnosis_card_order"
                        />
                    }
                    left={
                        <IconButton
                            variant="on_light"
                            onClick={() =>
                                onMsg({
                                    type: 'close',
                                })
                            }
                        >
                            {({ color }) => (
                                <BackIcon size={24} color={color} />
                            )}
                        </IconButton>
                    }
                />
                <HeaderV2
                    title={
                        <FormattedMessage
                            id="createCardOrder.redirectToGnosisPay.continue-in-gonosis-pay.title"
                            defaultMessage="Continue in Gnosis Pay"
                        />
                    }
                    subtitle={
                        <FormattedMessage
                            id="createCardOrder.redirectToGnosisPay.you-alredy-started-card-order.subtitle"
                            defaultMessage="You’ve already started your card order. Head back to the Gnosis Pay site to complete it."
                        />
                    }
                    icon={({ size }) => (
                        <GnosisPayIcon size={size} type="round" />
                    )}
                    align="left"
                    size="large"
                />

                <Spacer />

                <Actions variant="default">
                    <Button
                        variant="primary"
                        size="regular"
                        onClick={() =>
                            onMsg({
                                type: 'on_card_order_redirect_to_gnosis_pay_clicked',
                            })
                        }
                        rightIcon={({ size }) => (
                            <ExternalLink size={size} color="gray100" />
                        )}
                    >
                        <FormattedMessage
                            id="createCardOrder.redirectToGnosisPay.go_to_gnosis_pay"
                            defaultMessage="Go to GnosisPay.com"
                        />
                    </Button>
                </Actions>
            </Column>
        </Screen>
    )
}
