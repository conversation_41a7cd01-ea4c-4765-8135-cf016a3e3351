import { FormattedMessage, useIntl } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { HeaderV2 } from '@zeal/uikit/HeaderV2'
import { ArrowDown } from '@zeal/uikit/Icon/ArrowDown'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { IconButton } from '@zeal/uikit/IconButton'
import { Input } from '@zeal/uikit/Input'
import { InputButton } from '@zeal/uikit/InputButton'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { ScrollContainer } from '@zeal/uikit/ScrollContainer'

import { notReachable } from '@zeal/toolkit'

import {
    ReadonlySignerSelectedCardConfig,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { Avatar as CountryIcon } from '@zeal/domains/Country/components/Avatar'
import { SupportButton } from '@zeal/domains/Support/features/SupportButton'

import { ErrorPhoneNotValid, Errors, Form } from '../validation'

type Props = {
    installationId: string
    isLoading: boolean
    form: Form
    errors: Errors
    onMsg: (msg: Msg) => void
    cardConfig:
        | ReadonlySignerSelectedCardConfig
        | ReadonlySignerSelectedOnboardedCardConfig
}

type Msg =
    | { type: 'close' }
    | { type: 'on_form_submit_clicked' }
    | { type: 'on_country_selector_clicked' }
    | { type: 'on_phone_number_changed'; phone: string | null }

export const Layout = ({
    form,
    installationId,
    cardConfig,
    isLoading,
    errors,
    onMsg,
}: Props) => {
    const { formatMessage } = useIntl()

    return (
        <Screen
            padding="form"
            background="default"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <ScrollContainer withFloatingActions={false}>
                <Column spacing={12} fill>
                    <ActionBar
                        right={
                            <SupportButton
                                variant={{
                                    type: 'intercom_and_zendesk',
                                    cardConfig,
                                }}
                                layoutVariant="icon_button"
                                installationId={installationId}
                                location="gnosis_card_order"
                            />
                        }
                        left={
                            <IconButton
                                variant="on_light"
                                onClick={() => onMsg({ type: 'close' })}
                            >
                                {({ color }) => (
                                    <BackIcon size={24} color={color} />
                                )}
                            </IconButton>
                        }
                    />

                    <Column spacing={24}>
                        <HeaderV2
                            size="large"
                            align="left"
                            title={
                                <FormattedMessage
                                    id="virtualCard.activateCard"
                                    defaultMessage="Activate Card"
                                />
                            }
                            subtitle={null}
                        />
                        <Row spacing={4} alignY="start">
                            <InputButton
                                leftIcon={
                                    <CountryIcon
                                        countryCode={form.country.code}
                                        size={28}
                                    />
                                }
                                rightIcon={
                                    <ArrowDown color="iconDisabled" size={24} />
                                }
                                onClick={() => {
                                    onMsg({
                                        type: 'on_country_selector_clicked',
                                    })
                                }}
                            >
                                {form.country.dialingCode}
                            </InputButton>
                            <Row
                                spacing={0}
                                grow
                                shrink
                                ignoreContentWidth
                                fullWidth
                            >
                                <Input
                                    placeholder={formatMessage({
                                        id: 'phoneNumber.title',
                                        defaultMessage: 'Phone number',
                                    })}
                                    type="text"
                                    keyboardType="phone-pad"
                                    variant="large"
                                    autoFocus
                                    value={form.phone || ''}
                                    onSubmitEditing={() =>
                                        onMsg({
                                            type: 'on_form_submit_clicked',
                                        })
                                    }
                                    onChange={(e) => {
                                        onMsg({
                                            type: 'on_phone_number_changed',
                                            phone: e.nativeEvent.text,
                                        })
                                    }}
                                    state={!!errors.phone ? 'error' : 'normal'}
                                    message={
                                        errors.phone ? (
                                            <PhoneErrorMessage
                                                error={errors.phone}
                                            />
                                        ) : null
                                    }
                                    autoComplete="tel"
                                    autoCapitalize="none"
                                />
                            </Row>
                        </Row>
                    </Column>
                </Column>
            </ScrollContainer>

            <Actions variant="default">
                <Button
                    variant="primary"
                    size="regular"
                    disabled={!!errors.cta || isLoading}
                    loading={isLoading}
                    onClick={() => onMsg({ type: 'on_form_submit_clicked' })}
                >
                    {(() => {
                        if (!errors.cta) {
                            return (
                                <FormattedMessage
                                    id="action.requestCode"
                                    defaultMessage="Request code"
                                />
                            )
                        }
                        switch (errors.cta.type) {
                            case 'phoneRequired':
                            case 'phoneNotValid':
                                return (
                                    <FormattedMessage
                                        id="action.enterPhoneNumber"
                                        defaultMessage="Enter phone number"
                                    />
                                )

                            default:
                                return notReachable(errors.cta)
                        }
                    })()}
                </Button>
            </Actions>
        </Screen>
    )
}

const PhoneErrorMessage = ({ error }: { error: ErrorPhoneNotValid }) => {
    switch (error.type) {
        case 'phoneNotValid':
            return (
                <FormattedMessage
                    id="notValidPhone.title"
                    defaultMessage="This is not a valid phone number"
                />
            )

        default:
            return notReachable(error.type)
    }
}
