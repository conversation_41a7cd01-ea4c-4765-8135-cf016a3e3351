import { FormattedMessage } from 'react-intl'

import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { Header } from '@zeal/uikit/Header'
import { GaugeMeterSpeedLimit } from '@zeal/uikit/Icon/GaugeMeterSpeedLimit'
import { Popup } from '@zeal/uikit/Popup'

type Props = {
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'close' } | { type: 'on_continue_clicked' }

export const Confirmation = ({ onMsg }: Props) => {
    return (
        <Popup.Layout onMsg={onMsg} background="surfaceDefault">
            <Column spacing={24}>
                <Header
                    icon={({ size }) => (
                        <GaugeMeterSpeedLimit size={size} color="iconAccent2" />
                    )}
                    title={
                        <FormattedMessage
                            id="add-owner.confirmation.title"
                            defaultMessage="Your card will be frozen for 3 min while settings update"
                        />
                    }
                    subtitle={
                        <FormattedMessage
                            id="add-owner.confirmation.subtitle"
                            defaultMessage="For security, settings changes take 3 minutes to process, during which your card will be temporarily frozen, and payments won’t be possible."
                        />
                    }
                />
                <Popup.Actions>
                    <Button
                        variant="secondary"
                        onClick={() =>
                            onMsg({
                                type: 'close',
                            })
                        }
                        size="regular"
                    >
                        <FormattedMessage
                            id="action.cancel"
                            defaultMessage="Cancel"
                        />
                    </Button>
                    <Button
                        variant="primary"
                        onClick={() =>
                            onMsg({
                                type: 'on_continue_clicked',
                            })
                        }
                        size="regular"
                    >
                        <FormattedMessage
                            id="action.continue"
                            defaultMessage="Continue"
                        />
                    </Button>
                </Popup.Actions>
            </Column>
        </Popup.Layout>
    )
}
