import { FormattedMessage } from 'react-intl'

import { Head<PERSON> } from '@zeal/uikit/Header'
import { Popup } from '@zeal/uikit/Popup'

import { notReachable } from '@zeal/toolkit'

import { FiatMoney } from '@zeal/domains/Money'
import { useMoneyFormat } from '@zeal/domains/Money/hooks/useMoneyFormat'

type Props = {
    state: State
    onMsg: (msg: Msg) => void
}

export type State =
    | { type: 'closed' }
    | { type: 'cashback_estimate_info'; limit: FiatMoney | null }

type Msg = { type: 'close' }

export const Modal = ({ onMsg, state }: Props) => {
    const { formattedFiatMoneyCompact } = useMoneyFormat()
    switch (state.type) {
        case 'closed':
            return null
        case 'cashback_estimate_info':
            return (
                <Popup.Layout onMsg={onMsg}>
                    <Header
                        title={
                            <FormattedMessage
                                id="cashback-estimate.title"
                                defaultMessage="Cashback estimate"
                            />
                        }
                        subtitle={
                            state.limit ? (
                                <FormattedMessage
                                    id="cashback-estimate.text"
                                    defaultMessage="This is an estimate and NOT guaranteed payouts. All publicly known cashback rules are applied, but Gnosis Pay may exclude transactions at their discretion. A maximum spend of {amount} per week qualifies for Cashback even if the estimate for this transaction would indicate a higher total amount."
                                    values={{
                                        amount: formattedFiatMoneyCompact({
                                            money: state.limit,
                                        }),
                                    }}
                                />
                            ) : (
                                <FormattedMessage
                                    id="cashback-estimate.text.fallback"
                                    defaultMessage="This is an estimate and NOT guaranteed payouts. All publicly known cashback rules are applied, but Gnosis Pay may exclude transactions at their discretion."
                                />
                            )
                        }
                    />
                </Popup.Layout>
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
