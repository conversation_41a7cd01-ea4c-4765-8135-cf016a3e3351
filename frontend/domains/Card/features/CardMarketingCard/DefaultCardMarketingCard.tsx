import { FormattedMessage } from 'react-intl'

import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { SolidGift } from '@zeal/uikit/Icon/SolidGift'
import { MarketingCard } from '@zeal/uikit/MarketingCard'
import { Row } from '@zeal/uikit/Row'
import { Text } from '@zeal/uikit/Text'

import { getFormattedPercentage } from '@zeal/toolkit/Percentage'

import { DEFAULT_BREWARD_AMOUNT_IN_FIAT } from '@zeal/domains/Card/domains/Reward/constants'
import { FormattedMoneyPrecise } from '@zeal/domains/Money/components/FormattedMoneyPrecise'

type Props = {
    skyTakerApy: number
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'on_card_marketing_card_click' }

export const DefaultCardMarketingCard = ({ onMsg, skyTakerApy }: Props) => {
    return (
        <MarketingCard
            variant="card_promo"
            onClick={() => onMsg({ type: 'on_card_marketing_card_click' })}
        >
            <Column spacing={0} fill alignY="stretch">
                <Text variant="title1" weight="medium" color="gray100">
                    <FormattedMessage
                        id="card-marketing-card.title"
                        defaultMessage="Europe’s high interest Visa card"
                    />
                </Text>
                <Column spacing={8}>
                    <MarketingCard.InfoGroup
                        variant="card_promo"
                        left={
                            <MarketingCard.InfoGroupItem
                                variant="card_promo"
                                title={({ color, variant, weight }) => (
                                    <Text
                                        variant={variant}
                                        weight={weight}
                                        color={color}
                                    >
                                        {getFormattedPercentage(skyTakerApy)}
                                    </Text>
                                )}
                                subTitle={({ color, variant, weight }) => (
                                    <Text
                                        variant={variant}
                                        weight={weight}
                                        color={color}
                                    >
                                        <FormattedMessage
                                            id="card-marketing-card.left.subtitle"
                                            defaultMessage="Interest"
                                        />
                                    </Text>
                                )}
                            />
                        }
                        center={
                            <MarketingCard.InfoGroupItem
                                variant="card_promo"
                                title={({ color, variant, weight }) => (
                                    <Text
                                        variant={variant}
                                        weight={weight}
                                        color={color}
                                    >
                                        <FormattedMessage
                                            id="card-marketing-card.center.title"
                                            defaultMessage="0%"
                                        />
                                    </Text>
                                )}
                                subTitle={({ color, variant, weight }) => (
                                    <Text
                                        variant={variant}
                                        weight={weight}
                                        color={color}
                                    >
                                        <FormattedMessage
                                            id="card-marketing-card.center.subtitle"
                                            defaultMessage="FX Fees"
                                        />
                                    </Text>
                                )}
                            />
                        }
                        right={
                            <MarketingCard.InfoGroupItem
                                variant="card_promo"
                                title={({ variant, weight }) => (
                                    <Row spacing={2}>
                                        <Text
                                            variant={variant}
                                            weight={weight}
                                            color="teal60"
                                        >
                                            <FormattedMoneyPrecise
                                                money={
                                                    DEFAULT_BREWARD_AMOUNT_IN_FIAT
                                                }
                                                withSymbol
                                                sign={null}
                                            />
                                        </Text>
                                        <SolidGift size={24} color="teal60" />
                                    </Row>
                                )}
                                subTitle={({ color, variant, weight }) => (
                                    <Text
                                        variant={variant}
                                        weight={weight}
                                        color={color}
                                    >
                                        <FormattedMessage
                                            id="card-marketing-card.right.subtitle"
                                            defaultMessage="Sign-up Gift"
                                        />
                                    </Text>
                                )}
                            />
                        }
                    />
                    <Row spacing={0}>
                        <Button
                            variant="primary"
                            size="regular"
                            onClick={() =>
                                onMsg({
                                    type: 'on_card_marketing_card_click',
                                })
                            }
                        >
                            <FormattedMessage
                                id="card-marketing-tile.get-started"
                                defaultMessage="Get started"
                            />
                        </Button>
                    </Row>
                </Column>
            </Column>
        </MarketingCard>
    )
}
