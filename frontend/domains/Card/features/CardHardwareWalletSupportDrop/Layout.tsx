import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { HeaderV2 } from '@zeal/uikit/HeaderV2'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { IconButton } from '@zeal/uikit/IconButton'
import { Screen } from '@zeal/uikit/Screen'

import { notReachable } from '@zeal/toolkit'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account, AccountsMap } from '@zeal/domains/Account'

type Props = {
    variant: 'closable' | 'not_closable'
    cardReadonlySignerAddress: Web3.address.Address
    accountsMap: AccountsMap
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | { type: 'on_card_disconnected'; cardReadonlySigner: Account }
    | { type: 'on_hw_support_drop_add_new_owner_clicked' }

export const Layout = ({
    accountsMap,
    cardReadonlySignerAddress,
    variant,
    onMsg,
}: Props) => (
    <Screen
        padding="form"
        background="light"
        onNavigateBack={(() => {
            switch (variant) {
                case 'closable':
                    return () => () => onMsg({ type: 'close' })
                case 'not_closable':
                    return null
                default:
                    return notReachable(variant)
            }
        })()}
    >
        {(() => {
            switch (variant) {
                case 'closable':
                    return (
                        <ActionBar
                            left={
                                <IconButton
                                    variant="on_light"
                                    onClick={() => onMsg({ type: 'close' })}
                                >
                                    {({ color }) => (
                                        <BackIcon size={24} color={color} />
                                    )}
                                </IconButton>
                            }
                        />
                    )
                case 'not_closable':
                    return null
                default:
                    return notReachable(variant)
            }
        })()}

        <Column spacing={0} fill alignY="stretch">
            <HeaderV2
                title={
                    <FormattedMessage
                        id="card.hw-wallet-support-drop.title"
                        defaultMessage="Zeal no longer supports hardware wallets for Card"
                    />
                }
                subtitle={
                    <FormattedMessage
                        id="card.hw-wallet-support-drop.subtitle"
                        defaultMessage="To continue using your Gnosis Pay Card in Zeal, please add another owner to your Card who isn’t using a hardware wallet."
                    />
                }
                size="medium"
                align="left"
            />
            <Actions variant="default" direction="column">
                <Button
                    variant="secondary"
                    size="regular"
                    onClick={() =>
                        onMsg({
                            type: 'on_card_disconnected',
                            cardReadonlySigner:
                                accountsMap[cardReadonlySignerAddress],
                        })
                    }
                >
                    <FormattedMessage
                        id="card.hw-wallet-support-drop.disconnect-btn"
                        defaultMessage="Disconnect Card from Zeal"
                    />
                </Button>
                <Button
                    variant="primary"
                    size="regular"
                    onClick={() =>
                        onMsg({
                            type: 'on_hw_support_drop_add_new_owner_clicked',
                        })
                    }
                >
                    <FormattedMessage
                        id="card.hw-wallet-support-drop.add-owner-btn"
                        defaultMessage="Add new owner to Card"
                    />
                </Button>
            </Actions>
        </Column>
    </Screen>
)
