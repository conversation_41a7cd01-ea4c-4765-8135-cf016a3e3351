import { failure, Result, string, success } from '@zeal/toolkit/Result'

import { CardTermsType, GnosisTermsType } from '@zeal/domains/Card'

const GNOSIS_TERMS_TYPE_MAP: Record<GnosisTermsType, CardTermsType> = {
    'general-tos': 'general_terms',
    'card-monavate-tos': 'monavate_terms',
    'cashback-tos': 'cashback_terms',
    'privacy-policy': 'privacy_policy',
}

export const parseCardTermsType = (
    input: unknown
): Result<unknown, CardTermsType> =>
    string(input).andThen((str) => {
        const value = GNOSIS_TERMS_TYPE_MAP[str as GnosisTermsType]

        if (!value) {
            return failure({ type: 'unknown terms type', value: str })
        }

        return success(value)
    })
