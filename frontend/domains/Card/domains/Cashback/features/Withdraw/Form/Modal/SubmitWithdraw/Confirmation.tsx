import { FormattedMessage } from 'react-intl'

import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { Header } from '@zeal/uikit/Header'
import { GNO } from '@zeal/uikit/Icon/GNO'
import { Popup } from '@zeal/uikit/Popup'

import {
    CashbackWithdrawalRequest,
    EligibleForCashback,
} from '@zeal/domains/Card/domains/Cashback'
import { FormattedCashbackPercentage } from '@zeal/domains/Card/domains/Cashback/components/FormattedCashbackPercentage'
import { getCashbackPercentage } from '@zeal/domains/Card/domains/Cashback/helpers/getCashbackPercentage'
import { FormattedMoneyPrecise } from '@zeal/domains/Money/components/FormattedMoneyPrecise'

type Props = {
    cashbackWithdrawRequest: CashbackWithdrawalRequest
    cardCashback: EligibleForCashback
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'close' } | { type: 'on_i_understand_clicked' }

export const Confirmation = ({
    cashbackWithdrawRequest,
    cardCashback,
    onMsg,
}: Props) => {
    const newPercentage = getCashbackPercentage({
        amount: cashbackWithdrawRequest.totalAmountAfterWithdrawal,
        earlyAdopter: cardCashback.earlyAdopter,
    })

    return (
        <Popup.Layout onMsg={onMsg} background="surfaceDefault">
            <Column spacing={24}>
                <Header
                    icon={({ size }) => <GNO size={size} />}
                    title={
                        <FormattedMessage
                            id="cashback.withdrawal.delayTransaction.title"
                            defaultMessage="Start GNO withdrawal with{br} a 3-minute delay"
                            values={{ br: '\n' }}
                        />
                    }
                    subtitle={
                        <FormattedMessage
                            id="cashback.withdrawal.confirmation.subtitle"
                            defaultMessage="Start withdrawal of {amount} with a 3-minute delay. This will reduce your cashback to {after}."
                            values={{
                                br: '\n',
                                amount: (
                                    <FormattedMoneyPrecise
                                        withSymbol
                                        sign={null}
                                        money={
                                            cashbackWithdrawRequest.withdrawalAmount
                                        }
                                    />
                                ),
                                after: (
                                    <FormattedCashbackPercentage
                                        cashbackAmount={newPercentage}
                                    />
                                ),
                            }}
                        />
                    }
                />
                <Popup.Actions>
                    <Button
                        variant="secondary"
                        onClick={() => onMsg({ type: 'close' })}
                        size="regular"
                    >
                        <FormattedMessage
                            id="action.cancel"
                            defaultMessage="Cancel"
                        />
                    </Button>
                    <Button
                        variant="primary"
                        onClick={() => {
                            onMsg({ type: 'on_i_understand_clicked' })
                        }}
                        size="regular"
                    >
                        <FormattedMessage
                            id="action.understood"
                            defaultMessage="I understand"
                        />
                    </Button>
                </Popup.Actions>
            </Column>
        </Popup.Layout>
    )
}
