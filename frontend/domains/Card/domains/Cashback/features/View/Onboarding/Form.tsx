import { useEffect } from 'react'
import { FormattedMessage, useIntl } from 'react-intl'

import { get } from '@zeal/api/gnosisApi'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { BannerSolid } from '@zeal/uikit/BannerSolid'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { Group } from '@zeal/uikit/Group'
import { Header } from '@zeal/uikit/Header'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { BoldDownload } from '@zeal/uikit/Icon/BoldDownload'
import { Bridge } from '@zeal/uikit/Icon/Bridge'
import { Cashback } from '@zeal/uikit/Icon/Cashback'
import { GNO } from '@zeal/uikit/Icon/GNO'
import { IconButton } from '@zeal/uikit/IconButton'
import { BulletpointsListItem } from '@zeal/uikit/ListItem/BulletpointsListItem'
import { LoadingLayout } from '@zeal/uikit/LoadingLayout'
import { Screen } from '@zeal/uikit/Screen'
import { ScrollContainer } from '@zeal/uikit/ScrollContainer'
import { TextButton } from '@zeal/uikit/TextButton'

import { notReachable } from '@zeal/toolkit'
import { ImperativeError } from '@zeal/toolkit/Error'
import { parse as parseJSON } from '@zeal/toolkit/JSON'
import { useLazyLoadableData } from '@zeal/toolkit/LoadableData/LazyLoadableData'
import { useLiveRef } from '@zeal/toolkit/React'
import { arrayOf, object, string, unknown } from '@zeal/toolkit/Result'
import { openExternalURL } from '@zeal/toolkit/Window'

import { Account } from '@zeal/domains/Account'
import { CardSlientSignKeyStore } from '@zeal/domains/Card'
import { acceptTermsAndConditions } from '@zeal/domains/Card/api/acceptTermsAndConditions'
import { loginWithCache } from '@zeal/domains/Card/api/login'
import {
    cashBackTermsConfig,
    DEFAULT_CASHBACK_PERCENT,
    GNOSIS_PAY_CASHBACK_TERMS_URL,
} from '@zeal/domains/Card/domains/Cashback/constants'
import { AppErrorPopup } from '@zeal/domains/Error/components/AppErrorPopup'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { parseAppError } from '@zeal/domains/Error/parsers/parseAppError'

type Props = {
    cardReadOnlySigner: Account
    keyStore: CardSlientSignKeyStore
    sessionPassword: string
    installationId: string
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | { type: 'on_terms_and_conditions_accepted_successfully' }

const fetch = async ({
    cardReadOnlySigner,
    sessionPassword,
    keyStore,
    signal,
}: {
    cardReadOnlySigner: Account
    keyStore: CardSlientSignKeyStore
    sessionPassword: string
    signal?: AbortSignal
}): Promise<void> => {
    try {
        await acceptTermsAndConditions({
            termsToAccept: {
                type: 'cashback_terms',
                version: cashBackTermsConfig.version,
            },
            sessionPassword,
            keyStore,
            readonlySignerAddress: cardReadOnlySigner.address,
            signal,
        })
    } catch (error) {
        const parsedError = parseAppError(error)

        switch (parsedError.type) {
            case 'gnosis_pay_already_accepted_terms_error':
                try {
                    const gnosisPayLoginInfo = await loginWithCache({
                        keyStore,
                        signal,
                        sessionPassword,
                        readonlySignerAddress: cardReadOnlySigner.address,
                    })

                    const [userResponse, termsResponse] = await Promise.all([
                        get(
                            '/user',
                            {
                                auth: {
                                    type: 'bearer_token',
                                    token: gnosisPayLoginInfo.token,
                                },
                            },
                            signal
                        ),
                        get(
                            '/user/terms',
                            {
                                auth: {
                                    type: 'bearer_token',
                                    token: gnosisPayLoginInfo.token,
                                },
                            },
                            signal
                        ),
                    ])

                    const userId = string(userResponse)
                        .andThen(parseJSON)
                        .andThen(object)
                        .andThen((obj) => string(obj.id))
                        .getSuccessResultOrThrow(
                            'failed to parse gnoPay userId'
                        )

                    const rawTerms = string(termsResponse)
                        .andThen(parseJSON)
                        .andThen(object)
                        .andThen((obj) => arrayOf(obj.terms, unknown))
                        .getSuccessResultOrThrow('Failed to parse raw terms')

                    throw new ImperativeError(
                        'Cashback terms acceptance failure with valid terms structure',
                        { rawTerms, userId }
                    )
                } catch (e) {
                    captureError(e)
                }
                break
            default:
                break
        }
    }
}

export const Form = ({
    onMsg,
    sessionPassword,
    keyStore,
    cardReadOnlySigner,
    installationId,
}: Props) => {
    const [loadable, setLoadable] = useLazyLoadableData(fetch, {
        type: 'not_asked',
    })

    const { formatNumber } = useIntl()

    const liveMsg = useLiveRef(onMsg)

    useEffect(() => {
        switch (loadable.type) {
            case 'not_asked':
            case 'loading':
            case 'error':
                break
            case 'loaded':
                liveMsg.current({
                    type: 'on_terms_and_conditions_accepted_successfully',
                })
                break
            /* istanbul ignore next */
            default:
                return notReachable(loadable)
        }
    }, [loadable, liveMsg])

    switch (loadable.type) {
        case 'not_asked':
            return (
                <Screen
                    padding="form"
                    background="light"
                    onNavigateBack={() => onMsg({ type: 'close' })}
                >
                    <ActionBar
                        left={
                            <IconButton
                                variant="on_light"
                                onClick={() => onMsg({ type: 'close' })}
                            >
                                {({ color }) => (
                                    <BackIcon size={24} color={color} />
                                )}
                            </IconButton>
                        }
                    />
                    <Column spacing={8} fill shrink>
                        <Column spacing={24} fill shrink>
                            <Header
                                icon={({ size, color }) => (
                                    <Cashback size={size} color={color} />
                                )}
                                title={
                                    <FormattedMessage
                                        id="cardCashback.onboarding.title"
                                        defaultMessage="Get up to {percentage} Cashback"
                                        values={{
                                            // TODO @resetko-zeal use getFormattedPercentage
                                            percentage: formatNumber(
                                                DEFAULT_CASHBACK_PERCENT,
                                                {
                                                    maximumFractionDigits: 0,
                                                    minimumFractionDigits: 0,
                                                    style: 'percent',
                                                }
                                            ),
                                        }}
                                    />
                                }
                            />
                            <ScrollContainer
                                contentFill
                                withFloatingActions={false}
                            >
                                <Column spacing={8} fill>
                                    <Group variant="default">
                                        <BulletpointsListItem
                                            avatar={({ size }) => (
                                                <GNO size={size} />
                                            )}
                                            text={
                                                <FormattedMessage
                                                    id="cardCashback.onboarding.bullets.cashback_sent_weekly"
                                                    defaultMessage="Cashback is sent to your card at the start of the week after it was earned."
                                                />
                                            }
                                            rightIcon={null}
                                        />

                                        <BulletpointsListItem
                                            avatar={({ size }) => (
                                                <BoldDownload
                                                    size={size}
                                                    color="teal40"
                                                />
                                            )}
                                            text={
                                                <FormattedMessage
                                                    id="cardCashback.onboarding.bullets.more_deposit_more_earn"
                                                    defaultMessage="The more you deposit the more you earn on every purchase."
                                                />
                                            }
                                            rightIcon={null}
                                        />

                                        <BulletpointsListItem
                                            avatar={({ size }) => (
                                                <Bridge
                                                    size={size}
                                                    color="teal40"
                                                />
                                            )}
                                            text={
                                                <FormattedMessage
                                                    id="earn.earn_while_spend_info.bullets.cashback_is_sent_to_your_card"
                                                    defaultMessage="Withdraw funds anytime"
                                                />
                                            }
                                            rightIcon={null}
                                        />
                                    </Group>
                                    <BannerSolid
                                        variant="light"
                                        title={
                                            <FormattedMessage
                                                id="cashback-onbarding-tersm.title"
                                                defaultMessage="Terms of use and Privacy"
                                            />
                                        }
                                        subtitle={
                                            <FormattedMessage
                                                id="cashback-onbarding-tersm.subtitle"
                                                defaultMessage="Your Card transaction data will be shared with Karpatkey, who’s responsible for distributing Cashback rewards. By clicking accept, you agree to Gnosis DAO Cashback <terms>Terms and Conditions</terms>"
                                                values={{
                                                    br: '\n',
                                                    terms: (msg) => (
                                                        <TextButton
                                                            onClick={() =>
                                                                openExternalURL(
                                                                    GNOSIS_PAY_CASHBACK_TERMS_URL
                                                                )
                                                            }
                                                        >
                                                            {msg}
                                                        </TextButton>
                                                    ),
                                                }}
                                            />
                                        }
                                    />
                                </Column>
                            </ScrollContainer>
                        </Column>

                        <Actions variant="default">
                            <Button
                                variant="secondary"
                                size="regular"
                                onClick={() => {
                                    onMsg({ type: 'close' })
                                }}
                            >
                                <FormattedMessage
                                    id="action.cancel"
                                    defaultMessage="Cancel"
                                />
                            </Button>
                            <Button
                                variant="primary"
                                size="regular"
                                onClick={() =>
                                    setLoadable({
                                        type: 'loading',
                                        params: {
                                            sessionPassword,
                                            keyStore,
                                            cardReadOnlySigner,
                                        },
                                    })
                                }
                            >
                                <FormattedMessage
                                    id="action.accpet"
                                    defaultMessage="Accept"
                                />
                            </Button>
                        </Actions>
                    </Column>
                </Screen>
            )
        case 'loading':
        case 'loaded':
            return (
                <LoadingLayout
                    title={null}
                    actionBar={
                        <ActionBar
                            left={
                                <IconButton
                                    variant="on_light"
                                    onClick={() => onMsg({ type: 'close' })}
                                >
                                    {({ color }) => (
                                        <BackIcon size={24} color={color} />
                                    )}
                                </IconButton>
                            }
                        />
                    }
                    onClose={() => onMsg({ type: 'close' })}
                />
            )
        case 'error':
            return (
                <>
                    <LoadingLayout
                        title={null}
                        actionBar={
                            <ActionBar
                                left={
                                    <IconButton
                                        variant="on_light"
                                        onClick={() => onMsg({ type: 'close' })}
                                    >
                                        {({ color }) => (
                                            <BackIcon size={24} color={color} />
                                        )}
                                    </IconButton>
                                }
                            />
                        }
                        onClose={() => onMsg({ type: 'close' })}
                    />
                    <AppErrorPopup
                        error={parseAppError(loadable.error)}
                        installationId={installationId}
                        onMsg={(msg) => {
                            switch (msg.type) {
                                case 'close':
                                    onMsg(msg)
                                    break
                                case 'try_again_clicked':
                                    setLoadable({
                                        type: 'loading',
                                        params: {
                                            sessionPassword,
                                            cardReadOnlySigner,
                                            keyStore,
                                        },
                                    })
                                    break
                                /* istanbul ignore next */
                                default:
                                    return notReachable(msg)
                            }
                        }}
                    />
                </>
            )
        /* istanbul ignore next */
        default:
            return notReachable(loadable)
    }
}
