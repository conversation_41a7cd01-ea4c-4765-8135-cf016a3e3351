import { useRef } from 'react'
import { FormattedMessage } from 'react-intl'
import { TextInput } from 'react-native'

import { Actions } from '@zeal/uikit/Actions'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { ArrowDown } from '@zeal/uikit/Icon/ArrowDown'
import { QuestionCircle } from '@zeal/uikit/Icon/QuestionCircle'
import { Input } from '@zeal/uikit/Input'
import { InputButton } from '@zeal/uikit/InputButton'
import { ScrollContainer } from '@zeal/uikit/ScrollContainer'
import { Text } from '@zeal/uikit/Text'

import { noop, notReachable } from '@zeal/toolkit'
import { ZealPlatform } from '@zeal/toolkit/OS/ZealPlatform'

import { Counterparty } from '@zeal/domains/Card/domains/MoneriumBankTransfer/domains/Counterparty'
import { Avatar as CountryIcon } from '@zeal/domains/Country/components/Avatar'
import { COUNTRIES_MAP } from '@zeal/domains/Country/constants'
import { Avatar as CurrencyAvatar } from '@zeal/domains/Currency/components/Avatar'
import { sanitiseIbanInput } from '@zeal/domains/Currency/domains/BankTransfer/helpers/sanitiseIbanInput'
import { postUserEvent } from '@zeal/domains/UserEvents/api/postUserEvent'

import { Form, SubmitErrors, validate } from './validate'

export type { Form } from './validate'

type Props = {
    installationId: string
    form: Form
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | { type: 'on_change_country_clicked' }
    | {
          type: 'on_form_change'
          form: Form
      }
    | {
          type: 'on_save_counterparty_form_submitted'
          counterparty: Counterparty
      }

export const CounterpartyForm = ({ installationId, form, onMsg }: Props) => {
    const validationResult = validate({ form })
    const error = validationResult.getFailureReason()
    const lastNameInput = useRef<TextInput>(null)
    const labelInput = useRef<TextInput>(null)
    const firstNameInput = useRef<TextInput>(null)

    const onSubmit = () => {
        validationResult.tap((counterparty) => {
            postUserEvent({
                type: 'MoneriumAddRecipientCompletedEvent',
                installationId,
            })
            onMsg({
                type: 'on_save_counterparty_form_submitted',
                counterparty,
            })
        })
    }
    return (
        <Column spacing={8} fill shrink>
            <ScrollContainer withFloatingActions={false}>
                <Column spacing={8} fill>
                    <Column spacing={8}>
                        <Text
                            variant="paragraph"
                            weight="regular"
                            color="textSecondary"
                        >
                            <FormattedMessage
                                id="counterparty.iban"
                                defaultMessage="IBAN"
                            />
                        </Text>

                        <Input
                            keyboardType="default"
                            onSubmitEditing={() => {
                                switch (ZealPlatform.OS) {
                                    case 'ios':
                                    case 'android':
                                        firstNameInput.current?.focus()
                                        break
                                    case 'web':
                                        onSubmit()
                                        break
                                    /* istanbul ignore next */
                                    default:
                                        return notReachable(ZealPlatform)
                                }
                            }}
                            onChange={(e) =>
                                onMsg({
                                    type: 'on_form_change',
                                    form: {
                                        ...form,
                                        iban: sanitiseIbanInput(
                                            e.nativeEvent.text
                                        ),
                                    },
                                })
                            }
                            placeholder={`${form.countryCode || 'XX'}00 0000 0000 0000 0000`}
                            variant="large"
                            value={form.iban || ''}
                            state="normal"
                        />
                    </Column>

                    <Column spacing={8}>
                        <Text
                            variant="paragraph"
                            weight="regular"
                            color="textSecondary"
                        >
                            <FormattedMessage
                                id="counterparty.countryTitle"
                                defaultMessage="Recipient’s country"
                            />
                        </Text>

                        <InputButton
                            leftIcon={
                                form.countryCode ? (
                                    <CountryIcon
                                        countryCode={form.countryCode}
                                        size={28}
                                    />
                                ) : (
                                    <QuestionCircle
                                        size={28}
                                        color="iconDefault"
                                    />
                                )
                            }
                            rightIcon={
                                <ArrowDown color="iconDisabled" size={24} />
                            }
                            onClick={() => {
                                onMsg({
                                    type: 'on_change_country_clicked',
                                })
                            }}
                        >
                            {form.countryCode ? (
                                COUNTRIES_MAP[form.countryCode].name
                            ) : (
                                <FormattedMessage
                                    id="counterparty.country"
                                    defaultMessage="Country"
                                />
                            )}
                        </InputButton>
                    </Column>

                    <Column spacing={8}>
                        <Text
                            variant="paragraph"
                            weight="regular"
                            color="textSecondary"
                        >
                            <FormattedMessage
                                id="counterparty.currency"
                                defaultMessage="Currency"
                            />
                        </Text>

                        <InputButton
                            disabled
                            leftIcon={
                                <CurrencyAvatar
                                    currency={form.fiatCurrency}
                                    size={28}
                                />
                            }
                            rightIcon={<></>}
                            onClick={noop}
                        >
                            {form.fiatCurrency.code}
                        </InputButton>
                    </Column>

                    <Column spacing={8}>
                        <Text
                            variant="paragraph"
                            weight="regular"
                            color="textSecondary"
                        >
                            <FormattedMessage
                                id="counterparty.first_name"
                                defaultMessage="First name"
                            />
                        </Text>

                        <Input
                            keyboardType="default"
                            autoComplete="given-name"
                            returnKeyType="next"
                            ref={firstNameInput}
                            onSubmitEditing={() => {
                                switch (ZealPlatform.OS) {
                                    case 'ios':
                                    case 'android':
                                        lastNameInput.current?.focus()
                                        break
                                    case 'web':
                                        onSubmit()
                                        break
                                    /* istanbul ignore next */
                                    default:
                                        return notReachable(ZealPlatform)
                                }
                            }}
                            blurOnSubmit={false}
                            onChange={(e) =>
                                onMsg({
                                    type: 'on_form_change',
                                    form: {
                                        ...form,
                                        firstName: e.nativeEvent.text,
                                    },
                                })
                            }
                            state="normal"
                            placeholder="Thomas"
                            variant="large"
                            value={form.firstName || ''}
                        />
                    </Column>

                    <Column spacing={8}>
                        <Text
                            variant="paragraph"
                            weight="regular"
                            color="textSecondary"
                        >
                            <FormattedMessage
                                id="currency.last_name"
                                defaultMessage="Last name"
                            />
                        </Text>

                        <Input
                            keyboardType="default"
                            ref={lastNameInput}
                            autoComplete="family-name"
                            returnKeyType="next"
                            blurOnSubmit={false} // prevent keyboard flashing when pressing "next"
                            onSubmitEditing={() => {
                                switch (ZealPlatform.OS) {
                                    case 'ios':
                                    case 'android':
                                        labelInput.current?.focus()
                                        break
                                    case 'web':
                                        onSubmit()
                                        break
                                    /* istanbul ignore next */
                                    default:
                                        return notReachable(ZealPlatform)
                                }
                            }}
                            onChange={(e) =>
                                onMsg({
                                    type: 'on_form_change',
                                    form: {
                                        ...form,
                                        lastName: e.nativeEvent.text,
                                    },
                                })
                            }
                            state="normal"
                            placeholder="Anderson"
                            variant="large"
                            value={form.lastName || ''}
                        />
                    </Column>
                    <Column spacing={8}>
                        <Text
                            variant="paragraph"
                            weight="regular"
                            color="textSecondary"
                        >
                            <FormattedMessage
                                id="currency.label"
                                defaultMessage="Label (Optional)"
                            />
                        </Text>

                        <Input
                            keyboardType="default"
                            autoComplete="family-name"
                            ref={labelInput}
                            returnKeyType="next"
                            blurOnSubmit={false}
                            onSubmitEditing={onSubmit}
                            onChange={(e) =>
                                onMsg({
                                    type: 'on_form_change',
                                    form: {
                                        ...form,
                                        label: e.nativeEvent.text,
                                    },
                                })
                            }
                            state="normal"
                            placeholder=""
                            variant="large"
                            value={form.label || ''}
                        />
                    </Column>
                </Column>
            </ScrollContainer>
            <Actions variant="default">
                <Button
                    variant="primary"
                    size="regular"
                    onClick={onSubmit}
                    disabled={!!error?.submit}
                >
                    {error && error.submit ? (
                        <SubmitErrorsTitle errors={error.submit} />
                    ) : (
                        <FormattedMessage
                            id="action.continue"
                            defaultMessage="Continue"
                        />
                    )}
                </Button>
            </Actions>
        </Column>
    )
}

const SubmitErrorsTitle = ({ errors }: { errors: SubmitErrors }) => {
    switch (errors.type) {
        case 'iban_invalid':
            return (
                <FormattedMessage
                    id="validation.invalid.iban"
                    defaultMessage="Invalid IBAN"
                />
            )

        case 'iban_required':
            return (
                <FormattedMessage
                    id="validation.required.iban"
                    defaultMessage="IBAN required"
                />
            )

        case 'last_name_required':
            return (
                <FormattedMessage
                    id="validation.required.last_name"
                    defaultMessage="Last name required"
                />
            )
        case 'first_name_required':
            return (
                <FormattedMessage
                    id="validation.required.first_name"
                    defaultMessage="First name required"
                />
            )
        case 'country_required':
            return (
                <FormattedMessage
                    id="counterparty.errors.country_required"
                    defaultMessage="Country required"
                />
            )
        case 'first_name_invalid':
            return (
                <FormattedMessage
                    id="counterparty.errors.first_name.invalid"
                    defaultMessage="First name should be longer"
                />
            )
        case 'last_name_invalid':
            return (
                <FormattedMessage
                    id="counterparty.errors.last_name.invalid"
                    defaultMessage="Last name should be longer"
                />
            )

        default:
            return notReachable(errors)
    }
}
