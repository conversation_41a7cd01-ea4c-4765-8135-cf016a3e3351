import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Avatar } from '@zeal/uikit/Avatar'
import { Column } from '@zeal/uikit/Column'
import { Group } from '@zeal/uikit/Group'
import { Header } from '@zeal/uikit/Header'
import { BoldDiscount } from '@zeal/uikit/Icon/BoldDiscount'
import { BoldGeneralBank } from '@zeal/uikit/Icon/BoldGeneralBank'
import { CloseCross } from '@zeal/uikit/Icon/CloseCross'
import { Monerium } from '@zeal/uikit/Icon/Providers/Monerium'
import { ShieldDone } from '@zeal/uikit/Icon/ShieldDone'
import { IconButton } from '@zeal/uikit/IconButton'
import { BulletpointsListItem } from '@zeal/uikit/ListItem/BulletpointsListItem'
import { Popup } from '@zeal/uikit/Popup'
import { Tertiary } from '@zeal/uikit/Tertiary'
import { Text } from '@zeal/uikit/Text'

import { openExternalURL } from '@zeal/toolkit/Window'

import { MONERIUM_URL } from '@zeal/domains/Card/domains/MoneriumBankTransfer/constants'

type Props = {
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'close' }

export const MoneriumInfoPopup = ({ onMsg }: Props) => (
    <Popup.Layout onMsg={onMsg}>
        <Column spacing={0}>
            <ActionBar
                right={
                    <IconButton
                        variant="on_light"
                        onClick={() => onMsg({ type: 'close' })}
                    >
                        {({ color }) => <CloseCross size={24} color={color} />}
                    </IconButton>
                }
            />
            <Column spacing={24} fill>
                <Header
                    icon={({ size }) => (
                        <Avatar
                            size={size}
                            variant="rounded"
                            backgroundColor="surfaceDefault"
                        >
                            <Monerium size={size} />
                        </Avatar>
                    )}
                    title="Monerium"
                />
                <Group variant="default">
                    <BulletpointsListItem
                        avatar={({ size }) => (
                            <BoldGeneralBank size={size} color="green30" />
                        )}
                        text={
                            <FormattedMessage
                                id="moneriumInfo.registration"
                                defaultMessage="Monerium is authorised and regulated as an Electronic Money Institution under the Icelandic Electronic Money Act No. 17/2013 <link>Learn more</link>"
                                values={{
                                    link: (chunks) => (
                                        <Tertiary
                                            color="on_light"
                                            size="regular"
                                            onClick={() =>
                                                openExternalURL(MONERIUM_URL)
                                            }
                                        >
                                            {({
                                                color,
                                                textVariant,
                                                textWeight,
                                            }) => (
                                                <Text
                                                    textDecorationLine="underline"
                                                    color={color}
                                                    variant={textVariant}
                                                    weight={textWeight}
                                                >
                                                    {chunks}
                                                </Text>
                                            )}
                                        </Tertiary>
                                    ),
                                }}
                            />
                        }
                        rightIcon={null}
                    />
                    <BulletpointsListItem
                        avatar={({ size }) => (
                            <BoldDiscount size={size} color="teal40" />
                        )}
                        text={
                            <FormattedMessage
                                id="moneriumInfo.fees"
                                defaultMessage="You get 0% fees"
                            />
                        }
                        rightIcon={null}
                    />
                    <BulletpointsListItem
                        avatar={({ size }) => (
                            <ShieldDone size={size} color="blue30" />
                        )}
                        text={
                            <FormattedMessage
                                id="moneriumInfo.selfCustody"
                                defaultMessage="The digital cash that you receive is self-custodied and no one else will have control over your assets"
                            />
                        }
                        rightIcon={null}
                    />
                </Group>
            </Column>
        </Column>
    </Popup.Layout>
)
