import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Avatar } from '@zeal/uikit/Avatar'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { HeaderV2 } from '@zeal/uikit/HeaderV2'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { BoldDiscount } from '@zeal/uikit/Icon/BoldDiscount'
import { GnosisPayIcon } from '@zeal/uikit/Icon/GnosisPayIcon'
import { Monerium } from '@zeal/uikit/Icon/Providers/Monerium'
import { IconButton } from '@zeal/uikit/IconButton'
import { BulletpointsListItem } from '@zeal/uikit/ListItem/BulletpointsListItem'
import { Screen } from '@zeal/uikit/Screen'
import { Spacer } from '@zeal/uikit/Spacer'
import { Tertiary } from '@zeal/uikit/Tertiary'
import { Text } from '@zeal/uikit/Text'

import * as Web3 from '@zeal/toolkit/Web3'
import { openExternalURL } from '@zeal/toolkit/Window'

import { Account } from '@zeal/domains/Account'
import { AvatarWithoutBadge } from '@zeal/domains/Account/components/Avatar'
import {
    MONERIUM_TNC_URL,
    MONERIUM_URL,
} from '@zeal/domains/Card/domains/MoneriumBankTransfer/constants'

type Props = {
    cardReadOnlySigner: Account
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'close' } | { type: 'on_accept_clicked' }

export const AcceptMoneriumTermsAndConditions = ({
    cardReadOnlySigner,
    onMsg,
}: Props) => {
    return (
        <Screen
            padding="form"
            background="default"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <ActionBar
                left={
                    <IconButton
                        variant="on_light"
                        onClick={() => onMsg({ type: 'close' })}
                    >
                        {({ color }) => <BackIcon size={24} color={color} />}
                    </IconButton>
                }
            />
            <Column spacing={16} shrink fill>
                <HeaderV2
                    title={
                        <FormattedMessage
                            id="confirm-bank-transfer-recipient.title"
                            defaultMessage="Accept terms"
                        />
                    }
                    subtitle={null}
                    size="large"
                    align="left"
                />
                <Column spacing={8} shrink fill>
                    <BulletpointsListItem
                        avatar={({ size }) => (
                            <BoldDiscount color="green30" size={size} />
                        )}
                        text={
                            <FormattedMessage
                                id="confirm-bank-transfer-recipient.bullet-1"
                                defaultMessage="No fees on digital EUR"
                            />
                        }
                        rightIcon={null}
                    />
                    <BulletpointsListItem
                        avatar={({ size }) => (
                            <AvatarWithoutBadge
                                account={cardReadOnlySigner}
                                size={size}
                            />
                        )}
                        text={
                            <FormattedMessage
                                id="confirm-bank-transfer-recipient.bullet-2"
                                defaultMessage="Deposits to {walletLabel} {walletAddress}"
                                values={{
                                    walletLabel: cardReadOnlySigner.label,
                                    walletAddress: Web3.address.format(
                                        cardReadOnlySigner.address
                                    ),
                                }}
                            />
                        }
                        rightIcon={null}
                    />
                    <BulletpointsListItem
                        avatar={({ size }) => (
                            <GnosisPayIcon size={size} type="round" />
                        )}
                        text={
                            <FormattedMessage
                                id="confirm-bank-transfer-recipient.bullet-3"
                                defaultMessage="Share Gnosis Pay account details with Monerium, an authorised and regulated EMI. <link>Learn more</link>"
                                values={{
                                    link: (chunks) => (
                                        <Tertiary
                                            size="regular"
                                            color="on_light"
                                            onClick={() => {
                                                openExternalURL(MONERIUM_URL)
                                            }}
                                        >
                                            {({
                                                color,
                                                textVariant,
                                                textWeight,
                                            }) => (
                                                <Text
                                                    textDecorationLine="underline"
                                                    color={color}
                                                    variant={textVariant}
                                                    weight={textWeight}
                                                >
                                                    {chunks}
                                                </Text>
                                            )}
                                        </Tertiary>
                                    ),
                                }}
                            />
                        }
                        rightIcon={null}
                    />
                    <BulletpointsListItem
                        avatar={({ size }) => (
                            <Avatar size={size} variant="rounded">
                                <Monerium size={size} />
                            </Avatar>
                        )}
                        text={
                            <FormattedMessage
                                id="confirm-bank-transfer-recipient.bullet-4"
                                defaultMessage="Accept Monerium <link>terms of service</link>"
                                values={{
                                    link: (chunks) => (
                                        <Tertiary
                                            size="regular"
                                            color="on_light"
                                            onClick={() => {
                                                openExternalURL(
                                                    MONERIUM_TNC_URL
                                                )
                                            }}
                                        >
                                            {({
                                                color,
                                                textVariant,
                                                textWeight,
                                            }) => (
                                                <Text
                                                    textDecorationLine="underline"
                                                    color={color}
                                                    variant={textVariant}
                                                    weight={textWeight}
                                                >
                                                    {chunks}
                                                </Text>
                                            )}
                                        </Tertiary>
                                    ),
                                }}
                            />
                        }
                        rightIcon={null}
                    />
                    <Spacer />
                    <Actions variant="default">
                        <Button
                            variant="primary"
                            size="regular"
                            onClick={() => {
                                onMsg({
                                    type: 'on_accept_clicked',
                                })
                            }}
                        >
                            <FormattedMessage
                                id="action.accept"
                                defaultMessage="Accept"
                            />
                        </Button>
                    </Actions>
                </Column>
            </Column>
        </Screen>
    )
}
