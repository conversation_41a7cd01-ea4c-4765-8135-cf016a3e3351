import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Avatar } from '@zeal/uikit/Avatar'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { Header } from '@zeal/uikit/Header'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { Video } from '@zeal/uikit/Icon/Video'
import { IconButton } from '@zeal/uikit/IconButton'
import { ListItemButton } from '@zeal/uikit/ListItem'
import { SubtextListItem } from '@zeal/uikit/ListItem/SubtextListItem'
import { Screen } from '@zeal/uikit/Screen'
import { Text } from '@zeal/uikit/Text'

import { openExternalURL } from '@zeal/toolkit/Window'

import { Account } from '@zeal/domains/Account'
import { GNOSIS_PAY_ADD_CARD_OWNER_HELP } from '@zeal/domains/Card/constants'

type Props = {
    owner: Account
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | { type: 'on_create_smart_wallet_clicked' }
    | { type: 'monerium_on_card_disconnected' }

export const MoneriumAccountConflict = ({ onMsg, owner }: Props) => {
    return (
        <Screen
            background="light"
            onNavigateBack={() => onMsg({ type: 'close' })}
            padding="form"
        >
            <ActionBar
                left={
                    <IconButton
                        variant="on_light"
                        onClick={() => onMsg({ type: 'close' })}
                    >
                        {({ color }) => <BackIcon size={24} color={color} />}
                    </IconButton>
                }
            />
            <Column spacing={12} alignY="stretch">
                <Column spacing={24}>
                    <Header
                        title={
                            <FormattedMessage
                                id="conflicting-monerium-account.header"
                                defaultMessage="{wallet} linked to another Monerium account"
                                values={{
                                    wallet: owner.label,
                                }}
                            />
                        }
                        subtitle={
                            <FormattedMessage
                                id="conflicting-monerium-account.subtitle"
                                defaultMessage="Change your Gnosis Pay owner wallet"
                            />
                        }
                    />
                    <Column spacing={8}>
                        <ListItemButton
                            variant="default"
                            onClick={() =>
                                onMsg({
                                    type: 'on_create_smart_wallet_clicked',
                                })
                            }
                            background="surface"
                            disabled={false}
                            aria-current={false}
                            primaryText={
                                <FormattedMessage
                                    id="conflicting-monerium-account.create-wallet"
                                    defaultMessage="Create a new smart wallet"
                                />
                            }
                            avatar={({ size }) => (
                                <Avatar size={size} border="borderSecondary">
                                    <Text
                                        variant="caption1"
                                        weight="medium"
                                        color="textPrimary"
                                        align="center"
                                    >
                                        {1}
                                    </Text>
                                </Avatar>
                            )}
                        />
                        <SubtextListItem
                            size="large"
                            primaryText={
                                <FormattedMessage
                                    id="conflicting-monerium-account.add-owner"
                                    defaultMessage="Add as Gnosis Pay Owner"
                                />
                            }
                            avatar={({ size }) => (
                                <Avatar size={size} border="borderSecondary">
                                    <Text
                                        variant="caption1"
                                        weight="medium"
                                        color="textPrimary"
                                        align="center"
                                    >
                                        {2}
                                    </Text>
                                </Avatar>
                            )}
                            side={{
                                rightIcon: () => (
                                    <Video size={24} color="gray40" />
                                ),
                            }}
                            onClick={() =>
                                openExternalURL(GNOSIS_PAY_ADD_CARD_OWNER_HELP)
                            }
                            subItems={
                                <Text variant="footnote">
                                    <FormattedMessage
                                        id="card.connectWalletToCardGuide.addGnosisPayOwner.steps"
                                        defaultMessage="1. Open GnosisPay.com with your other wallet{br}2. Click “Account”{br}3. Click “Account details”{br}4. Click “Edit”, next to “Account Owner”, and{br}5. Click “Add address”{br}6. Paste your Zeal address and click save"
                                        values={{ br: '\n' }}
                                    />
                                </Text>
                            }
                        />
                        <ListItemButton
                            variant="default"
                            onClick={() =>
                                onMsg({
                                    type: 'monerium_on_card_disconnected',
                                })
                            }
                            background="surface"
                            disabled={false}
                            aria-current={false}
                            wrapPrimaryText
                            primaryText={
                                <FormattedMessage
                                    id="conflicting-monerium-account.disconnect-card"
                                    defaultMessage="Disconnect card from Zeal and reconnect with new owner"
                                />
                            }
                            avatar={({ size }) => (
                                <Avatar size={size} border="borderSecondary">
                                    <Text
                                        variant="caption1"
                                        weight="medium"
                                        color="textPrimary"
                                        align="center"
                                    >
                                        {3}
                                    </Text>
                                </Avatar>
                            )}
                        />
                    </Column>
                </Column>
                <Actions variant="default">
                    <Button
                        size="regular"
                        variant="secondary"
                        onClick={() => onMsg({ type: 'close' })}
                    >
                        <FormattedMessage
                            id="action.close"
                            defaultMessage="Close"
                        />
                    </Button>
                    <Button size="regular" variant="primary" disabled>
                        <FormattedMessage
                            id="action.complete-steps"
                            defaultMessage="Complete steps"
                        />
                    </Button>
                </Actions>
            </Column>
        </Screen>
    )
}
