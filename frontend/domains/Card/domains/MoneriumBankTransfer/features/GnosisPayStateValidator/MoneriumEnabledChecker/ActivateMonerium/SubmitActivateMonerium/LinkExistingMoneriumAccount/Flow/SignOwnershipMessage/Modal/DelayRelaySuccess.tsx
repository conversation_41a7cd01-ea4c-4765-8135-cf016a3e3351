import React from 'react'
import { FormattedMessage } from 'react-intl'

import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { Header } from '@zeal/uikit/Header'
import { GaugeMeterSpeedLimit } from '@zeal/uikit/Icon/GaugeMeterSpeedLimit'
import { Popup } from '@zeal/uikit/Popup'

import { notReachable } from '@zeal/toolkit'

type Props = {
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'on_monerium_sign_delay_relay_success_close_clicked' }

export const DelayRelaySuccess = ({ onMsg }: Props) => (
    <Popup.Layout
        onMsg={(msg) => {
            switch (msg.type) {
                case 'close':
                    onMsg({
                        type: 'on_monerium_sign_delay_relay_success_close_clicked',
                    })
                    break
                /* istanbul ignore next */
                default:
                    return notReachable(msg.type)
            }
        }}
        background="surfaceDefault"
    >
        <Column spacing={24}>
            <Header
                icon={({ size }) => (
                    <GaugeMeterSpeedLimit size={size} color="iconAccent2" />
                )}
                title={
                    <FormattedMessage
                        id="monerium-card-delay-relay.success.title"
                        defaultMessage="Come back in 3 min to continue Monerium setup"
                    />
                }
                subtitle={
                    <FormattedMessage
                        id="monerium-card-delay-relay.success.subtitle"
                        defaultMessage="For security reasons, card settings changes take 3 minutes to be processed."
                    />
                }
            />
            <Popup.Actions>
                <Button
                    variant="primary"
                    onClick={() =>
                        onMsg({
                            type: 'on_monerium_sign_delay_relay_success_close_clicked',
                        })
                    }
                    size="regular"
                >
                    <FormattedMessage
                        id="monerium-card-delay-relay.success.cta"
                        defaultMessage="Close"
                    />
                </Button>
            </Popup.Actions>
        </Column>
    </Popup.Layout>
)
