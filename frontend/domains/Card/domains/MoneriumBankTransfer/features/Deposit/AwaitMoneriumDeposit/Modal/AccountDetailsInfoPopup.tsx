import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Avatar } from '@zeal/uikit/Avatar'
import { Column } from '@zeal/uikit/Column'
import { Group } from '@zeal/uikit/Group'
import { CloseCross } from '@zeal/uikit/Icon/CloseCross'
import { Monerium } from '@zeal/uikit/Icon/Providers/Monerium'
import { IconButton } from '@zeal/uikit/IconButton'
import { BulletpointsListItem } from '@zeal/uikit/ListItem/BulletpointsListItem'
import { Popup } from '@zeal/uikit/Popup'
import { Tertiary } from '@zeal/uikit/Tertiary'
import { Text } from '@zeal/uikit/Text'

import { openExternalURL } from '@zeal/toolkit/Window'

import {
    DEFAULT_MONERIUM_CRYPTO_CURRENCY,
    DEFAULT_MONERIUM_FIAT_CURRENCY,
    MONERIUM_URL,
} from '@zeal/domains/Card/domains/MoneriumBankTransfer/constants'
import { AccountDetailsBulletpoints } from '@zeal/domains/Currency/domains/BankTransfer/components/AccountDetailsBulletpoints'
import { NetworkMap } from '@zeal/domains/Network'

type Props = {
    networkMap: NetworkMap
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'close' }

export const AccountDetailsInfoPopup = ({ networkMap, onMsg }: Props) => (
    <Popup.Layout onMsg={onMsg}>
        <Column spacing={20}>
            <ActionBar
                left={
                    <Text variant="title3" weight="medium" color="gray20">
                        <FormattedMessage
                            id="monerium-deposit.account-details-info-popup.title"
                            defaultMessage="Your account details"
                        />
                    </Text>
                }
                right={
                    <IconButton
                        variant="on_light"
                        onClick={() => onMsg({ type: 'close' })}
                    >
                        {({ color }) => <CloseCross size={24} color={color} />}
                    </IconButton>
                }
            />
            <Group variant="default">
                <AccountDetailsBulletpoints
                    networkMap={networkMap}
                    inputFiatCurrency={DEFAULT_MONERIUM_FIAT_CURRENCY}
                    outputCryptoCurrency={DEFAULT_MONERIUM_CRYPTO_CURRENCY}
                />
                <BulletpointsListItem
                    avatar={({ size }) => (
                        <Avatar size={size} variant="rounded">
                            <Monerium size={size} />
                        </Avatar>
                    )}
                    text={
                        <FormattedMessage
                            id="account-details.monerium"
                            defaultMessage="Transfers are done using Monerium, an authorised and regulated EMI. <link>Learn more</link>"
                            values={{
                                link: (chunks) => (
                                    <Tertiary
                                        color="on_light"
                                        size="regular"
                                        onClick={() =>
                                            openExternalURL(MONERIUM_URL)
                                        }
                                    >
                                        {({
                                            color,
                                            textVariant,
                                            textWeight,
                                        }) => (
                                            <Text
                                                textDecorationLine="underline"
                                                color={color}
                                                variant={textVariant}
                                                weight={textWeight}
                                            >
                                                {chunks}
                                            </Text>
                                        )}
                                    </Tertiary>
                                ),
                            }}
                        />
                    }
                    rightIcon={null}
                />
            </Group>
        </Column>
    </Popup.Layout>
)
