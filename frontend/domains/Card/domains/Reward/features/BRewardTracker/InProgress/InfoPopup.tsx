import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { Group } from '@zeal/uikit/Group'
import { HeaderV2 } from '@zeal/uikit/HeaderV2'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { BoldLock } from '@zeal/uikit/Icon/BoldLock'
import { Clock } from '@zeal/uikit/Icon/Clock'
import { CreditCardSolid } from '@zeal/uikit/Icon/CreditCardSolid'
import { SolidGift } from '@zeal/uikit/Icon/SolidGift'
import { IconButton } from '@zeal/uikit/IconButton'
import { BulletpointsListItem } from '@zeal/uikit/ListItem/BulletpointsListItem'
import { Screen } from '@zeal/uikit/Screen'
import { Text } from '@zeal/uikit/Text'

import { useReadableDistance } from '@zeal/toolkit/Date/useReadableDistance'

import { InProgressBReward } from '@zeal/domains/Card/domains/Reward'
import { FormattedBRewardInFiat } from '@zeal/domains/Card/domains/Reward/features/FormattedBRewardInFiat'
import { getExpiredInTimestempMS } from '@zeal/domains/Card/domains/Reward/helpers/getExpiredInTimestempMS'
import { getLeftToSpend } from '@zeal/domains/Card/domains/Reward/helpers/getLeftToSpend'
import { convertStableCoinToFiat } from '@zeal/domains/Money/helpers/convertStableCoinToFiat'
import { useMoneyFormat } from '@zeal/domains/Money/hooks/useMoneyFormat'

type Props = {
    reward: InProgressBReward
    installationCampaign: string | null
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'close' }

export const InfoPopup = ({ reward, installationCampaign, onMsg }: Props) => {
    const { formattedFiatMoneyCompact } = useMoneyFormat()
    const expiredIn = getExpiredInTimestempMS({ reward })
    const formatDistance = useReadableDistance()
    const expiredInFormatted = formatDistance(expiredIn)

    const amountLeftToSpent = getLeftToSpend({ reward })
    const formattedAmountLeftToSpent = formattedFiatMoneyCompact({
        money: convertStableCoinToFiat({ money: amountLeftToSpent }),
    })

    return (
        <Screen
            background="light"
            padding="form"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <ActionBar
                left={
                    <IconButton
                        variant="on_light"
                        onClick={() => onMsg({ type: 'close' })}
                    >
                        {({ color }) => <BackIcon size={24} color={color} />}
                    </IconButton>
                }
            />
            <Column spacing={24} fill shrink>
                <HeaderV2
                    title={
                        <FormattedMessage
                            id="breaward.in_porgress.info_popup.header"
                            defaultMessage="Earn {earn} by spending {remaining}"
                            values={{
                                earn: (
                                    <FormattedBRewardInFiat
                                        installationCampaign={
                                            installationCampaign
                                        }
                                    />
                                ),
                                remaining: formattedAmountLeftToSpent,
                            }}
                        />
                    }
                    subtitle={null}
                    size="large"
                    align="left"
                />

                <Column spacing={20}>
                    <Group variant="default">
                        <BulletpointsListItem
                            avatar={({ size }) => (
                                <Clock size={size} color="orange30" />
                            )}
                            text={
                                <FormattedMessage
                                    id="breaward.in_porgress.info_popup.bullet_point_1"
                                    defaultMessage="Spend {remaining} within the next {time} to claim this reward."
                                    values={{
                                        remaining: formattedAmountLeftToSpent,
                                        time: expiredInFormatted,
                                    }}
                                />
                            }
                            rightIcon={null}
                        />
                        <BulletpointsListItem
                            avatar={({ size }) => (
                                <CreditCardSolid size={size} color="purple50" />
                            )}
                            text={
                                <FormattedMessage
                                    id="breaward.in_porgress.info_popup.bullet_point_2"
                                    defaultMessage="Only valid Gnosis Pay purchases count towards your spend amount."
                                />
                            }
                            rightIcon={null}
                        />
                        <BulletpointsListItem
                            avatar={({ size }) => (
                                <SolidGift size={size} color="teal40" />
                            )}
                            text={
                                <FormattedMessage
                                    id="breaward.in_porgress.info_popup.bullet_point_3"
                                    defaultMessage="After claiming your reward it will be sent to your Zeal account."
                                />
                            }
                            rightIcon={null}
                        />
                    </Group>
                    <Text color="gray40" weight="regular" variant="footnote">
                        <FormattedMessage
                            id="breaard.in_porgress.info_popup.footnote"
                            defaultMessage="By using your Zeal and Gnosis Pay card you agree to the terms and conditions of this rewards campaign."
                        />
                    </Text>
                </Column>
            </Column>
            <Actions variant="default">
                <Button
                    size="regular"
                    variant="primary"
                    disabled
                    rightIcon={({ color, size }) => (
                        <BoldLock color={color} size={size} />
                    )}
                >
                    <FormattedMessage
                        id="breaard.in_porgress.info_popup.cta"
                        defaultMessage="Spend to earn {earn}"
                        values={{
                            earn: (
                                <FormattedBRewardInFiat
                                    installationCampaign={installationCampaign}
                                />
                            ),
                        }}
                    />
                </Button>
            </Actions>
        </Screen>
    )
}
