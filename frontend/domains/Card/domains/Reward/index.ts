import * as Web3 from '@zeal/toolkit/Web3'

import { CryptoMoney } from '@zeal/domains/Money'

export type BReward =
    | {
          type: 'not_eligible'
      }
    | InProgressBReward
    | ReadyToClaimBReward
    | ExpiredBReward
    | ClaimedBReward

export type InProgressBReward = {
    type: 'in_progress'
    firstCardActivatedTimestampMS: number
    spent: CryptoMoney
}

export type ReadyToClaimBReward = {
    type: 'ready_to_claim'
    firstCardActivatedTimestampMS: number
    spent: CryptoMoney
}

export type ClaimedBReward = { type: 'claimed' }

export type ExpiredBReward = {
    type: 'expired'
    dismissed: boolean
    firstCardActivatedTimestampMS: number
    spent: CryptoMoney
}

export type BRewardClaimParams = {
    gnosisToken: string
    ownerAddress: Web3.address.Address
    firstCardActivatedTimestampMS: number
    installationCampaign: string | null
    referralCode: string | null
}

export type UserAConfiguredReferralConfig = {
    type: 'configured'
    ownerAddress: Web3.address.Address
    referralCode: string
    shareableLink: string
}

declare const UserAReferralConfigSymbol: unique symbol
declare const UserBReferralConfigSymbol: unique symbol

export type UserAReferralConfig = (
    | { type: 'not_configured' }
    | UserAConfiguredReferralConfig
) & {
    __userAReferralConfig: typeof UserAReferralConfigSymbol
}

export type UserBReferralConfig = (
    | { type: 'not_configured' }
    | { type: 'configured'; referralCode: string }
) & {
    __userBReferralConfig: typeof UserBReferralConfigSymbol
}

export type ReferralConfig = {
    userA: UserAReferralConfig
    userB: UserBReferralConfig
}

// TODO @resetko-zeal think if this is a correct place for it
export type Campaign = 'dappcon25'
