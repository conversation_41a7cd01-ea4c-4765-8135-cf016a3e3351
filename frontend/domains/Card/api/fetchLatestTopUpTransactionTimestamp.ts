import { notReachable } from '@zeal/toolkit'
import * as Web3 from '@zeal/toolkit/Web3'

import {
    CARD_NETWORK,
    ON_CHAIN_BALANCE_ESTIMATED_PENDING_TIME_MS,
} from '@zeal/domains/Card/constants'
import { CryptoCurrency } from '@zeal/domains/Currency'
import { fetchIndexedTransactions } from '@zeal/domains/Transactions/api/fetchIndexedTransactions'

export const fetchLatestTopUpTransactionTimestamp = async ({
    cardSafeAddress,
    cardCurrency,
    signal,
}: {
    cardSafeAddress: Web3.address.Address
    cardCurrency: CryptoCurrency
    signal?: AbortSignal
}): Promise<number | null> => {
    const transactions = await fetchIndexedTransactions({
        address: cardSafeAddress,
        networkHexId: CARD_NETWORK.hexChainId,
        beforeTimestampMs: null,
        afterTimestampMs:
            Date.now() - ON_CHAIN_BALANCE_ESTIMATED_PENDING_TIME_MS,
        logsForAddresses: null,
        signal,
    })

    const topUpTransactions = transactions
        .filter((transaction) =>
            transaction.logs.some((log) => {
                switch (log.type) {
                    case 'unknown':
                    case 'added_owner':
                    case 'approval':
                    case 'account_deployed':
                    case 'threshold_updated':
                    case 'set_allowance':
                    case 'enable_module':
                    case 'disable_module':
                    case 'safe_module_transaction':
                    case 'safe_module_transaction_for_native_fee_payment':
                    case 'safe_received':
                    case 'user_operation_event':
                    case 'user_operation_revert_reason':
                    case 'native_wrapper_withdraw':
                    case 'native_wrapper_deposit':
                        return false
                    case 'erc20_transfer':
                        return (
                            log.to === cardSafeAddress &&
                            log.currencyId === cardCurrency.id
                        )
                    /* istanbul ignore next */
                    default:
                        return notReachable(log)
                }
            })
        )
        .toSorted((a, b) => b.timestamp - a.timestamp)

    return topUpTransactions[0]?.timestamp || null
}
