import { notReachable } from '@zeal/toolkit'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { failure, groupByType, Result, success } from '@zeal/toolkit/Result'
import * as Web3 from '@zeal/toolkit/Web3'

import { CARD_NETWORK } from '@zeal/domains/Card/constants'
import { CryptoCurrency } from '@zeal/domains/Currency'
import { CryptoMoney } from '@zeal/domains/Money'
import { ERC20TransferLog } from '@zeal/domains/RPCRequest'
import { IndexedTransaction } from '@zeal/domains/Transactions'
import { fetchIndexedTransactions } from '@zeal/domains/Transactions/api/fetchIndexedTransactions'

type TopUpTransaction = {
    hash: Hexadecimal.Hexadecimal
    timestamp: number
    amount: CryptoMoney
}

export const fetchTopUpTransactions = async ({
    cardSafeAddress,
    cardCurrency,
    afterTimestampMs,
    beforeTimestampMs,
    signal,
}: {
    cardSafeAddress: Web3.address.Address
    cardCurrency: CryptoCurrency
    beforeTimestampMs: number | null
    afterTimestampMs: number | null
    signal?: AbortSignal
}): Promise<TopUpTransaction[]> => {
    const transactions = await fetchIndexedTransactions({
        address: cardSafeAddress,
        networkHexId: CARD_NETWORK.hexChainId,
        afterTimestampMs,
        beforeTimestampMs,
        logsForAddresses: null,
        signal,
    })

    const [_, topUpTransactions] = groupByType(
        transactions.map((tx) =>
            parseTopUpTransaction({
                indexedTransaction: tx,
                cardSafeAddress,
                cardCurrency,
            })
        )
    )

    return topUpTransactions
}

const parseTopUpTransaction = ({
    indexedTransaction,
    cardCurrency,
    cardSafeAddress,
}: {
    indexedTransaction: IndexedTransaction
    cardCurrency: CryptoCurrency
    cardSafeAddress: Web3.address.Address
}): Result<unknown, TopUpTransaction> => {
    const transferLog = indexedTransaction.logs.find(
        (log): log is ERC20TransferLog => {
            switch (log.type) {
                case 'unknown':
                case 'added_owner':
                case 'approval':
                case 'account_deployed':
                case 'threshold_updated':
                case 'set_allowance':
                case 'enable_module':
                case 'disable_module':
                case 'safe_module_transaction_for_native_fee_payment':
                case 'safe_module_transaction':
                case 'safe_received':
                case 'user_operation_event':
                case 'user_operation_revert_reason':
                case 'native_wrapper_deposit':
                case 'native_wrapper_withdraw':
                    return false
                case 'erc20_transfer':
                    return (
                        log.to === cardSafeAddress &&
                        log.currencyId === cardCurrency.id
                    )
                default:
                    return notReachable(log)
            }
        }
    )

    if (!transferLog) {
        return failure({ type: 'no_card_top_up_log_found' })
    }

    return success({
        hash: indexedTransaction.hash,
        timestamp: indexedTransaction.timestamp,
        amount: {
            currency: cardCurrency,
            amount: transferLog.amount,
        },
    })
}
