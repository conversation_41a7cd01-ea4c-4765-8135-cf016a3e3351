import { post } from '@zeal/api/gnosisApi'

import * as Web3 from '@zeal/toolkit/Web3'

import {
    CardSlientSignKeyStore,
    CardTermsType,
    GnosisTermsType,
} from '@zeal/domains/Card'
import { loginWithCache } from '@zeal/domains/Card/api/login'

const TERMS_TYPE_MAP: Record<CardTermsType, GnosisTermsType> = {
    general_terms: 'general-tos',
    monavate_terms: 'card-monavate-tos',
    cashback_terms: 'cashback-tos',
    privacy_policy: 'privacy-policy',
}

export const acceptTermsAndConditions = async ({
    termsToAccept,
    sessionPassword,
    keyStore,
    readonlySignerAddress,
    signal,
}: {
    readonlySignerAddress: Web3.address.Address
    sessionPassword: string
    keyStore: CardSlientSignKeyStore
    signal?: AbortSignal
    termsToAccept: { type: CardTermsType; version: string }
}): Promise<void> => {
    const gnosisPayLoginInfo = await loginWithCache({
        keyStore,
        signal,
        sessionPassword,
        readonlyS<PERSON>erAddress,
    })

    await post(
        '/user/terms',
        {
            auth: { type: 'bearer_token', token: gnosisPayLoginInfo.token },
            query: undefined,
            body: {
                terms: TERMS_TYPE_MAP[termsToAccept.type],
                version: termsToAccept.version,
            },
        },
        signal
    )
}
