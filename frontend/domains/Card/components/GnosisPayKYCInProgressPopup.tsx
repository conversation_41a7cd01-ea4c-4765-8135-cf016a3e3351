import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Button } from '@zeal/uikit/Button'
import { Header } from '@zeal/uikit/Header'
import { Clock } from '@zeal/uikit/Icon/Clock'
import { CloseCross } from '@zeal/uikit/Icon/CloseCross'
import { IconButton } from '@zeal/uikit/IconButton'
import { Popup } from '@zeal/uikit/Popup'

type Msg = {
    type: 'close'
}

type Props = {
    onMsg: (msg: Msg) => void
}

export const GnosisPayKycInProgressPopup = ({ onMsg }: Props) => (
    <Popup.Layout onMsg={onMsg} background="surfaceDefault">
        <ActionBar
            right={
                <IconButton
                    variant="on_light"
                    onClick={() => onMsg({ type: 'close' })}
                >
                    {({ color }) => <CloseCross size={24} color={color} />}
                </IconButton>
            }
        />
        <Header
            icon={({ size }) => <Clock size={size} color="blue30" />}
            title={
                <FormattedMessage
                    id="gnosiskyc.modal.in-progress.title"
                    defaultMessage="ID verification can take 24 hrs or longer. Please be patient"
                />
            }
        />

        <Popup.Actions>
            <Button
                variant="secondary"
                onClick={() => onMsg({ type: 'close' })}
                size="regular"
            >
                <FormattedMessage id="action.close" defaultMessage="Close" />
            </Button>
        </Popup.Actions>
    </Popup.Layout>
)
