import { FormattedMessage } from 'react-intl'

import { BannerSolid } from '@zeal/uikit/BannerSolid'

export const DelayQueueBusyBanner = () => {
    return (
        <BannerSolid
            title={
                <FormattedMessage
                    id="delayQueueBusyBanner.processing-yout-action.title"
                    defaultMessage="Processing your action, please wait"
                />
            }
            subtitle={
                <FormattedMessage
                    id="delayQueueBusyBanner.processing-yout-action.subtitle"
                    defaultMessage="You won’t be able to do this action for 3 minutes. For security reasons, any card settings changes or withdrawals take 3 minutes to be processed."
                />
            }
            rounded
            variant="neutral"
        />
    )
}
