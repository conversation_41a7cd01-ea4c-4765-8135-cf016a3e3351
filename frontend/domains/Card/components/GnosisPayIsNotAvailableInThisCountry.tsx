import { FormattedMessage } from 'react-intl'

import { Avatar } from '@zeal/uikit/Avatar'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { Header } from '@zeal/uikit/Header'
import { BoldDangerTriangle } from '@zeal/uikit/Icon/BoldDangerTriangle'
import { Popup } from '@zeal/uikit/Popup'

import { notReachable } from '@zeal/toolkit'

import { GnosisPayIsNotAvailableInThisCountry as E } from '@zeal/domains/Error/domains/GnosisPay'

type Props = {
    error: E
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'on_gnosis_pay_not_available_accepted' }

export const GnosisPayIsNotAvailableInThisCountry = ({
    onMsg,
    error: _,
}: Props) => {
    return (
        <Popup.Layout
            onMsg={(msg) => {
                switch (msg.type) {
                    case 'close':
                        onMsg({ type: 'on_gnosis_pay_not_available_accepted' })
                        break

                    /* istanbul ignore next */
                    default:
                        notReachable(msg.type)
                }
            }}
            background="surfaceDefault"
        >
            <Column spacing={24}>
                <Header
                    icon={({ size }) => (
                        <Avatar
                            size={72}
                            variant="round"
                            backgroundColor="backgroundLight"
                        >
                            <BoldDangerTriangle
                                size={size}
                                color="iconStatusWarning"
                            />
                        </Avatar>
                    )}
                    title={
                        <FormattedMessage
                            id="gnosisPayIsNotAvailableInThisCountry.title"
                            defaultMessage="Gnosis Pay is not yet available in your country"
                        />
                    }
                />
                <Popup.Actions>
                    <Button
                        variant="primary"
                        onClick={() =>
                            onMsg({
                                type: 'on_gnosis_pay_not_available_accepted',
                            })
                        }
                        size="regular"
                    >
                        <FormattedMessage
                            id="action.understood"
                            defaultMessage="I understand"
                        />
                    </Button>
                </Popup.Actions>
            </Column>
        </Popup.Layout>
    )
}
