import { FormattedMessage } from 'react-intl'

import { notReachable } from '@zeal/toolkit'

import { MerchantCategory } from '../MerchantCategory'

type Props = {
    category: MerchantCategory
}

export const MerchantCategoryLabel = ({ category }: Props) => {
    switch (category) {
        case 'cars':
            return (
                <FormattedMessage
                    id="merchantCategory.cars"
                    defaultMessage="Cars"
                />
            )

        case 'carWash':
            return (
                <FormattedMessage
                    id="merchantCategory.carWash"
                    defaultMessage="Car wash"
                />
            )

        case 'tollRoad':
            return (
                <FormattedMessage
                    id="merchantCategory.tollRoad"
                    defaultMessage="Toll road"
                />
            )
        case 'trafficFine':
            return (
                <FormattedMessage
                    id="merchantCategory.trafficFine"
                    defaultMessage="Traffic fine"
                />
            )
        case 'parking':
            return (
                <FormattedMessage
                    id="merchantCategory.parking"
                    defaultMessage="Parking"
                />
            )
        case 'fuel':
            return (
                <FormattedMessage
                    id="merchantCategory.fuel"
                    defaultMessage="Fuel"
                />
            )
        case 'autoRepair':
            return (
                <FormattedMessage
                    id="merchantCategory.autoRepair"
                    defaultMessage="Auto repair"
                />
            )
        case 'charity':
            return (
                <FormattedMessage
                    id="merchantCategory.charity"
                    defaultMessage="Charity"
                />
            )
        case 'financialInstitutions':
            return (
                <FormattedMessage
                    id="merchantCategory.financialInstitutions"
                    defaultMessage="Financial institutions"
                />
            )
        case 'alcohol':
            return (
                <FormattedMessage
                    id="merchantCategory.alcohol"
                    defaultMessage="Alcohol"
                />
            )
        case 'cigarette':
            return (
                <FormattedMessage
                    id="merchantCategory.cigarette"
                    defaultMessage="Cigarette"
                />
            )
        case 'casino':
            return (
                <FormattedMessage
                    id="merchantCategory.casino"
                    defaultMessage="Casino"
                />
            )
        case 'kids':
            return (
                <FormattedMessage
                    id="merchantCategory.kids"
                    defaultMessage="Kids"
                />
            )
        case 'toys':
            return (
                <FormattedMessage
                    id="merchantCategory.toys"
                    defaultMessage="Toys"
                />
            )
        case 'furniture':
            return (
                <FormattedMessage
                    id="merchantCategory.furniture"
                    defaultMessage="Furniture"
                />
            )
        case 'construction':
            return (
                <FormattedMessage
                    id="merchantCategory.construction"
                    defaultMessage="Construction"
                />
            )
        case 'laundry':
            return (
                <FormattedMessage
                    id="merchantCategory.laundry"
                    defaultMessage="Laundry"
                />
            )
        case 'stationery':
            return (
                <FormattedMessage
                    id="merchantCategory.stationery"
                    defaultMessage="Stationery"
                />
            )
        case 'shoeRepair':
            return (
                <FormattedMessage
                    id="merchantCategory.shoeRepair"
                    defaultMessage="Shoe repair"
                />
            )
        case 'cleaning':
            return (
                <FormattedMessage
                    id="merchantCategory.cleaning"
                    defaultMessage="Cleaning"
                />
            )
        case 'appliances':
            return (
                <FormattedMessage
                    id="merchantCategory.appliances"
                    defaultMessage="Appliances"
                />
            )
        case 'pets':
            return (
                <FormattedMessage
                    id="merchantCategory.pets"
                    defaultMessage="Pets"
                />
            )
        case 'games':
            return (
                <FormattedMessage
                    id="merchantCategory.games"
                    defaultMessage="Games"
                />
            )
        case 'cinema':
            return (
                <FormattedMessage
                    id="merchantCategory.cinema"
                    defaultMessage="Cinema"
                />
            )
        case 'magazines':
            return (
                <FormattedMessage
                    id="merchantCategory.magazines"
                    defaultMessage="Magazines"
                />
            )
        case 'musicalInstruments':
            return (
                <FormattedMessage
                    id="merchantCategory.musicalInstruments"
                    defaultMessage="Musical instruments"
                />
            )
        case 'media':
            return (
                <FormattedMessage
                    id="merchantCategory.media"
                    defaultMessage="Media"
                />
            )
        case 'hotel':
            return (
                <FormattedMessage
                    id="merchantCategory.hotel"
                    defaultMessage="Hotel"
                />
            )
        case 'travelAgency':
            return (
                <FormattedMessage
                    id="merchantCategory.travelAgency"
                    defaultMessage="Travel agency"
                />
            )
        case 'billiard':
            return (
                <FormattedMessage
                    id="merchantCategory.billiard"
                    defaultMessage="Billiard"
                />
            )
        case 'bowling':
            return (
                <FormattedMessage
                    id="merchantCategory.bowling"
                    defaultMessage="Bowling"
                />
            )
        case 'sport':
            return (
                <FormattedMessage
                    id="merchantCategory.sport"
                    defaultMessage="Sport"
                />
            )
        case 'luxuries':
            return (
                <FormattedMessage
                    id="merchantCategory.luxuries"
                    defaultMessage="Luxuries"
                />
            )
        case 'fitness':
            return (
                <FormattedMessage
                    id="merchantCategory.fitness"
                    defaultMessage="Fitness"
                />
            )
        case 'housing':
            return (
                <FormattedMessage
                    id="merchantCategory.housing"
                    defaultMessage="Housing"
                />
            )
        case 'gas':
            return (
                <FormattedMessage
                    id="merchantCategory.gas"
                    defaultMessage="Gas"
                />
            )
        case 'electricity':
            return (
                <FormattedMessage
                    id="merchantCategory.electricity"
                    defaultMessage="Electricity"
                />
            )
        case 'medicine':
            return (
                <FormattedMessage
                    id="merchantCategory.medicine"
                    defaultMessage="Medicine"
                />
            )
        case 'drugs':
            return (
                <FormattedMessage
                    id="merchantCategory.drugs"
                    defaultMessage="Drugs"
                />
            )
        case 'dentist':
            return (
                <FormattedMessage
                    id="merchantCategory.dentist"
                    defaultMessage="Dentist"
                />
            )
        case 'optics':
            return (
                <FormattedMessage
                    id="merchantCategory.optics"
                    defaultMessage="Optics"
                />
            )
        case 'taxes':
            return (
                <FormattedMessage
                    id="merchantCategory.taxes"
                    defaultMessage="Taxes"
                />
            )
        case 'government':
            return (
                <FormattedMessage
                    id="merchantCategory.government"
                    defaultMessage="Government"
                />
            )
        case 'books':
            return (
                <FormattedMessage
                    id="merchantCategory.books"
                    defaultMessage="Books"
                />
            )
        case 'education':
            return (
                <FormattedMessage
                    id="merchantCategory.education"
                    defaultMessage="Education"
                />
            )
        case 'clothes':
            return (
                <FormattedMessage
                    id="merchantCategory.clothes"
                    defaultMessage="Clothes"
                />
            )

        case 'food':
            return (
                <FormattedMessage
                    id="merchantCategory.food"
                    defaultMessage="Food"
                />
            )
        case 'purchases':
            return (
                <FormattedMessage
                    id="merchantCategory.purchases"
                    defaultMessage="Purchases"
                />
            )
        case 'gifts':
            return (
                <FormattedMessage
                    id="merchantCategory.gifts"
                    defaultMessage="Gifts"
                />
            )
        case 'flowers':
            return (
                <FormattedMessage
                    id="merchantCategory.flowers"
                    defaultMessage="Flowers"
                />
            )
        case 'flights':
            return (
                <FormattedMessage
                    id="merchantCategory.flights"
                    defaultMessage="Flights"
                />
            )
        case 'train':
            return (
                <FormattedMessage
                    id="merchantCategory.train"
                    defaultMessage="Train"
                />
            )
        case 'taxi':
            return (
                <FormattedMessage
                    id="merchantCategory.taxi"
                    defaultMessage="Taxi"
                />
            )
        case 'publicTransport':
            return (
                <FormattedMessage
                    id="merchantCategory.publicTransport"
                    defaultMessage="Public transport"
                />
            )
        case 'delivery':
            return (
                <FormattedMessage
                    id="merchantCategory.delivery"
                    defaultMessage="Delivery"
                />
            )
        case 'waterTransport':
            return (
                <FormattedMessage
                    id="merchantCategory.waterTransport"
                    defaultMessage="Water transport"
                />
            )
        case 'sportingGoods':
            return (
                <FormattedMessage
                    id="merchantCategory.sportingGoods"
                    defaultMessage="Sporting goods"
                />
            )
        case 'cosmetics':
            return (
                <FormattedMessage
                    id="merchantCategory.cosmetics"
                    defaultMessage="Cosmetics"
                />
            )
        case 'telephony':
            return (
                <FormattedMessage
                    id="merchantCategory.telephony"
                    defaultMessage="Telephony"
                />
            )
        case 'internet':
            return (
                <FormattedMessage
                    id="merchantCategory.internet"
                    defaultMessage="Internet"
                />
            )
        case 'cellular':
            return (
                <FormattedMessage
                    id="merchantCategory.cellular"
                    defaultMessage="Cellular"
                />
            )
        case 'tv':
            return (
                <FormattedMessage
                    id="merchantCategory.tv"
                    defaultMessage="TV"
                />
            )
        case 'insurance':
            return (
                <FormattedMessage
                    id="merchantCategory.insurance"
                    defaultMessage="Insurance"
                />
            )
        case 'other':
            return (
                <FormattedMessage
                    id="merchantCategory.other"
                    defaultMessage="Other"
                />
            )
        case 'agriculture':
            return (
                <FormattedMessage
                    id="merchantCategory.agriculture"
                    defaultMessage="Agriculture"
                />
            )
        case 'antiques':
            return (
                <FormattedMessage
                    id="merchantCategory.antiques"
                    defaultMessage="Antiques"
                />
            )
        case 'artGalleries':
            return (
                <FormattedMessage
                    id="merchantCategory.artGalleries"
                    defaultMessage="Art Galleries"
                />
            )
        case 'autoRepairService':
            return (
                <FormattedMessage
                    id="merchantCategory.autoRepairService"
                    defaultMessage="Auto Repair Service"
                />
            )
        case 'beautyFitnessSpas':
            return (
                <FormattedMessage
                    id="merchantCategory.beautyFitnessSpas"
                    defaultMessage="Beauty, Fitness & Spas"
                />
            )
        case 'beautyPersonalCare':
            return (
                <FormattedMessage
                    id="merchantCategory.beautyPersonalCare"
                    defaultMessage="Beauty & Personal Care"
                />
            )
        case 'businessProfessionalServices':
            return (
                <FormattedMessage
                    id="merchantCategory.businessProfessionalServices"
                    defaultMessage="Business & Professional Services"
                />
            )
        case 'carRental':
            return (
                <FormattedMessage
                    id="merchantCategory.carRental"
                    defaultMessage="Car Rental"
                />
            )
        case 'casinoGambling':
            return (
                <FormattedMessage
                    id="merchantCategory.casinoGambling"
                    defaultMessage="Casino & Gambling"
                />
            )
        case 'childcare':
            return (
                <FormattedMessage
                    id="merchantCategory.childcare"
                    defaultMessage="Childcare"
                />
            )
        case 'cinemaEvents':
            return (
                <FormattedMessage
                    id="merchantCategory.cinemaEvents"
                    defaultMessage="Cinema & Events"
                />
            )
        case 'cleaningMaintenance':
            return (
                <FormattedMessage
                    id="merchantCategory.cleaningMaintenance"
                    defaultMessage="Cleaning & Maintenance"
                />
            )
        case 'clothingServices':
            return (
                <FormattedMessage
                    id="merchantCategory.clothingServices"
                    defaultMessage="Clothing Services"
                />
            )
        case 'communicationServices':
            return (
                <FormattedMessage
                    id="merchantCategory.communicationServices"
                    defaultMessage="Communication Services"
                />
            )
        case 'craftsArtSupplies':
            return (
                <FormattedMessage
                    id="merchantCategory.craftsArtSupplies"
                    defaultMessage="Crafts & Art Supplies"
                />
            )
        case 'datingServices':
            return (
                <FormattedMessage
                    id="merchantCategory.datingServices"
                    defaultMessage="Dating Services"
                />
            )
        case 'departmentStores':
            return (
                <FormattedMessage
                    id="merchantCategory.departmentStores"
                    defaultMessage="Department Stores"
                />
            )
        case 'directMarketingSubscription':
            return (
                <FormattedMessage
                    id="merchantCategory.directMarketingSubscription"
                    defaultMessage="Direct Marketing & Subscription"
                />
            )
        case 'discountStores':
            return (
                <FormattedMessage
                    id="merchantCategory.discountStores"
                    defaultMessage="Discount Stores"
                />
            )
        case 'dutyFree':
            return (
                <FormattedMessage
                    id="merchantCategory.dutyFree"
                    defaultMessage="Duty Free"
                />
            )
        case 'electronics':
            return (
                <FormattedMessage
                    id="merchantCategory.electronics"
                    defaultMessage="Electronics"
                />
            )
        case 'emergencyServices':
            return (
                <FormattedMessage
                    id="merchantCategory.emergencyServices"
                    defaultMessage="Emergency Services"
                />
            )
        case 'equipmentRental':
            return (
                <FormattedMessage
                    id="merchantCategory.equipmentRental"
                    defaultMessage="Equipment Rental"
                />
            )
        case 'evCharging':
            return (
                <FormattedMessage
                    id="merchantCategory.evCharging"
                    defaultMessage="EV Charging"
                />
            )
        case 'financialProfessionalServices':
            return (
                <FormattedMessage
                    id="merchantCategory.financialProfessionalServices"
                    defaultMessage="Financial & Professional Services"
                />
            )
        case 'finesPenalties':
            return (
                <FormattedMessage
                    id="merchantCategory.finesPenalties"
                    defaultMessage="Fines & Penalties"
                />
            )
        case 'flowersGarden':
            return (
                <FormattedMessage
                    id="merchantCategory.flowersGarden"
                    defaultMessage="Flowers & Garden"
                />
            )
        case 'freight':
            return (
                <FormattedMessage
                    id="merchantCategory.freight"
                    defaultMessage="Freight"
                />
            )
        case 'funeralServices':
            return (
                <FormattedMessage
                    id="merchantCategory.funeralServices"
                    defaultMessage="Funeral Services"
                />
            )
        case 'generalMerchandiseRetail':
            return (
                <FormattedMessage
                    id="merchantCategory.generalMerchandiseRetail"
                    defaultMessage="General Merchandise & Retail"
                />
            )
        case 'governmentServices':
            return (
                <FormattedMessage
                    id="merchantCategory.governmentServices"
                    defaultMessage="Government Services"
                />
            )
        case 'hardware':
            return (
                <FormattedMessage
                    id="merchantCategory.hardware"
                    defaultMessage="Hardware"
                />
            )
        case 'healthMedicine':
            return (
                <FormattedMessage
                    id="merchantCategory.healthMedicine"
                    defaultMessage="Health & Medicine"
                />
            )
        case 'homeImprovement':
            return (
                <FormattedMessage
                    id="merchantCategory.homeImprovement"
                    defaultMessage="Home Improvement"
                />
            )
        case 'homeServices':
            return (
                <FormattedMessage
                    id="merchantCategory.homeServices"
                    defaultMessage="Home Services"
                />
            )
        case 'laundryCleaningServices':
            return (
                <FormattedMessage
                    id="merchantCategory.laundryCleaningServices"
                    defaultMessage="Laundry & Cleaning Services"
                />
            )
        case 'legalGovernmentFees':
            return (
                <FormattedMessage
                    id="merchantCategory.legalGovernmentFees"
                    defaultMessage="Legal & Government Fees"
                />
            )
        case 'luxuriesCollectibles':
            return (
                <FormattedMessage
                    id="merchantCategory.luxuriesCollectibles"
                    defaultMessage="Luxuries & Collectibles"
                />
            )
        case 'magazinesNews':
            return (
                <FormattedMessage
                    id="merchantCategory.magazinesNews"
                    defaultMessage="Magazines & News"
                />
            )
        case 'marketplaces':
            return (
                <FormattedMessage
                    id="merchantCategory.marketplaces"
                    defaultMessage="Marketplaces"
                />
            )
        case 'mobileHomes':
            return (
                <FormattedMessage
                    id="merchantCategory.mobileHomes"
                    defaultMessage="Mobile Homes"
                />
            )
        case 'moneyTransferCrypto':
            return (
                <FormattedMessage
                    id="merchantCategory.moneyTransferCrypto"
                    defaultMessage="Money Transfer & Crypto"
                />
            )
        case 'musicRelated':
            return (
                <FormattedMessage
                    id="merchantCategory.musicRelated"
                    defaultMessage="Music Related"
                />
            )
        case 'organizationsClubs':
            return (
                <FormattedMessage
                    id="merchantCategory.organizationsClubs"
                    defaultMessage="Organisations & Clubs"
                />
            )
        case 'pawnShops':
            return (
                <FormattedMessage
                    id="merchantCategory.pawnShops"
                    defaultMessage="Pawn Shops"
                />
            )
        case 'photoServicesSupplies':
            return (
                <FormattedMessage
                    id="merchantCategory.photoServicesSupplies"
                    defaultMessage="Photo Services & Supplies"
                />
            )
        case 'postalServices':
            return (
                <FormattedMessage
                    id="merchantCategory.postalServices"
                    defaultMessage="Postal Services"
                />
            )
        case 'professionalServicesOther':
            return (
                <FormattedMessage
                    id="merchantCategory.professionalServicesOther"
                    defaultMessage="Professional Services (Other)"
                />
            )
        case 'purchasesMiscServices':
            return (
                <FormattedMessage
                    id="merchantCategory.purchasesMiscServices"
                    defaultMessage="Purchases & Misc Services"
                />
            )
        case 'recreationServices':
            return (
                <FormattedMessage
                    id="merchantCategory.recreationServices"
                    defaultMessage="Recreation Services"
                />
            )
        case 'religiousGoods':
            return (
                <FormattedMessage
                    id="merchantCategory.religiousGoods"
                    defaultMessage="Religious Goods"
                />
            )
        case 'secondhandRetail':
            return (
                <FormattedMessage
                    id="merchantCategory.secondhandRetail"
                    defaultMessage="Secondhand Retail"
                />
            )
        case 'shoeHatRepair':
            return (
                <FormattedMessage
                    id="merchantCategory.shoeHatRepair"
                    defaultMessage="Shoe & Hat Repair"
                />
            )
        case 'softwareApps':
            return (
                <FormattedMessage
                    id="merchantCategory.softwareApps"
                    defaultMessage="Software & Apps"
                />
            )
        case 'specializedRepairs':
            return (
                <FormattedMessage
                    id="merchantCategory.specializedRepairs"
                    defaultMessage="Specialised Repairs"
                />
            )
        case 'sportingGoodsRecreation':
            return (
                <FormattedMessage
                    id="merchantCategory.sportingGoodsRecreation"
                    defaultMessage="Sporting Goods & Recreation"
                />
            )
        case 'sportsClubsFields':
            return (
                <FormattedMessage
                    id="merchantCategory.sportsClubsFields"
                    defaultMessage="Sports Clubs & Fields"
                />
            )
        case 'stationaryPrinting':
            return (
                <FormattedMessage
                    id="merchantCategory.stationaryPrinting"
                    defaultMessage="Stationery & Printing"
                />
            )
        case 'storage':
            return (
                <FormattedMessage
                    id="merchantCategory.storage"
                    defaultMessage="Storage"
                />
            )
        case 'telecomEquipment':
            return (
                <FormattedMessage
                    id="merchantCategory.telecomEquipment"
                    defaultMessage="Telecom Equipment"
                />
            )
        case 'tobacco':
            return (
                <FormattedMessage
                    id="merchantCategory.tobacco"
                    defaultMessage="Tobacco"
                />
            )
        case 'tourismAttractionsAmusement':
            return (
                <FormattedMessage
                    id="merchantCategory.tourismAttractionsAmusement"
                    defaultMessage="Tourism, Attractions & Amusement"
                />
            )
        case 'towing':
            return (
                <FormattedMessage
                    id="merchantCategory.towing"
                    defaultMessage="Towing"
                />
            )
        case 'toysHobbies':
            return (
                <FormattedMessage
                    id="merchantCategory.toysHobbies"
                    defaultMessage="Toys & Hobbies"
                />
            )
        case 'tvRadioStreaming':
            return (
                <FormattedMessage
                    id="merchantCategory.tvRadioStreaming"
                    defaultMessage="TV, Radio & Streaming"
                />
            )
        case 'utilities':
            return (
                <FormattedMessage
                    id="merchantCategory.utilities"
                    defaultMessage="Utilities"
                />
            )
        case 'wholesaleClubs':
            return (
                <FormattedMessage
                    id="merchantCategory.wholesaleClubs"
                    defaultMessage="Wholesale Clubs"
                />
            )
        default:
            return notReachable(category)
    }
}
