import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { Address } from '@zeal/toolkit/Web3/address'

import { AccountsMap } from '@zeal/domains/Account'
import { CardConfig } from '@zeal/domains/Card'
import { EnableCardToUseMonerium } from '@zeal/domains/Card/domains/MoneriumBankTransfer/components/EnableCardToUseMonerium'
import { CountryISOCode } from '@zeal/domains/Country'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { DepositWithdrawVariant } from '@zeal/domains/Currency/domains/BankTransfer'
import { BANK_TRANSFER_LOGIN_NETWORK } from '@zeal/domains/Currency/domains/BankTransfer/constants'
import { DepositWithdraw as UnblockDepositWithdraw } from '@zeal/domains/Currency/domains/BankTransfer/features/DepositWithdraw'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import {
    BankTransferUnblockUserCreated,
    CustomCurrencyMap,
    DefaultCurrencyConfig,
} from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { MoneriumCountryEligibilityChecker } from './MoneriumCountryEligibilityChecker'

import { SelectResidency } from '../SelectResidency'

type Props = {
    bankTransferInfo: BankTransferUnblockUserCreated
    networkMap: NetworkMap
    installationId: string

    selectedAddress: Address
    accountsMap: AccountsMap
    keystoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    sessionPassword: string
    customCurrencies: CustomCurrencyMap
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    feePresetMap: FeePresetMap
    networkRPCMap: NetworkRPCMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    cardConfig: CardConfig
    variant: DepositWithdrawVariant
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<MsgOf<typeof SelectResidency>, { type: 'close' }>
    | MsgOf<typeof MoneriumCountryEligibilityChecker>
    | Extract<
          MsgOf<typeof EnableCardToUseMonerium>,
          { type: 'monerium_deposit_on_enable_card_clicked' }
      >
    | Extract<
          MsgOf<typeof UnblockDepositWithdraw>,
          {
              type:
                  | 'on_account_create_request'
                  | 'import_keys_button_clicked'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_latest_bank_transfer_owner_found'
                  | 'on_import_latest_bank_transfer_owner_clicked'
                  | 'on_user_login_to_unblock_success'
                  | 'kyc_applicant_created'
                  | 'bank_transfer_owner_successfully_changed'
                  | 'on_withdrawal_monitor_fiat_transaction_start'
                  | 'on_withdrawal_monitor_fiat_transaction_success'
                  | 'on_contact_support_clicked'
                  | 'on_on_ramp_transfer_success_close_click'
          }
      >

type State =
    | { type: 'choose_residency' }
    | {
          type: 'monerium_country_eligibility_checker'
          userCountryCode: CountryISOCode
      }
    | { type: 'enable_card' }
    | { type: 'unblock_bank_transfers'; initialCurrency: CryptoCurrency | null }

export const NoCardHasUnblock = ({
    bankTransferInfo,
    selectedAddress,
    customCurrencies,
    sessionPassword,
    portfolioMap,
    accountsMap,
    keystoreMap,
    networkMap,
    networkRPCMap,
    feePresetMap,
    installationId,
    cardConfig,
    currencyHiddenMap,
    currencyPinMap,
    variant,
    onMsg,
    gasCurrencyPresetMap,
    defaultCurrencyConfig,
}: Props) => {
    const [state, setState] = useState<State>(
        bankTransferInfo.countryCode
            ? {
                  type: 'monerium_country_eligibility_checker',
                  userCountryCode: bankTransferInfo.countryCode,
              }
            : { type: 'choose_residency' }
    )

    switch (state.type) {
        case 'choose_residency':
            return (
                <SelectResidency
                    installationId={installationId}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                onMsg(msg)
                                break
                            case 'on_supported_country_selected':
                                switch (msg.providerSupport.type) {
                                    case 'unblock_only':
                                        setState({
                                            type: 'unblock_bank_transfers',
                                            initialCurrency: null,
                                        })
                                        break
                                    case 'monerium_only':
                                        captureError({
                                            type: 'user_has_unblock_but_selected_monerium_supported_country_as_residency',
                                            info: 'should be imposible state',
                                        })
                                        setState({ type: 'enable_card' })
                                        break
                                    case 'monerium_and_unblock':
                                        setState({
                                            type: 'monerium_country_eligibility_checker',
                                            userCountryCode: msg.countryCode,
                                        })
                                        break
                                    /* istanbul ignore next */
                                    default:
                                        notReachable(msg.providerSupport)
                                }
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'monerium_country_eligibility_checker':
            return (
                <MoneriumCountryEligibilityChecker
                    userCountryCode={state.userCountryCode}
                    variant={variant}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    selectedAddress={selectedAddress}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    accountsMap={accountsMap}
                    bankTransferInfo={bankTransferInfo}
                    customCurrencies={customCurrencies}
                    cardConfig={cardConfig}
                    installationId={installationId}
                    keystoreMap={keystoreMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    feePresetMap={feePresetMap}
                    portfolioMap={portfolioMap}
                    sessionPassword={sessionPassword}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                if (bankTransferInfo.countryCode) {
                                    onMsg(msg)
                                } else {
                                    setState({ type: 'choose_residency' })
                                }
                                break
                            case 'monerium_deposit_on_enable_card_clicked':
                            case 'on_account_create_request':
                            case 'import_keys_button_clicked':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_import_latest_bank_transfer_owner_clicked':
                            case 'on_user_login_to_unblock_success':
                            case 'kyc_applicant_created':
                            case 'bank_transfer_owner_successfully_changed':
                            case 'on_withdrawal_monitor_fiat_transaction_start':
                            case 'on_withdrawal_monitor_fiat_transaction_success':
                            case 'on_contact_support_clicked':
                            case 'on_on_ramp_transfer_success_close_click':
                            case 'on_kyc_data_updated_close_clicked':
                                onMsg(msg)
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'enable_card':
            return (
                <EnableCardToUseMonerium
                    installationId={installationId}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                setState({ type: 'choose_residency' })
                                break
                            case 'monerium_deposit_on_enable_card_clicked':
                                onMsg(msg)
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'unblock_bank_transfers':
            return (
                <UnblockDepositWithdraw
                    variant={variant}
                    network={BANK_TRANSFER_LOGIN_NETWORK}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    initialCurrency={null}
                    selectedAddress={selectedAddress}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    accountsMap={accountsMap}
                    bankTransfer={bankTransferInfo}
                    customCurrencies={customCurrencies}
                    cardConfig={cardConfig}
                    installationId={installationId}
                    keystoreMap={keystoreMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    feePresetMap={feePresetMap}
                    portfolioMap={portfolioMap}
                    sessionPassword={sessionPassword}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                setState({ type: 'choose_residency' })
                                break
                            case 'on_account_create_request':
                            case 'import_keys_button_clicked':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_import_latest_bank_transfer_owner_clicked':
                            case 'on_user_login_to_unblock_success':
                            case 'kyc_applicant_created':
                            case 'bank_transfer_owner_successfully_changed':
                            case 'on_withdrawal_monitor_fiat_transaction_start':
                            case 'on_withdrawal_monitor_fiat_transaction_success':
                            case 'on_contact_support_clicked':
                            case 'on_on_ramp_transfer_success_close_click':
                            case 'on_kyc_data_updated_close_clicked':
                                onMsg(msg)
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
