import { StepW<PERSON>rd } from '@zeal/uikit/StepWizard'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { Account, AccountsMap } from '@zeal/domains/Account'
import { CardConfig, CardSlientSignKeyStore } from '@zeal/domains/Card'
import { tryToGetUserTimeZoneCountries } from '@zeal/domains/Country/helpers/tryToGetUserTimeZoneCountries'
import { CurrencyHiddenMap } from '@zeal/domains/Currency'
import { DepositWithdrawVariant } from '@zeal/domains/Currency/domains/BankTransfer'
import { MtPelerinBankTransfer } from '@zeal/domains/Currency/domains/BankTransfer/features/MtPelerinBankTransfer'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

import { SetupCardForBankTransfers } from './SetupCardForBankTransfers'

import { MtPelerinMoneriumSelectionScreen } from '../MtPelerinMoneriumSelectionScreen'

type Props = {
    installationId: string
    variant: DepositWithdrawVariant
    cardConfig: CardConfig
    selectedAddress: Web3.address.Address

    accountsMap: AccountsMap
    keystoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    currencyHiddenMap: CurrencyHiddenMap
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    sessionPassword: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    installationCampaign: string | null
    onMsg: (msg: Msg) => void
}

type State =
    | {
          type: 'mt_pelerin_monerium_selection_screen'
          account: Account
          keyStore: CardSlientSignKeyStore
      }
    | {
          type: 'setup_card_for_bank_transfer'
      }
    | {
          type: 'mt_pelerin_bank_transfer'
          account: Account
          keyStore: CardSlientSignKeyStore
      }

const calculateMtPelerinEligibility = ({
    defaultCurrencyConfig,
    accountsMap,
    keyStoreMap,
    selectedAddress,
}: {
    defaultCurrencyConfig: DefaultCurrencyConfig
    selectedAddress: Web3.address.Address
    keyStoreMap: KeyStoreMap
    accountsMap: AccountsMap
}):
    | {
          type: 'mt_pelerin_eligible'
          account: Account
          keyStore: CardSlientSignKeyStore
      }
    | { type: 'mt_pelerin_not_eligible' } => {
    const account = accountsMap[selectedAddress]
    if (!account) {
        return { type: 'mt_pelerin_not_eligible' }
    }
    const keyStore = getKeyStore({ address: selectedAddress, keyStoreMap })
    switch (keyStore.type) {
        case 'private_key_store':
        case 'secret_phrase_key':
        case 'safe_4337':
            const tzCountries =
                tryToGetUserTimeZoneCountries().getSuccessResult() || []

            return tzCountries.map((c) => c.code).includes('CH') ||
                defaultCurrencyConfig.defaultCurrency.code === 'CHF'
                ? { type: 'mt_pelerin_eligible', account, keyStore }
                : { type: 'mt_pelerin_not_eligible' }

        case 'ledger':
        case 'trezor':
        case 'track_only':
            return { type: 'mt_pelerin_not_eligible' }

        /* istanbul ignore next */
        default:
            return notReachable(keyStore)
    }
}

type Msg =
    | Extract<MsgOf<typeof MtPelerinMoneriumSelectionScreen>, { type: 'close' }>
    | MsgOf<typeof SetupCardForBankTransfers>
    | Extract<
          MsgOf<typeof MtPelerinBankTransfer>,
          {
              type:
                  | 'on_card_disconnected'
                  | 'on_create_smart_wallet_clicked'
                  | 'on_card_import_on_import_keys_clicked'
                  | 'on_card_imported_success_animation_complete'
                  | 'on_onboarded_card_imported_success_animation_complete'
          }
      >

export const ChooseProviderCurrency = ({
    defaultCurrencyConfig,
    variant,
    cardConfig,
    sessionPassword,
    portfolioMap,
    installationId,
    currencyHiddenMap,
    accountsMap,
    installationCampaign,
    keystoreMap,
    networkRPCMap,
    selectedAddress,
    networkMap,
    onMsg,
}: Props) => {
    const mtPelerinEligibility = calculateMtPelerinEligibility({
        defaultCurrencyConfig,
        selectedAddress,
        keyStoreMap: keystoreMap,
        accountsMap,
    })
    return (
        <StepWizard<State>
            initialStep={() => {
                switch (mtPelerinEligibility.type) {
                    case 'mt_pelerin_eligible':
                        return {
                            type: 'mt_pelerin_monerium_selection_screen',
                            account: mtPelerinEligibility.account,
                            keyStore: mtPelerinEligibility.keyStore,
                        }
                    case 'mt_pelerin_not_eligible':
                        return { type: 'setup_card_for_bank_transfer' }

                    /* istanbul ignore next */
                    default:
                        return notReachable(mtPelerinEligibility)
                }
            }}
        >
            {({ step, forwardTo, backTo }) => {
                switch (step.type) {
                    case 'mt_pelerin_monerium_selection_screen':
                        return (
                            <MtPelerinMoneriumSelectionScreen
                                cardConfig={cardConfig}
                                installationId={installationId}
                                variant={variant}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'close':
                                            onMsg(msg)
                                            break
                                        case 'on_provider_selected':
                                            {
                                                switch (msg.provider) {
                                                    case 'mt_pelerin':
                                                        forwardTo({
                                                            type: 'mt_pelerin_bank_transfer',
                                                            account:
                                                                step.account,
                                                            keyStore:
                                                                step.keyStore,
                                                        })
                                                        break
                                                    case 'monerium':
                                                        forwardTo({
                                                            type: 'setup_card_for_bank_transfer',
                                                        })
                                                        break

                                                    /* istanbul ignore next */
                                                    default:
                                                        notReachable(
                                                            msg.provider
                                                        )
                                                        break
                                                }
                                            }
                                            break

                                        /* istanbul ignore next */
                                        default:
                                            notReachable(msg)
                                    }
                                }}
                            />
                        )
                    case 'setup_card_for_bank_transfer':
                        return (
                            <SetupCardForBankTransfers
                                installationId={installationId}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'on_login_to_gnosis_pay_clicked':
                                        case 'on_create_gnosis_pay_account_clicked':
                                            onMsg(msg)
                                            break
                                        case 'close':
                                            {
                                                switch (
                                                    mtPelerinEligibility.type
                                                ) {
                                                    case 'mt_pelerin_eligible':
                                                        backTo({
                                                            type: 'mt_pelerin_monerium_selection_screen',
                                                            account:
                                                                mtPelerinEligibility.account,
                                                            keyStore:
                                                                mtPelerinEligibility.keyStore,
                                                        })
                                                        break
                                                    case 'mt_pelerin_not_eligible':
                                                        onMsg(msg)
                                                        break

                                                    default:
                                                        notReachable(
                                                            mtPelerinEligibility
                                                        )
                                                        break
                                                }
                                            }

                                            break

                                        /* istanbul ignore next */
                                        default:
                                            notReachable(msg)
                                    }
                                }}
                            />
                        )
                    case 'mt_pelerin_bank_transfer':
                        return (
                            <MtPelerinBankTransfer
                                variant={variant}
                                keyStore={step.keyStore}
                                selectedAddress={step.account.address}
                                sessionPassword={sessionPassword}
                                onMsg={(msg) => {
                                    switch (msg.type) {
                                        case 'close':
                                            backTo({
                                                type: 'mt_pelerin_monerium_selection_screen',
                                                account: step.account,
                                                keyStore: step.keyStore,
                                            })

                                            break
                                        case 'on_card_disconnected':
                                        case 'on_create_smart_wallet_clicked':
                                        case 'on_card_import_on_import_keys_clicked':
                                        case 'on_card_imported_success_animation_complete':
                                        case 'on_onboarded_card_imported_success_animation_complete':
                                            onMsg(msg)
                                            break
                                        default:
                                            return notReachable(msg)
                                    }
                                }}
                            />
                        )

                    /* istanbul ignore next */
                    default:
                        return notReachable(step)
                }
            }}
        </StepWizard>
    )
}
