import { useState } from 'react'

import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { Address } from '@zeal/toolkit/Web3/address'

import { AccountsMap } from '@zeal/domains/Account'
import { CardConfig } from '@zeal/domains/Card'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { DepositWithdrawVariant } from '@zeal/domains/Currency/domains/BankTransfer'
import { BANK_TRANSFER_LOGIN_NETWORK } from '@zeal/domains/Currency/domains/BankTransfer/constants'
import { SelectResidency } from '@zeal/domains/Currency/domains/BankTransfer/features/BankTransferFork2/Layout/SelectResidency'
import { DepositWithdraw as UnblockDepositWithdraw } from '@zeal/domains/Currency/domains/BankTransfer/features/DepositWithdraw'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap } from '@zeal/domains/Portfolio'
import {
    BankTransferInfo,
    CustomCurrencyMap,
    DefaultCurrencyConfig,
} from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { ChooseProviderCurrency } from './ChooseProviderCurrency'

type Props = {
    installationCampaign: string | null
    variant: DepositWithdrawVariant
    installationId: string
    selectedAddress: Address
    bankTransfer: BankTransferInfo
    accountsMap: AccountsMap
    keystoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    sessionPassword: string
    customCurrencies: CustomCurrencyMap
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    networkMap: NetworkMap
    feePresetMap: FeePresetMap
    networkRPCMap: NetworkRPCMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    cardConfig: CardConfig
    defaultCurrencyConfig: DefaultCurrencyConfig
    onMsg: (msg: Msg) => void
}

type Msg =
    | MsgOf<typeof ChooseProviderCurrency>
    | MsgOf<typeof UnblockDepositWithdraw>
    | Extract<MsgOf<typeof SelectResidency>, { type: 'close' }>

type State =
    | { type: 'choose_residency' }
    | { type: 'choose_provider_currency' }
    | { type: 'unblock_bank_transfer' }

// FIXME @kate change states for step wizzard
export const NoCardNoUnblock = ({
    variant,
    installationId,
    onMsg,
    selectedAddress,
    customCurrencies,
    sessionPassword,
    bankTransfer,
    portfolioMap,
    accountsMap,
    keystoreMap,
    networkMap,
    networkRPCMap,
    feePresetMap,
    cardConfig,
    currencyHiddenMap,
    currencyPinMap,
    gasCurrencyPresetMap,
    defaultCurrencyConfig,
    installationCampaign,
}: Props) => {
    const [state, setState] = useState<State>({ type: 'choose_residency' })

    switch (state.type) {
        case 'choose_residency':
            return (
                <SelectResidency
                    installationId={installationId}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                onMsg(msg)
                                break
                            case 'on_supported_country_selected':
                                switch (msg.providerSupport.type) {
                                    case 'unblock_only':
                                        setState({
                                            type: 'unblock_bank_transfer',
                                        })
                                        break
                                    case 'monerium_only':
                                    case 'monerium_and_unblock':
                                        setState({
                                            type: 'choose_provider_currency',
                                        })
                                        break
                                    /* istanbul ignore next */
                                    default:
                                        return notReachable(msg.providerSupport)
                                }
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )
        case 'choose_provider_currency':
            return (
                <ChooseProviderCurrency
                    selectedAddress={selectedAddress}
                    installationCampaign={installationCampaign}
                    variant={variant}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    currencyHiddenMap={currencyHiddenMap}
                    accountsMap={accountsMap}
                    cardConfig={cardConfig}
                    installationId={installationId}
                    keystoreMap={keystoreMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    portfolioMap={portfolioMap}
                    sessionPassword={sessionPassword}
                    onMsg={onMsg}
                />
            )

        case 'unblock_bank_transfer':
            return (
                <UnblockDepositWithdraw
                    variant={variant}
                    network={BANK_TRANSFER_LOGIN_NETWORK}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    initialCurrency={null}
                    selectedAddress={selectedAddress}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    accountsMap={accountsMap}
                    bankTransfer={bankTransfer}
                    customCurrencies={customCurrencies}
                    cardConfig={cardConfig}
                    installationId={installationId}
                    keystoreMap={keystoreMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    feePresetMap={feePresetMap}
                    portfolioMap={portfolioMap}
                    sessionPassword={sessionPassword}
                    onMsg={onMsg}
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
