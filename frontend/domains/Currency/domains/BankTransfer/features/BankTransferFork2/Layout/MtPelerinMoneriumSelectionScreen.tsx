import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Column } from '@zeal/uikit/Column'
import { HeaderV2 } from '@zeal/uikit/HeaderV2'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { LightArrowRight2 } from '@zeal/uikit/Icon/LightArrowRight2'
import { IconButton } from '@zeal/uikit/IconButton'
import { ListItemButton } from '@zeal/uikit/ListItem'
import { Screen } from '@zeal/uikit/Screen'

import { notReachable } from '@zeal/toolkit'

import { CardConfig } from '@zeal/domains/Card'
import { DEFAULT_MONERIUM_CRYPTO_CURRENCY } from '@zeal/domains/Card/domains/MoneriumBankTransfer/constants'
import { Avatar as CountryAvatar } from '@zeal/domains/Country/components/Avatar'
import { Avatar as CurrencyAvatar } from '@zeal/domains/Currency/components/Avatar'
import { DepositWithdrawVariant } from '@zeal/domains/Currency/domains/BankTransfer'
import { SupportButton } from '@zeal/domains/Support/features/SupportButton'

type Props = {
    cardConfig: CardConfig
    installationId: string
    variant: DepositWithdrawVariant
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | { type: 'on_provider_selected'; provider: 'mt_pelerin' | 'monerium' }

export const MtPelerinMoneriumSelectionScreen = ({
    cardConfig,
    onMsg,
    variant,
    installationId,
}: Props) => (
    <Screen
        padding="form"
        background="default"
        onNavigateBack={() => onMsg({ type: 'close' })}
    >
        <ActionBar
            left={
                <IconButton
                    variant="on_light"
                    onClick={() => onMsg({ type: 'close' })}
                >
                    {({ color }) => <BackIcon size={24} color={color} />}
                </IconButton>
            }
            right={
                <SupportButton
                    variant={{
                        type: 'intercom_and_zendesk',
                        cardConfig,
                    }}
                    layoutVariant="icon_button"
                    installationId={installationId}
                    location="mt_pelerin_monerium_bank_transfer_fork"
                />
            }
        />
        <Column spacing={24} fill>
            <HeaderV2
                title={(() => {
                    switch (variant.type) {
                        case 'deposit':
                            return (
                                <FormattedMessage
                                    id="mt-pelerin-fork.title.deposit"
                                    defaultMessage="What do you want to deposit?"
                                />
                            )
                        case 'withdraw':
                            return (
                                <FormattedMessage
                                    id="mt-pelerin-fork.title.withdraw"
                                    defaultMessage="What do you want to send?"
                                />
                            )
                        default:
                            return notReachable(variant)
                    }
                })()}
                subtitle={null}
                size="medium"
                align="left"
            />
            <Column spacing={8}>
                <ListItemButton
                    variant="outline"
                    background="surface"
                    disabled={false}
                    aria-current={false}
                    onClick={() =>
                        onMsg({
                            type: 'on_provider_selected',
                            provider: 'mt_pelerin',
                        })
                    }
                    avatar={({ size }) => (
                        <CountryAvatar size={size} countryCode="CH" />
                    )}
                    primaryText={
                        <FormattedMessage
                            id="mt-pelerin-fork.options.chf.primary"
                            defaultMessage="Swiss Franc"
                        />
                    }
                    shortText={
                        <FormattedMessage
                            id="mt-pelerin-fork.options.chf.short"
                            defaultMessage="Instant & Free with Mt Pelerin"
                        />
                    }
                    side={{
                        rightIcon: ({ size }) => (
                            <LightArrowRight2 size={size} color="gray20" />
                        ),
                    }}
                />
                <ListItemButton
                    variant="outline"
                    background="surface"
                    disabled={false}
                    aria-current={false}
                    onClick={() =>
                        onMsg({
                            type: 'on_provider_selected',
                            provider: 'monerium',
                        })
                    }
                    avatar={({ size }) => (
                        <CurrencyAvatar
                            size={size}
                            currency={DEFAULT_MONERIUM_CRYPTO_CURRENCY}
                        />
                    )}
                    primaryText={
                        <FormattedMessage
                            id="mt-pelerin-fork.options.euro.primary"
                            defaultMessage="Euro"
                        />
                    }
                    shortText={
                        <FormattedMessage
                            id="mt-pelerin-fork.options.euro.short"
                            defaultMessage="Instant & Free with Monerium"
                        />
                    }
                    side={{
                        rightIcon: ({ size }) => (
                            <LightArrowRight2 size={size} color="gray20" />
                        ),
                    }}
                />
            </Column>
        </Column>
    </Screen>
)
