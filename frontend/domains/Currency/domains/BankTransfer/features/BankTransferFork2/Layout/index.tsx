import { notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { Address } from '@zeal/toolkit/Web3/address'

import { AccountsMap } from '@zeal/domains/Account'
import {
    CardConfig,
    ReadonlySignerSelectedCardConfig,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { Counterparty } from '@zeal/domains/Card/domains/MoneriumBankTransfer/domains/Counterparty'
import {
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { DepositWithdrawVariant } from '@zeal/domains/Currency/domains/BankTransfer'
import { HasCardHasUnblock } from '@zeal/domains/Currency/domains/BankTransfer/features/BankTransferFork2/Layout/HasCardHasUnblock'
import { HasCardNoUnblock } from '@zeal/domains/Currency/domains/BankTransfer/features/BankTransferFork2/Layout/HasCardNoUnblock'
import { KeyStoreMap, SigningKeyStore } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import {
    BankTransferInfo,
    BankTransferUnblockUserCreated,
    CustomCurrencyMap,
    DefaultCurrencyConfig,
} from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

import { NoCardHasUnblock } from './NoCardHasUnblock'
import { NoCardNoUnblock } from './NoCardNoUnblock'

type Props = {
    cardConfig: CardConfig
    bankTransferInfo: BankTransferInfo
    selectedAddress: Address

    variant: DepositWithdrawVariant
    accountsMap: AccountsMap
    keystoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    sessionPassword: string
    customCurrencies: CustomCurrencyMap
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    networkMap: NetworkMap
    feePresetMap: FeePresetMap
    installationId: string
    networkRPCMap: NetworkRPCMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    defaultCurrencyConfig: DefaultCurrencyConfig
    portfolio: ServerPortfolio2
    installationCampaign: string | null
    counterparties: Counterparty[]
    onMsg: (msg: Msg) => void
}

type Msg =
    | MsgOf<typeof NoCardNoUnblock>
    | MsgOf<typeof NoCardHasUnblock>
    | MsgOf<typeof HasCardNoUnblock>
    | MsgOf<typeof HasCardHasUnblock>

type State =
    | { type: 'no_card_no_unblock' }
    | {
          type: 'has_card_no_unblock'
          cardConfig:
              | ReadonlySignerSelectedCardConfig
              | ReadonlySignerSelectedOnboardedCardConfig
          cardKeyStore: SigningKeyStore
      }
    | {
          type: 'no_card_has_unblock'
          bankTransferInfo: BankTransferUnblockUserCreated
      }
    | {
          type: 'has_card_has_unblock'
          cardConfig:
              | ReadonlySignerSelectedCardConfig
              | ReadonlySignerSelectedOnboardedCardConfig
          cardKeyStore: SigningKeyStore
          bankTransferInfo: BankTransferUnblockUserCreated
      }

const calculateState = (
    bankTransferInfo: BankTransferInfo,
    cardConfig: CardConfig,
    keyStoreMap: KeyStoreMap
): State => {
    switch (bankTransferInfo.type) {
        case 'not_started':
            switch (cardConfig.type) {
                case 'card_readonly_signer_address_is_not_selected':
                    return { type: 'no_card_no_unblock' }
                case 'card_readonly_signer_address_is_selected':
                case 'card_readonly_signer_address_is_selected_fully_onboarded':
                    const keyStore = getKeyStore({
                        keyStoreMap,
                        address: cardConfig.readonlySignerAddress,
                    })

                    switch (keyStore.type) {
                        case 'track_only':
                            return { type: 'no_card_no_unblock' }
                        case 'private_key_store':
                        case 'secret_phrase_key':
                        case 'safe_4337':
                        case 'trezor':
                        case 'ledger':
                            return {
                                type: 'has_card_no_unblock',
                                cardConfig,
                                cardKeyStore: keyStore,
                            }
                        /* istanbul ignore next */
                        default:
                            return notReachable(keyStore)
                    }

                default:
                    return notReachable(cardConfig)
            }
        case 'unblock_user_created':
            switch (cardConfig.type) {
                case 'card_readonly_signer_address_is_not_selected':
                    return { type: 'no_card_has_unblock', bankTransferInfo }
                case 'card_readonly_signer_address_is_selected':
                case 'card_readonly_signer_address_is_selected_fully_onboarded':
                    const keyStore = getKeyStore({
                        keyStoreMap,
                        address: cardConfig.readonlySignerAddress,
                    })

                    switch (keyStore.type) {
                        case 'track_only':
                            return {
                                type: 'no_card_has_unblock',
                                bankTransferInfo,
                            }
                        case 'private_key_store':
                        case 'secret_phrase_key':
                        case 'safe_4337':
                        case 'trezor':
                        case 'ledger':
                            return {
                                type: 'has_card_has_unblock',
                                cardConfig,
                                bankTransferInfo,
                                cardKeyStore: keyStore,
                            }
                        /* istanbul ignore next */
                        default:
                            return notReachable(keyStore)
                    }

                default:
                    return notReachable(cardConfig)
            }
        default:
            return notReachable(bankTransferInfo)
    }
}

export const Layout = ({
    bankTransferInfo,
    onMsg,
    gasCurrencyPresetMap,
    feePresetMap,
    currencyHiddenMap,
    currencyPinMap,
    networkMap,
    networkRPCMap,
    portfolioMap,
    keystoreMap,
    installationCampaign,
    installationId,
    variant,
    customCurrencies,
    sessionPassword,
    selectedAddress,
    accountsMap,
    cardConfig,
    defaultCurrencyConfig,
    portfolio,
    counterparties,
}: Props) => {
    const state = calculateState(bankTransferInfo, cardConfig, keystoreMap)

    switch (state.type) {
        case 'no_card_no_unblock':
            return (
                <NoCardNoUnblock
                    installationCampaign={installationCampaign}
                    variant={variant}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    selectedAddress={selectedAddress}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    accountsMap={accountsMap}
                    bankTransfer={bankTransferInfo}
                    customCurrencies={customCurrencies}
                    cardConfig={cardConfig}
                    installationId={installationId}
                    keystoreMap={keystoreMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    feePresetMap={feePresetMap}
                    portfolioMap={portfolioMap}
                    sessionPassword={sessionPassword}
                    onMsg={onMsg}
                />
            )
        case 'has_card_no_unblock':
            return (
                <HasCardNoUnblock
                    portfolio={portfolio}
                    installationCampaign={installationCampaign}
                    counterparties={counterparties}
                    variant={variant}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    selectedAddress={selectedAddress}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    accountsMap={accountsMap}
                    bankTransfer={bankTransferInfo}
                    customCurrencies={customCurrencies}
                    installationId={installationId}
                    keystoreMap={keystoreMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    feePresetMap={feePresetMap}
                    portfolioMap={portfolioMap}
                    sessionPassword={sessionPassword}
                    keyStoreMap={keystoreMap}
                    cardConfig={state.cardConfig}
                    keyStore={state.cardKeyStore}
                    onMsg={onMsg}
                />
            )
        case 'no_card_has_unblock':
            return (
                <NoCardHasUnblock
                    bankTransferInfo={state.bankTransferInfo}
                    variant={variant}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    selectedAddress={selectedAddress}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    accountsMap={accountsMap}
                    customCurrencies={customCurrencies}
                    installationId={installationId}
                    keystoreMap={keystoreMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    feePresetMap={feePresetMap}
                    portfolioMap={portfolioMap}
                    sessionPassword={sessionPassword}
                    cardConfig={cardConfig}
                    onMsg={onMsg}
                />
            )
        case 'has_card_has_unblock':
            return (
                <HasCardHasUnblock
                    keyStore={state.cardKeyStore}
                    portfolio={portfolio}
                    installationCampaign={installationCampaign}
                    counterparties={counterparties}
                    variant={variant}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    selectedAddress={selectedAddress}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    accountsMap={accountsMap}
                    bankTransfer={bankTransferInfo}
                    customCurrencies={customCurrencies}
                    installationId={installationId}
                    keystoreMap={keystoreMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    feePresetMap={feePresetMap}
                    portfolioMap={portfolioMap}
                    sessionPassword={sessionPassword}
                    keyStoreMap={keystoreMap}
                    cardConfig={state.cardConfig}
                    onMsg={onMsg}
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
