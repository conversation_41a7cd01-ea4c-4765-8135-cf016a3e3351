import { useState } from 'react'

import { noop, notReachable } from '@zeal/toolkit'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import { Address } from '@zeal/toolkit/Web3/address'

import { AccountsMap } from '@zeal/domains/Account'
import {
    CardSlientSignKeyStore,
    ReadonlySignerSelectedCardConfig,
    ReadonlySignerSelectedOnboardedCardConfig,
} from '@zeal/domains/Card'
import { Counterparty } from '@zeal/domains/Card/domains/MoneriumBankTransfer/domains/Counterparty'
import { Deposit as MoneriumDeposit } from '@zeal/domains/Card/domains/MoneriumBankTransfer/features/Deposit'
import { Withdraw } from '@zeal/domains/Card/domains/MoneriumBankTransfer/features/Withdraw'
import {
    CryptoCurrency,
    CurrencyHiddenMap,
    CurrencyPinMap,
    GasCurrencyPresetMap,
} from '@zeal/domains/Currency'
import { DepositWithdrawVariant } from '@zeal/domains/Currency/domains/BankTransfer'
import { BANK_TRANSFER_LOGIN_NETWORK } from '@zeal/domains/Currency/domains/BankTransfer/constants'
import { NotEligibleForMonerium } from '@zeal/domains/Currency/domains/BankTransfer/features/BankTransferFork2/Layout/NotEligibleForMonerium'
import { ChooseProviderCurrency } from '@zeal/domains/Currency/domains/BankTransfer/features/ChooseProviderCurrency'
import { DepositWithdraw as UnblockDepositWithdraw } from '@zeal/domains/Currency/domains/BankTransfer/features/DepositWithdraw'
import { MtPelerinBankTransfer } from '@zeal/domains/Currency/domains/BankTransfer/features/MtPelerinBankTransfer'
import { KeyStoreMap, SigningKeyStore } from '@zeal/domains/KeyStore'
import { NetworkMap, NetworkRPCMap } from '@zeal/domains/Network'
import { PortfolioMap, ServerPortfolio2 } from '@zeal/domains/Portfolio'
import {
    BankTransferInfo,
    CustomCurrencyMap,
    DefaultCurrencyConfig,
} from '@zeal/domains/Storage'
import { FeePresetMap } from '@zeal/domains/Transactions/domains/FeeForecast'

type Props = {
    variant: DepositWithdrawVariant
    keyStore: SigningKeyStore
    cardConfig:
        | ReadonlySignerSelectedCardConfig
        | ReadonlySignerSelectedOnboardedCardConfig
    networkMap: NetworkMap
    networkRPCMap: NetworkRPCMap
    accountsMap: AccountsMap
    feePresetMap: FeePresetMap
    gasCurrencyPresetMap: GasCurrencyPresetMap
    selectedAddress: Address
    installationId: string
    keyStoreMap: KeyStoreMap
    portfolioMap: PortfolioMap
    sessionPassword: string
    defaultCurrencyConfig: DefaultCurrencyConfig
    bankTransfer: BankTransferInfo
    keystoreMap: KeyStoreMap
    customCurrencies: CustomCurrencyMap
    currencyHiddenMap: CurrencyHiddenMap
    currencyPinMap: CurrencyPinMap
    portfolio: ServerPortfolio2
    counterparties: Counterparty[]
    installationCampaign: string | null
    onMsg: (msg: Msg) => void
}

type Msg =
    | Extract<MsgOf<typeof ChooseProviderCurrency>, { type: 'close' }>
    | Extract<
          MsgOf<typeof MoneriumDeposit>,
          {
              type:
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_activate_existing_monerium_account_click'
                  | 'monerium_deposit_on_enable_card_clicked'
                  | 'on_monerium_deposit_success_go_to_wallet_clicked'
                  | 'monerium_on_card_disconnected'
                  | 'on_create_smart_wallet_clicked'
                  | 'on_monerium_sign_delay_relay_success_close_clicked'
                  | 'on_card_disconnected'
                  | 'on_card_import_on_import_keys_clicked'
                  | 'on_card_imported_success_animation_complete'
                  | 'on_onboarded_card_imported_success_animation_complete'
          }
      >
    | Extract<
          MsgOf<typeof Withdraw>,
          {
              type:
                  | 'on_monerium_order_status_changed'
                  | 'close'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_activate_existing_monerium_account_click'
                  | 'monerium_deposit_on_enable_card_clicked'
                  | 'on_save_counterparty_form_submitted'
                  | 'on_delete_counterparty_submitted'
                  | 'on_delete_last_counterparty_submitted'
                  | 'on_contact_support_clicked'
                  | 'monerium_on_card_disconnected'
                  | 'on_create_smart_wallet_clicked'
                  | 'on_monerium_sign_delay_relay_success_close_clicked'
          }
      >
    | Extract<
          MsgOf<typeof UnblockDepositWithdraw>,
          {
              type:
                  | 'import_keys_button_clicked'
                  | 'on_4337_auto_gas_token_selection_clicked'
                  | 'on_4337_gas_currency_selected'
                  | 'on_predefined_fee_preset_selected'
                  | 'on_account_create_request'
                  | 'on_latest_bank_transfer_owner_found'
                  | 'on_import_latest_bank_transfer_owner_clicked'
                  | 'on_user_login_to_unblock_success'
                  | 'kyc_applicant_created'
                  | 'bank_transfer_owner_successfully_changed'
                  | 'on_withdrawal_monitor_fiat_transaction_start'
                  | 'on_withdrawal_monitor_fiat_transaction_success'
                  | 'on_contact_support_clicked'
                  | 'on_on_ramp_transfer_success_close_click'
                  | 'on_kyc_data_updated_close_clicked'
          }
      >

type State =
    | { type: 'choose_provider_currency' }
    | { type: 'monerium_bank_transfer' }
    | { type: 'mt_pelerin_bank_transfer'; keyStore: CardSlientSignKeyStore }
    | { type: 'not_eligible_for_monerium' }
    | { type: 'unblock_bank_transfer'; currency: CryptoCurrency }

export const HasCardHasUnblock = ({
    variant,
    onMsg,
    cardConfig,
    defaultCurrencyConfig,
    gasCurrencyPresetMap,
    feePresetMap,
    installationId,
    portfolioMap,
    accountsMap,
    keyStoreMap,
    networkMap,
    networkRPCMap,
    currencyHiddenMap,
    currencyPinMap,
    customCurrencies,
    keystoreMap,
    bankTransfer,
    sessionPassword,
    selectedAddress,
    counterparties,
    portfolio,
    installationCampaign,
    keyStore,
}: Props) => {
    const [state, setState] = useState<State>({
        type: 'choose_provider_currency',
    })

    switch (state.type) {
        case 'choose_provider_currency':
            return (
                <ChooseProviderCurrency
                    keyStore={keyStore}
                    installationId={installationId}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    networkMap={networkMap}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                onMsg(msg)
                                break
                            case 'on_monerium_currency_selected':
                                setState({ type: 'monerium_bank_transfer' })
                                break
                            case 'on_mt_pelerin_currency_selected':
                                setState({
                                    type: 'mt_pelerin_bank_transfer',
                                    keyStore: msg.keyStore,
                                })
                                break
                            case 'on_unblock_currency_selected':
                                setState({
                                    type: 'unblock_bank_transfer',
                                    currency: msg.currency,
                                })
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )

        case 'monerium_bank_transfer':
            switch (variant.type) {
                case 'deposit':
                    return (
                        <MoneriumDeposit
                            accountsMap={accountsMap}
                            installationCampaign={installationCampaign}
                            cardConfig={cardConfig}
                            currencyHiddenMap={currencyHiddenMap}
                            defaultCurrencyConfig={defaultCurrencyConfig}
                            feePresetMap={feePresetMap}
                            gasCurrencyPresetMap={gasCurrencyPresetMap}
                            installationId={installationId}
                            keyStoreMap={keyStoreMap}
                            networkMap={networkMap}
                            networkRPCMap={networkRPCMap}
                            portfolioMap={portfolioMap}
                            sessionPassword={sessionPassword}
                            location="bank_transfer"
                            onMsg={(msg) => {
                                switch (msg.type) {
                                    case 'on_4337_auto_gas_token_selection_clicked':
                                    case 'on_4337_gas_currency_selected':
                                    case 'on_activate_existing_monerium_account_click':
                                    case 'monerium_deposit_on_enable_card_clicked':
                                    case 'on_monerium_deposit_success_go_to_wallet_clicked':
                                    case 'monerium_on_card_disconnected':
                                    case 'on_create_smart_wallet_clicked':
                                    case 'on_monerium_sign_delay_relay_success_close_clicked':
                                    case 'on_card_disconnected':
                                    case 'on_card_import_on_import_keys_clicked':
                                    case 'on_card_imported_success_animation_complete':
                                    case 'on_onboarded_card_imported_success_animation_complete':
                                        onMsg(msg)
                                        break
                                    case 'on_switch_bank_transfer_provider_clicked':
                                    case 'close':
                                        setState({
                                            type: 'choose_provider_currency',
                                        })
                                        break
                                    case 'on_monerium_successfully_activated':
                                        noop() // no need to react to this here
                                        break
                                    case 'on_user_not_eligible_for_monerium':
                                        setState({
                                            type: 'not_eligible_for_monerium',
                                        })
                                        break
                                    /* istanbul ignore next */
                                    default:
                                        return notReachable(msg)
                                }
                            }}
                        />
                    )
                case 'withdraw':
                    return (
                        <Withdraw
                            installationCampaign={installationCampaign}
                            selectedAdress={selectedAddress}
                            currencyHiddenMap={currencyHiddenMap}
                            portfolio={portfolio}
                            counterparties={counterparties}
                            keyStore={keyStore}
                            accountsMap={accountsMap}
                            cardConfig={cardConfig}
                            defaultCurrencyConfig={defaultCurrencyConfig}
                            feePresetMap={feePresetMap}
                            gasCurrencyPresetMap={gasCurrencyPresetMap}
                            installationId={installationId}
                            keyStoreMap={keyStoreMap}
                            networkMap={networkMap}
                            networkRPCMap={networkRPCMap}
                            portfolioMap={portfolioMap}
                            sessionPassword={sessionPassword}
                            location="bank_transfer"
                            onMsg={(msg) => {
                                switch (msg.type) {
                                    case 'close':
                                        setState({
                                            type: 'choose_provider_currency',
                                        })
                                        break
                                    case 'on_4337_auto_gas_token_selection_clicked':
                                    case 'on_4337_gas_currency_selected':
                                    case 'on_activate_existing_monerium_account_click':
                                    case 'monerium_deposit_on_enable_card_clicked':
                                    case 'on_monerium_order_status_changed':
                                    case 'on_save_counterparty_form_submitted':
                                    case 'on_delete_counterparty_submitted':
                                    case 'on_delete_last_counterparty_submitted':
                                    case 'on_contact_support_clicked':
                                    case 'monerium_on_card_disconnected':
                                    case 'on_create_smart_wallet_clicked':
                                    case 'on_monerium_sign_delay_relay_success_close_clicked':
                                    case 'on_card_disconnected':
                                    case 'on_card_import_on_import_keys_clicked':
                                    case 'on_card_imported_success_animation_complete':
                                    case 'on_onboarded_card_imported_success_animation_complete':
                                        onMsg(msg)
                                        break

                                    case 'on_monerium_successfully_activated':
                                        noop() // no need to react to this here
                                        break
                                    case 'on_user_not_eligible_for_monerium':
                                        setState({
                                            type: 'not_eligible_for_monerium',
                                        })
                                        break

                                    /* istanbul ignore next */
                                    default:
                                        notReachable(msg)
                                }
                            }}
                        />
                    )
                /* istanbul ignore next */
                default:
                    return notReachable(variant)
            }
        case 'mt_pelerin_bank_transfer':
            return (
                <MtPelerinBankTransfer
                    variant={variant}
                    keyStore={state.keyStore}
                    selectedAddress={cardConfig.readonlySignerAddress}
                    sessionPassword={sessionPassword}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                setState({ type: 'choose_provider_currency' })
                                break
                            case 'on_card_disconnected':
                            case 'on_create_smart_wallet_clicked':
                            case 'on_card_import_on_import_keys_clicked':
                            case 'on_card_imported_success_animation_complete':
                            case 'on_onboarded_card_imported_success_animation_complete':
                                onMsg(msg)
                                break
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )

        case 'unblock_bank_transfer':
            return (
                <UnblockDepositWithdraw
                    variant={variant}
                    network={BANK_TRANSFER_LOGIN_NETWORK}
                    defaultCurrencyConfig={defaultCurrencyConfig}
                    initialCurrency={state.currency}
                    selectedAddress={selectedAddress}
                    gasCurrencyPresetMap={gasCurrencyPresetMap}
                    currencyHiddenMap={currencyHiddenMap}
                    currencyPinMap={currencyPinMap}
                    accountsMap={accountsMap}
                    bankTransfer={bankTransfer}
                    customCurrencies={customCurrencies}
                    cardConfig={cardConfig}
                    installationId={installationId}
                    keystoreMap={keystoreMap}
                    networkMap={networkMap}
                    networkRPCMap={networkRPCMap}
                    feePresetMap={feePresetMap}
                    portfolioMap={portfolioMap}
                    sessionPassword={sessionPassword}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                setState({ type: 'choose_provider_currency' })
                                break
                            case 'import_keys_button_clicked':
                            case 'on_4337_auto_gas_token_selection_clicked':
                            case 'on_4337_gas_currency_selected':
                            case 'on_predefined_fee_preset_selected':
                            case 'on_account_create_request':
                            case 'on_import_latest_bank_transfer_owner_clicked':
                            case 'on_user_login_to_unblock_success':
                            case 'kyc_applicant_created':
                            case 'bank_transfer_owner_successfully_changed':
                            case 'on_withdrawal_monitor_fiat_transaction_start':
                            case 'on_withdrawal_monitor_fiat_transaction_success':
                            case 'on_contact_support_clicked':
                            case 'on_on_ramp_transfer_success_close_click':
                            case 'on_kyc_data_updated_close_clicked':
                                onMsg(msg)
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg)
                        }
                    }}
                />
            )

        case 'not_eligible_for_monerium':
            return (
                <NotEligibleForMonerium
                    installationId={installationId}
                    onMsg={(msg) => {
                        switch (msg.type) {
                            case 'close':
                                setState({ type: 'choose_provider_currency' })
                                break
                            /* istanbul ignore next */
                            default:
                                return notReachable(msg.type)
                        }
                    }}
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(state)
    }
}
