import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { CardSlientSignKeyStore } from '@zeal/domains/Card'
import { HardwareWalletSupportDrop } from '@zeal/domains/Card/features/CardHardwareWalletSupportDrop'
import { DepositWithdrawVariant } from '@zeal/domains/Currency/domains/BankTransfer'

import { Flow } from './Flow'

type Msg = { type: 'close' } | MsgOf<typeof HardwareWalletSupportDrop>

type Props = {
    selectedAddress: Web3.address.Address
    keyStore: CardSlientSignKeyStore
    variant: DepositWithdrawVariant
    sessionPassword: string
    onMsg: (msg: Msg) => void
}

export const MtPelerinBankTransfer = ({
    onMsg,
    keyStore,
    selectedAddress,
    variant,
    sessionPassword,
}: Props) => {
    return (
        <Flow
            variant={variant}
            keyStore={keyStore}
            selectedAddress={selectedAddress}
            sessionPassword={sessionPassword}
            onMsg={onMsg}
        />
    )
}
