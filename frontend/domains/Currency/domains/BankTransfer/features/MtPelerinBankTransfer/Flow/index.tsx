import { useEffect } from 'react'

import { Language } from '@zeal/uikit/Language'
import { useLanguage } from '@zeal/uikit/Language/LanguageContext'
import { LoadingLayout } from '@zeal/uikit/LoadingLayout'

import { notReachable } from '@zeal/toolkit'
import * as Hexadecimal from '@zeal/toolkit/Hexadecimal'
import { useLoadableData } from '@zeal/toolkit/LoadableData/LoadableData'
import {
    generateRandomNumber,
    generateRandomNumberBetween,
} from '@zeal/toolkit/Number'
import { base64Encode, base64UrlEncode } from '@zeal/toolkit/String/base64'
import * as Web3 from '@zeal/toolkit/Web3'

import { CardSlientSignKeyStore } from '@zeal/domains/Card'
import { CARD_NETWORK } from '@zeal/domains/Card/constants'
import { DepositWithdrawVariant } from '@zeal/domains/Currency/domains/BankTransfer'
import { captureError } from '@zeal/domains/Error/helpers/captureError'
import { signMessage } from '@zeal/domains/RPCRequest/helpers/signMessage'

import { Layout } from './Layout'

type Props = {
    selectedAddress: Web3.address.Address
    keyStore: CardSlientSignKeyStore
    variant: DepositWithdrawVariant
    sessionPassword: string
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'close' }

type MtPelerinLanguage = 'en' | 'fr' | 'de' | 'it' | 'es' | 'pt'

const DEPOSIT_WIDGET_BASE_URL = new URL(
    'https://widget.mtpelerin.com/?_ctkn=21394ded-ade9-427d-889a-244a0ca9e70f&type=direct-link&tabs=buy&bsc=CHF&bsa=100&bdc=ZCHF&dnet=xdai_mainnet&snet=xdai_mainnet&rfr=zeal&primary=%2301C9C9&success=%2301C9C9&mylogo=https://pbs.twimg.com/profile_images/1914969263443750913/R8RSPYwt_400x400.jpg'
)

const WITHDRAW_WIDGET_BASE_URL = new URL(
    'https://widget.mtpelerin.com/?_ctkn=21394ded-ade9-427d-889a-244a0ca9e70f&type=direct-link&tabs=sell&ssc=ZCHF&ssa=100&sdc=CHF&snet=xdai_mainnet&dnet=xdai_mainnet&rfr=zeal&primary=%2301C9C9&success=%2301C9C9&mylogo=https://pbs.twimg.com/profile_images/1914969263443750913/R8RSPYwt_400x400.jpg'
)

const VARIANT_URL_MAP: Record<DepositWithdrawVariant['type'], URL> = {
    deposit: DEPOSIT_WIDGET_BASE_URL,
    withdraw: WITHDRAW_WIDGET_BASE_URL,
}

const LANGUAGE_TO_MT_PELERIN_LANGUAGE_MAP: Record<
    Language,
    MtPelerinLanguage | null
> = {
    'en-GB': 'en',
    'bg-BG': null,
    'fr-FR': 'fr',
    'fr-BE': 'fr',
    'de-DE': 'de',
    'es-ES': 'es',
    'ca-ES': 'es',
    'pt-BR': 'pt',
    'pt-PT': 'pt',
    'it-IT': 'it',
    'hr-HR': null,
    'hu-HU': null,
    'is-IS': null,
    'lb-LU': null,
    'lt-LT': null,
    'lv-LV': null,
    'mt-MT': null,
    'nb-NO': null,
    'nl-BE': null,
    'nl-NL': null,
    'pl-PL': null,
    'uk-UA': null,
    'sv-SE': null,
    'cs-CZ': null,
    'da-DK': null,
    'el-GR': null,
    'et-EE': null,
    'fi-FI': null,
    'af-ZA': null,
    'ro-RO': null,
    'sk-SK': null,
    'sl-SI': null,
    'sq-AL': null,
    'sr-RS': null,
    'tr-TR': null,
    'ga-IE': null,
}

const generateUrl = async ({
    keyStore,
    selectedAddress,
    sessionPassword,
    variant,
    currentSelectedLanguage,
}: {
    selectedAddress: Web3.address.Address
    keyStore: CardSlientSignKeyStore
    currentSelectedLanguage: Language
    variant: DepositWithdrawVariant
    sessionPassword: string
}): Promise<URL> => {
    const widgetBaseUrl = VARIANT_URL_MAP[variant.type]

    const url = new URL(widgetBaseUrl)

    const language =
        LANGUAGE_TO_MT_PELERIN_LANGUAGE_MAP[currentSelectedLanguage] || 'en'

    const code = generateRandomNumberBetween(1000, 9999) // https://developers.mtpelerin.com/integration-guides/parameters-and-customization/automating-the-end-user-address-validation
    const message = `MtPelerin-${code}`

    const signature = await signMessage({
        request: {
            id: generateRandomNumber(),
            jsonrpc: '2.0',
            method: 'personal_sign',
            params: [message],
        },
        keyStore: keyStore,
        network: CARD_NETWORK,
        sessionPassword,
        dApp: null,
    })

    const signatureBase64 = base64Encode(
        Uint8Array.from(Hexadecimal.toBuffer(signature))
    )

    url.search = (() => {
        switch (keyStore.type) {
            case 'private_key_store':
            case 'secret_phrase_key':
                return new URLSearchParams({
                    ...Object.fromEntries(widgetBaseUrl.searchParams),
                    lang: language,
                    addr: selectedAddress,
                    code: code.toString(),
                    hash: base64UrlEncode(signatureBase64),
                }).toString()
            case 'safe_4337':
                return new URLSearchParams({
                    ...Object.fromEntries(widgetBaseUrl.searchParams),
                    lang: language,
                    addr: selectedAddress,
                    code: code.toString(),
                    hash: base64UrlEncode(signatureBase64),
                    chain: 'xdai_mainnet',
                }).toString()
            default:
                return notReachable(keyStore)
        }
    })()

    return url
}

export const Flow = ({
    onMsg,
    selectedAddress,
    sessionPassword,
    variant,
    keyStore,
}: Props) => {
    const { currentSelectedLanguage } = useLanguage()

    const [loadable] = useLoadableData(generateUrl, {
        type: 'loading',
        params: {
            keyStore,
            variant,
            selectedAddress,
            currentSelectedLanguage,
            sessionPassword,
        },
    })

    useEffect(() => {
        switch (loadable.type) {
            case 'loading':
            case 'loaded':
                break
            case 'error':
                captureError(loadable.error)
                break
            default:
                return notReachable(loadable)
        }
    }, [loadable])

    switch (loadable.type) {
        case 'loading':
            return (
                <LoadingLayout
                    actionBar={null}
                    title={null}
                    onClose={() => onMsg({ type: 'close' })}
                />
            )
        case 'loaded':
            return <Layout url={loadable.data} onMsg={onMsg} />
        case 'error':
            return <Layout url={VARIANT_URL_MAP[variant.type]} onMsg={onMsg} />
        default:
            return notReachable(loadable)
    }
}
