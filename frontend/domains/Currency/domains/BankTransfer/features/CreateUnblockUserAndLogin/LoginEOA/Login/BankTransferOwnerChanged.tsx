import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { BannerSolid } from '@zeal/uikit/BannerSolid'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { Header } from '@zeal/uikit/Header'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { Setting } from '@zeal/uikit/Icon/Setting'
import { IconButton } from '@zeal/uikit/IconButton'
import { Screen } from '@zeal/uikit/Screen'
import { Spacer } from '@zeal/uikit/Spacer'

type Props = {
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | { type: 'on_import_latest_bank_transfer_owner_clicked' }

export const BankTransferOwnerChanged = ({ onMsg }: Props) => (
    <Screen
        padding="form"
        background="light"
        onNavigateBack={() => onMsg({ type: 'close' })}
    >
        <ActionBar
            left={
                <IconButton
                    variant="on_light"
                    onClick={() => onMsg({ type: 'close' })}
                >
                    {({ color }) => <BackIcon size={24} color={color} />}
                </IconButton>
            }
        />
        <Column spacing={16} fill>
            <Header
                title={
                    <FormattedMessage
                        id="import-bank-transfer-owner.title"
                        defaultMessage="Import wallet to use bank transfers on this device"
                    />
                }
                icon={({ color, size }) => (
                    <Setting color={color} size={size} />
                )}
            />
            <Column spacing={8}>
                <BannerSolid
                    rounded
                    variant="light"
                    title={
                        <FormattedMessage
                            id="import-bank-transfer-owner.banner.title"
                            defaultMessage="The wallet connected to bank transfers has been changed. To continue with bank transfers on this device, import your wallet."
                        />
                    }
                />
            </Column>
            <Spacer />
            <Actions variant="default">
                <Button
                    variant="secondary"
                    size="regular"
                    onClick={() => onMsg({ type: 'close' })}
                >
                    <FormattedMessage
                        id="action.cancel"
                        defaultMessage="Cancel"
                    />
                </Button>
                <Button
                    variant="primary"
                    size="regular"
                    onClick={() =>
                        onMsg({
                            type: 'on_import_latest_bank_transfer_owner_clicked',
                        })
                    }
                >
                    <FormattedMessage
                        id="action.import"
                        defaultMessage="Import"
                    />
                </Button>
            </Actions>
        </Column>
    </Screen>
)
