import { useState } from 'react'
import { FormattedMessage, useIntl } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Avatar } from '@zeal/uikit/Avatar'
import { BannerLimit } from '@zeal/uikit/BannerLimit'
import { BannerSolid } from '@zeal/uikit/BannerSolid'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { FeeInputButton } from '@zeal/uikit/FeeInputButton'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { BoldDangerTriangle } from '@zeal/uikit/Icon/BoldDangerTriangle'
import { BoldSetting } from '@zeal/uikit/Icon/BoldSetting'
import { InfoCircleOutline } from '@zeal/uikit/Icon/InfoCircleOutline'
import { LightArrowDown2 } from '@zeal/uikit/Icon/LightArrowDown2'
import { Unblock } from '@zeal/uikit/Icon/Providers/Unblock'
import { IconButton } from '@zeal/uikit/IconButton'
import { AmountInput } from '@zeal/uikit/Input/AmountInput'
import { NextStepSeparator } from '@zeal/uikit/NextStepSeparator'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { ScrollContainer } from '@zeal/uikit/ScrollContainer'
import { Skeleton } from '@zeal/uikit/Skeleton'
import { Spacer } from '@zeal/uikit/Spacer'
import { TabHeader } from '@zeal/uikit/TabHeader'
import { Text } from '@zeal/uikit/Text'

import { noop, notReachable } from '@zeal/toolkit'
import { fromFixedWithFraction } from '@zeal/toolkit/BigInt'
import { PollableData } from '@zeal/toolkit/LoadableData/PollableData'

import { AccountsMap } from '@zeal/domains/Account'
import { NullableListItemButton } from '@zeal/domains/Account/components/NullableListItemButton'
import { Avatar as CurrencyAvatar } from '@zeal/domains/Currency/components/Avatar'
import {
    DepositPollable,
    UnblockUser,
} from '@zeal/domains/Currency/domains/BankTransfer'
import { OnRampFeeParams } from '@zeal/domains/Currency/domains/BankTransfer/api/fetchTransactionFee'
import { OnRampFeeResponse } from '@zeal/domains/Currency/domains/BankTransfer/api/fetchUnblockOnRampFee'
import { DepositFees } from '@zeal/domains/Currency/domains/BankTransfer/components/DepositFees'
import { applyRate2 } from '@zeal/domains/FXRate/helpers/applyRate'
import { CryptoMoney, FiatMoney } from '@zeal/domains/Money'
import { FormattedMoneyPrecise } from '@zeal/domains/Money/components/FormattedMoneyPrecise'
import { sub2 } from '@zeal/domains/Money/helpers/sub'
import { useMoneyFormat } from '@zeal/domains/Money/hooks/useMoneyFormat'
import { NetworkMap } from '@zeal/domains/Network'
import { FancyButton as NetworkFancyButton } from '@zeal/domains/Network/components/FancyButton'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { getBalanceByCryptoCurrency2 } from '@zeal/domains/Portfolio/helpers/getBalanceByCryptoCurrency'

import {
    BannerError,
    FormErrors,
    validateBeforeSubmit,
    validateOnSubmit,
} from './validation'

type Props = {
    pollable: DepositPollable
    unblockUser: UnblockUser
    accountsMap: AccountsMap
    depositReceiverPortfolio: ServerPortfolio2
    networkMap: NetworkMap
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | { type: 'on_amount_change'; amount: string | null }
    | { type: 'on_withdraw_tab_click' }
    | { type: 'on_fiat_currency_selector_click' }
    | { type: 'on_crypto_currency_selector_click' }
    | { type: 'on_network_selector_click' }
    | { type: 'on_provider_info_click' }
    | {
          type: 'on_submit_form_click'
          depositFeesPollable: PollableData<OnRampFeeResponse, OnRampFeeParams>
      }
    | { type: 'on_settings_icon_click' }
    | { type: 'on_deposit_receiver_click' }

const getAmounts = (
    pollable: DepositPollable
): {
    cryptoNetOutputAmount: CryptoMoney | null
    cryptoNetOutputAmountInDefaultCurrency: FiatMoney | null
    fiatInputAmountInDefaultCurrency: FiatMoney | null
} => {
    switch (pollable.type) {
        case 'loaded':
        case 'reloading':
        case 'subsequent_failed':
            const fiatInputAmount: FiatMoney = {
                amount: fromFixedWithFraction(
                    pollable.params.amount,
                    pollable.params.inputCurrency.fraction
                ),
                currency: pollable.params.inputCurrency,
            }

            const fiatInputAmountInDefaultCurrency = pollable.data
                .defaultCurrencyRateToInputCurrency
                ? applyRate2({
                      baseAmount: fiatInputAmount,
                      rate: pollable.data.defaultCurrencyRateToInputCurrency,
                  })
                : null

            const grossAmount = applyRate2({
                baseAmount: fiatInputAmount,
                rate: pollable.data.rate,
            })

            const feeAmount = applyRate2({
                baseAmount: pollable.data.fee.amount,
                rate: pollable.data.rate,
            })

            const cryptoNetOutputAmount = sub2(grossAmount, feeAmount)

            const cryptoNetOutputAmountInDefaultCurrency = pollable.data
                .defaultCurrencyRateToOutputCurrency
                ? applyRate2({
                      baseAmount: cryptoNetOutputAmount,
                      rate: pollable.data.defaultCurrencyRateToOutputCurrency,
                  })
                : null

            return {
                cryptoNetOutputAmount,
                cryptoNetOutputAmountInDefaultCurrency,
                fiatInputAmountInDefaultCurrency,
            }

        case 'loading':
        case 'error':
            return {
                cryptoNetOutputAmount: null,
                cryptoNetOutputAmountInDefaultCurrency: null,
                fiatInputAmountInDefaultCurrency: null,
            }
        /* istanbul ignore next */
        default:
            return notReachable(pollable)
    }
}

export const Layout = ({
    pollable,
    unblockUser,
    networkMap,
    onMsg,
    accountsMap,
    depositReceiverPortfolio,
}: Props) => {
    const [submitAttempted, setSubmitAttempted] = useState<boolean>(false)

    const { formattedCryptoMoneyPrecise } = useMoneyFormat()
    const { formatMessage } = useIntl()

    const { amount, inputCurrency, outputCurrency } = pollable.params

    const result = submitAttempted
        ? validateOnSubmit(pollable)
        : validateBeforeSubmit(pollable)

    const errors = result.getFailureReason() || {}

    // TODO @resetko-zeal fee has been deducted. With the rounding in the UI, the numbers are a bit off
    const {
        cryptoNetOutputAmount,
        cryptoNetOutputAmountInDefaultCurrency,
        fiatInputAmountInDefaultCurrency,
    } = getAmounts(pollable)

    const formattedCrypto = cryptoNetOutputAmount
        ? formattedCryptoMoneyPrecise({
              money: cryptoNetOutputAmount,
              withSymbol: false,
              sign: null,
          })
        : null

    const outputCryptoCurrencyTokenBalance = getBalanceByCryptoCurrency2({
        serverPortfolio: depositReceiverPortfolio,
        currency: pollable.params.outputCurrency,
    })

    const network = findNetworkByHexChainId(
        pollable.params.outputCurrency.networkHexChainId,
        networkMap
    )

    const onSubmit = () => {
        setSubmitAttempted(true)
        const result = validateOnSubmit(pollable)

        switch (result.type) {
            case 'Failure':
                break
            case 'Success':
                onMsg({
                    type: 'on_submit_form_click',
                    depositFeesPollable: result.data,
                })
                break
            /* istanbul ignore next */
            default:
                return notReachable(result)
        }
    }

    return (
        <Screen
            padding="form"
            background="light"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <Column spacing={16} fill>
                <ActionBar
                    center={
                        <Row spacing={12} grow shrink>
                            <TabHeader selected>
                                <FormattedMessage
                                    id="bank_transfers.deposit.deposit-header"
                                    defaultMessage="Deposit"
                                />
                            </TabHeader>
                            <TabHeader
                                selected={false}
                                onClick={() =>
                                    onMsg({ type: 'on_withdraw_tab_click' })
                                }
                            >
                                <FormattedMessage
                                    id="bank_transfers.deposit.withdraw-header"
                                    defaultMessage="Withdraw"
                                />
                            </TabHeader>
                        </Row>
                    }
                    left={
                        <IconButton
                            variant="on_light"
                            onClick={() => onMsg({ type: 'close' })}
                        >
                            {({ color }) => (
                                <BackIcon size={24} color={color} />
                            )}
                        </IconButton>
                    }
                    right={
                        <IconButton
                            variant="on_light"
                            onClick={() =>
                                onMsg({ type: 'on_settings_icon_click' })
                            }
                        >
                            {({ color }) => (
                                <BoldSetting size={24} color={color} />
                            )}
                        </IconButton>
                    }
                />
                <Column spacing={8} fill alignY="stretch">
                    <Column spacing={16} fill alignY="stretch">
                        <ScrollContainer
                            contentFill
                            withFloatingActions={false}
                        >
                            <Column spacing={12} fill>
                                <Column spacing={4}>
                                    <AmountInput
                                        state={
                                            errors.input ? 'error' : 'normal'
                                        }
                                        content={{
                                            topLeft: (
                                                <IconButton
                                                    variant="on_light"
                                                    onClick={() => {
                                                        onMsg({
                                                            type: 'on_fiat_currency_selector_click',
                                                        })
                                                    }}
                                                >
                                                    {({ color }) => (
                                                        <Row spacing={4}>
                                                            <CurrencyAvatar
                                                                key={
                                                                    inputCurrency.id
                                                                }
                                                                currency={
                                                                    inputCurrency
                                                                }
                                                                size={24}
                                                                rightBadge={() =>
                                                                    null
                                                                }
                                                            />
                                                            <Text
                                                                variant="title3"
                                                                color="textPrimary"
                                                                weight="medium"
                                                            >
                                                                {
                                                                    inputCurrency.code
                                                                }
                                                            </Text>
                                                            <LightArrowDown2
                                                                size={18}
                                                                color={color}
                                                            />
                                                        </Row>
                                                    )}
                                                </IconButton>
                                            ),
                                            topRight: ({ onBlur, onFocus }) => (
                                                <AmountInput.Input
                                                    onBlur={onBlur}
                                                    onFocus={onFocus}
                                                    label={formatMessage({
                                                        id: 'bank_transfers.deposit.amount-input',
                                                        defaultMessage:
                                                            'Amount to deposit',
                                                    })}
                                                    amount={amount}
                                                    fraction={
                                                        inputCurrency.fraction
                                                    }
                                                    onChange={(value) =>
                                                        onMsg({
                                                            type: 'on_amount_change',
                                                            amount: value,
                                                        })
                                                    }
                                                    autoFocus
                                                    prefix={
                                                        inputCurrency.symbol
                                                    }
                                                    onSubmitEditing={onSubmit}
                                                />
                                            ),
                                            bottomRight: (() => {
                                                switch (pollable.type) {
                                                    case 'loading':
                                                    case 'reloading':
                                                        return (
                                                            <Skeleton
                                                                variant="default"
                                                                width={40}
                                                                height={16}
                                                            />
                                                        )
                                                    case 'loaded':
                                                    case 'subsequent_failed':
                                                        return fiatInputAmountInDefaultCurrency ? (
                                                            <Text
                                                                variant="footnote"
                                                                color="textSecondary"
                                                                weight="regular"
                                                            >
                                                                <FormattedMoneyPrecise
                                                                    withSymbol
                                                                    sign={null}
                                                                    money={
                                                                        fiatInputAmountInDefaultCurrency
                                                                    }
                                                                />
                                                            </Text>
                                                        ) : null
                                                    case 'error':
                                                        return null
                                                    /* istanbul ignore next */
                                                    default:
                                                        return notReachable(
                                                            pollable
                                                        )
                                                }
                                            })(),
                                        }}
                                    />

                                    <NextStepSeparator />

                                    <AmountInput
                                        state={(() => {
                                            switch (pollable.type) {
                                                case 'error':
                                                    return 'error'
                                                case 'loaded':
                                                case 'reloading':
                                                case 'subsequent_failed':
                                                case 'loading':
                                                    return 'normal'
                                                default:
                                                    return notReachable(
                                                        pollable
                                                    )
                                            }
                                        })()}
                                        top={
                                            <NetworkFancyButton
                                                fill
                                                rounded={false}
                                                network={network}
                                                onClick={() =>
                                                    onMsg({
                                                        type: 'on_network_selector_click',
                                                    })
                                                }
                                            />
                                        }
                                        content={{
                                            topLeft: (
                                                <IconButton
                                                    variant="on_light"
                                                    onClick={() =>
                                                        onMsg({
                                                            type: 'on_crypto_currency_selector_click',
                                                        })
                                                    }
                                                >
                                                    {() => (
                                                        <Row spacing={4}>
                                                            <CurrencyAvatar
                                                                key={
                                                                    outputCurrency.id
                                                                }
                                                                currency={
                                                                    outputCurrency
                                                                }
                                                                size={24}
                                                                rightBadge={() =>
                                                                    null
                                                                }
                                                            />
                                                            <Text
                                                                variant="title3"
                                                                color="textPrimary"
                                                                weight="medium"
                                                            >
                                                                {
                                                                    outputCurrency.code
                                                                }
                                                            </Text>
                                                            <LightArrowDown2
                                                                size={18}
                                                                color="iconDefault"
                                                            />
                                                        </Row>
                                                    )}
                                                </IconButton>
                                            ),
                                            topRight: ({ onBlur, onFocus }) =>
                                                (() => {
                                                    switch (pollable.type) {
                                                        case 'loading':
                                                        case 'reloading':
                                                            return (
                                                                <AmountInput.InputSkeleton />
                                                            )
                                                        case 'loaded':
                                                        case 'subsequent_failed':
                                                            return (
                                                                <AmountInput.Input
                                                                    onBlur={
                                                                        onBlur
                                                                    }
                                                                    onFocus={
                                                                        onFocus
                                                                    }
                                                                    label={formatMessage(
                                                                        {
                                                                            id: 'bank_transfers.deposit.amount-output',
                                                                            defaultMessage:
                                                                                'Destination amount',
                                                                        }
                                                                    )}
                                                                    amount={
                                                                        formattedCrypto
                                                                    }
                                                                    fraction={
                                                                        outputCurrency.fraction ??
                                                                        0
                                                                    }
                                                                    onChange={
                                                                        noop
                                                                    }
                                                                    prefix=""
                                                                    readOnly
                                                                    onSubmitEditing={
                                                                        onSubmit
                                                                    }
                                                                />
                                                            )
                                                        case 'error':
                                                            return (
                                                                <Column
                                                                    spacing={0}
                                                                    alignX="end"
                                                                >
                                                                    <Text
                                                                        variant="title3"
                                                                        color="textDisabled"
                                                                        weight="regular"
                                                                    >
                                                                        <FormattedMessage
                                                                            id="bank_transfers.deposit.amount-output.error"
                                                                            defaultMessage="Error"
                                                                        />
                                                                    </Text>
                                                                </Column>
                                                            )
                                                        /* istanbul ignore next */
                                                        default:
                                                            return notReachable(
                                                                pollable
                                                            )
                                                    }
                                                })(),
                                            bottomRight: (() => {
                                                switch (pollable.type) {
                                                    case 'loading':
                                                    case 'reloading':
                                                        return (
                                                            <Skeleton
                                                                variant="default"
                                                                width={40}
                                                                height={16}
                                                            />
                                                        )
                                                    case 'loaded':
                                                    case 'subsequent_failed':
                                                        return cryptoNetOutputAmountInDefaultCurrency ? (
                                                            <Text
                                                                variant="footnote"
                                                                color="textSecondary"
                                                                weight="regular"
                                                            >
                                                                <FormattedMoneyPrecise
                                                                    withSymbol
                                                                    sign={null}
                                                                    money={
                                                                        cryptoNetOutputAmountInDefaultCurrency
                                                                    }
                                                                />
                                                            </Text>
                                                        ) : null
                                                    case 'error':
                                                        return null
                                                    /* istanbul ignore next */
                                                    default:
                                                        return notReachable(
                                                            pollable
                                                        )
                                                }
                                            })(),
                                            bottomLeft: (
                                                <Text
                                                    color="textSecondary"
                                                    variant="paragraph"
                                                >
                                                    <FormattedMessage
                                                        id="bank_transfers.deposit.default-token.balance"
                                                        defaultMessage="Balance {amount}"
                                                        values={{
                                                            amount: (
                                                                <FormattedMoneyPrecise
                                                                    withSymbol={
                                                                        false
                                                                    }
                                                                    sign={null}
                                                                    money={
                                                                        outputCryptoCurrencyTokenBalance
                                                                    }
                                                                />
                                                            ),
                                                        }}
                                                    />
                                                </Text>
                                            ),
                                        }}
                                    />

                                    <NextStepSeparator />

                                    <NullableListItemButton
                                        disabled={false}
                                        address={
                                            unblockUser.depositReceiverAddress
                                        }
                                        accountsMap={accountsMap}
                                        onClick={() =>
                                            onMsg({
                                                type: 'on_deposit_receiver_click',
                                            })
                                        }
                                    />
                                </Column>
                                <Spacer />
                                <Column spacing={12}>
                                    <Text
                                        variant="footnote"
                                        weight="regular"
                                        color="textSecondary"
                                    >
                                        <FormattedMessage
                                            id="currency.bridge.bridge_provider"
                                            defaultMessage="Transfer provider"
                                        />
                                    </Text>
                                    <FeeInputButton
                                        left={
                                            <Row spacing={4}>
                                                <Avatar
                                                    variant="squared"
                                                    size={20}
                                                >
                                                    <Unblock size={20} />
                                                </Avatar>

                                                <Text
                                                    variant="paragraph"
                                                    weight="regular"
                                                    color="textPrimary"
                                                >
                                                    Unblock
                                                </Text>
                                            </Row>
                                        }
                                        right={
                                            <Row spacing={4}>
                                                <Row
                                                    spacing={4}
                                                    alignY="center"
                                                >
                                                    <Text
                                                        variant="paragraph"
                                                        weight="regular"
                                                        color="textPrimary"
                                                    >
                                                        <FormattedMessage
                                                            id="bank_transfers.fees"
                                                            defaultMessage="Fees"
                                                        />
                                                    </Text>
                                                    <DepositFees
                                                        depositFeesPollable={
                                                            pollable
                                                        }
                                                    />
                                                </Row>
                                                <InfoCircleOutline
                                                    size={20}
                                                    color="iconDefault"
                                                />
                                            </Row>
                                        }
                                        onClick={() =>
                                            onMsg({
                                                type: 'on_provider_info_click',
                                            })
                                        }
                                    />
                                </Column>
                                {errors.banner && (
                                    <ErrorBanner error={errors.banner} />
                                )}
                            </Column>
                        </ScrollContainer>
                    </Column>
                    <Actions variant="default">
                        <Button
                            size="regular"
                            variant="primary"
                            disabled={!!errors.submit}
                            onClick={onSubmit}
                        >
                            {errors.submit ? (
                                <SubmitButtonErrorText error={errors.submit} />
                            ) : (
                                <FormattedMessage
                                    id="bank_transfers.deposit.continue"
                                    defaultMessage="Continue"
                                />
                            )}
                        </Button>
                    </Actions>
                </Column>
            </Column>
        </Screen>
    )
}

const SubmitButtonErrorText = ({
    error,
}: {
    error: NonNullable<FormErrors['submit']>
}) => {
    switch (error.type) {
        case 'post_kyc_limit_reached':
            return (
                <FormattedMessage
                    id="bank_transfers.deposit.reduce_amount"
                    defaultMessage="Reduce amount"
                />
            )
        case 'pollable_loading':
            return (
                <FormattedMessage
                    id="bank_transfers.deposit.loading"
                    defaultMessage="Loading"
                />
            )
        case 'amount_is_zero':
            return (
                <FormattedMessage
                    id="bank_transfers.deposit.enter_amount"
                    defaultMessage="Enter Amount"
                />
            )
        case 'minimum_amount':
            return (
                <FormattedMessage
                    id="bank_transfers.deposit.increase-amount"
                    defaultMessage="Minimum transfer is {limit}"
                    values={{
                        limit: (
                            <FormattedMoneyPrecise
                                withSymbol
                                sign={null}
                                money={error.limit}
                            />
                        ),
                    }}
                />
            )
        case 'currency_is_currently_not_supported':
            return (
                <FormattedMessage
                    id="bank_transfers.currency_is_currently_not_supported"
                    defaultMessage="Continue"
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(error)
    }
}

const ErrorBanner = ({ error }: { error: BannerError }) => {
    switch (error.type) {
        case 'post_kyc_limit_reached':
            return (
                <BannerLimit
                    variant="warning"
                    onClick={null}
                    icon={({ size, color }) => (
                        <BoldDangerTriangle size={size} color={color} />
                    )}
                    title={
                        <FormattedMessage
                            id="bank_transfers.deposit.max-limit-reached"
                            defaultMessage="Amount exceeds max transfer limit"
                        />
                    }
                />
            )
        case 'pollable_loading':
            return null
        case 'currency_is_currently_not_supported':
            return (
                <BannerSolid
                    rounded
                    variant="warning"
                    title={
                        <FormattedMessage
                            id="bank_transfers.deposit.currency-not-supported.title"
                            defaultMessage="{code} deposits currently not supported"
                            values={{ code: error.currency.code }}
                        />
                    }
                    subtitle={
                        <FormattedMessage
                            id="bank_transfers.deposit.currency-not-supported.subtitle"
                            defaultMessage="Bank deposits from {code} have been disabled until further notice."
                            values={{ code: error.currency.code }}
                        />
                    }
                />
            )
        /* istanbul ignore next */
        default:
            return notReachable(error)
    }
}
