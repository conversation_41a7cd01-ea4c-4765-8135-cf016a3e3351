import { FormattedMessage } from 'react-intl'

import { Actions } from '@zeal/uikit/Actions'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { Content } from '@zeal/uikit/Content'

import { MsgOf } from '@zeal/toolkit/MsgOf'

import {
    UnblockOnrampCryptoTransferCompletedEvent,
    UnblockOnrampCryptoTransferCompletedNoInfoEvent,
    UnblockOnrampCryptoTransferIssuedEvent,
    UnblockOnrampFailedEvent,
    UnblockOnrampPendingEvent,
    UnblockOnrampTransferApprovedEvent,
    UnblockOnrampTransferInProgress,
    UnblockOnrampTransferInReviewEvent,
    UnblockOnrampTransferOnHoldComplianceEvent,
    UnblockOnrampTransferOnHoldReviewEvent,
    UnblockOnrampTransferReceivedEvent,
} from '@zeal/domains/Currency/domains/BankTransfer'
import { OnRampFeeParams } from '@zeal/domains/Currency/domains/BankTransfer/api/fetchTransactionFee'
import { NetworkMap } from '@zeal/domains/Network'

import { Footer } from './Footer'

import { CryptoPendingSection } from '../CryptoPendingSection'
import { FiatCompletedSection } from '../FiatCompletedSection'

type Props = {
    networkMap: NetworkMap
    event:
        | UnblockOnrampCryptoTransferCompletedEvent
        | UnblockOnrampCryptoTransferCompletedNoInfoEvent
        | UnblockOnrampCryptoTransferIssuedEvent
        | UnblockOnrampFailedEvent
        | UnblockOnrampPendingEvent
        | UnblockOnrampTransferApprovedEvent
        | UnblockOnrampTransferInProgress
        | UnblockOnrampTransferInReviewEvent
        | UnblockOnrampTransferOnHoldComplianceEvent
        | UnblockOnrampTransferOnHoldReviewEvent
        | UnblockOnrampTransferReceivedEvent
    now: number
    startedAt: number
    form: OnRampFeeParams
    onMsg: (msg: Msg) => void
}

type Msg = MsgOf<typeof Footer> | { type: 'on_minimize_click' }

export const CryptoTransferInProgress = ({
    event,
    form,
    networkMap,
    now,
    startedAt,
    onMsg,
}: Props) => {
    return (
        <>
            <Content
                header={
                    <Content.Header
                        title={
                            <FormattedMessage
                                id="currency.bankTransfer.deposit_status.title"
                                defaultMessage="Deposit"
                            />
                        }
                    />
                }
                footer={
                    <Footer
                        event={event}
                        now={now}
                        startedAt={startedAt}
                        onMsg={onMsg}
                    />
                }
            >
                <Column spacing={16}>
                    <FiatCompletedSection event={event} />

                    <CryptoPendingSection
                        event={event}
                        form={form}
                        networkMap={networkMap}
                    />
                </Column>
            </Content>
            <Actions variant="default">
                <Button
                    size="regular"
                    variant="primary"
                    onClick={() =>
                        onMsg({
                            type: 'on_minimize_click',
                        })
                    }
                >
                    <FormattedMessage
                        id="action.minimize"
                        defaultMessage="Minimise"
                    />
                </Button>
            </Actions>
        </>
    )
}
