import React, { useState } from 'react'
import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Avatar as UIAvatar } from '@zeal/uikit/Avatar'
import { Chain } from '@zeal/uikit/Chain'
import { Clickable } from '@zeal/uikit/Clickable'
import { Column } from '@zeal/uikit/Column'
import { Group, GroupHeader, Section } from '@zeal/uikit/Group'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { Monerium } from '@zeal/uikit/Icon/Providers/Monerium'
import { MtPelerin } from '@zeal/uikit/Icon/Providers/MtPelerin'
import { Unblock } from '@zeal/uikit/Icon/Providers/Unblock'
import { ListItem as UIListItem } from '@zeal/uikit/ListItem'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { ScrollContainer } from '@zeal/uikit/ScrollContainer'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { uuid } from '@zeal/toolkit/Crypto'

import { CardSlientSignKeyStore } from '@zeal/domains/Card'
import { DEFAULT_MONERIUM_CRYPTO_CURRENCY } from '@zeal/domains/Card/domains/MoneriumBankTransfer/constants'
import { tryToGetUserTimeZoneCountries } from '@zeal/domains/Country/helpers/tryToGetUserTimeZoneCountries'
import { CryptoCurrency } from '@zeal/domains/Currency'
import { Avatar } from '@zeal/domains/Currency/components/Avatar'
import { GNOSIS_ZCHF } from '@zeal/domains/Currency/constants'
import { BankTransferCurrencies } from '@zeal/domains/Currency/domains/BankTransfer/api/fetchUnblockSupportedCurrencies'
import { KeyStore } from '@zeal/domains/KeyStore'
import { NetworkMap } from '@zeal/domains/Network'
import { Badge } from '@zeal/domains/Network/components/Badge'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { DefaultCurrencyConfig } from '@zeal/domains/Storage'

type Props = {
    unblockSupportedCurrencies: BankTransferCurrencies
    defaultCurrencyConfig: DefaultCurrencyConfig
    networkMap: NetworkMap
    keyStore: KeyStore
    onMsg: (msg: Msg) => void
}

type Msg =
    | { type: 'close' }
    | { type: 'on_monerium_currency_selected'; currency: CryptoCurrency }
    | {
          type: 'on_mt_pelerin_currency_selected'
          currency: CryptoCurrency
          keyStore: CardSlientSignKeyStore
      }
    | { type: 'on_unblock_currency_selected'; currency: CryptoCurrency }

const calculateMtPelerinEligibility = (
    defaultCurrencyConfig: DefaultCurrencyConfig,
    keyStore: KeyStore
):
    | { type: 'eligible'; keyStore: CardSlientSignKeyStore }
    | { type: 'not_eligible' } => {
    switch (keyStore.type) {
        case 'private_key_store':
        case 'safe_4337':
        case 'secret_phrase_key':
            const tzCountries =
                tryToGetUserTimeZoneCountries().getSuccessResult() || []

            return tzCountries.map((c) => c.code).includes('CH') ||
                defaultCurrencyConfig.defaultCurrency.code === 'CHF'
                ? { type: 'eligible', keyStore }
                : { type: 'not_eligible' }
        case 'trezor':
        case 'ledger':
        case 'track_only':
            return { type: 'not_eligible' }

        /* istanbul ignore next */
        default:
            return notReachable(keyStore)
    }
}

export const Layout = ({
    onMsg,
    unblockSupportedCurrencies,
    networkMap,
    defaultCurrencyConfig,
    keyStore,
}: Props) => {
    const [moneriumLabelId] = useState(uuid())
    const [unblockLabelId] = useState(uuid())
    const [mtPelerinLabelId] = useState(uuid())

    const mtPelerinEligibility = calculateMtPelerinEligibility(
        defaultCurrencyConfig,
        keyStore
    )

    return (
        <Screen
            padding="form"
            background="light"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <Column spacing={12} fill shrink>
                <ActionBar
                    left={
                        <Clickable onClick={() => onMsg({ type: 'close' })}>
                            <Row spacing={4}>
                                <BackIcon size={24} color="iconDefault" />

                                <Text
                                    variant="title3"
                                    weight="semi_bold"
                                    color="textPrimary"
                                >
                                    <FormattedMessage
                                        id="bank-transfer-select-service-provider-title"
                                        defaultMessage="Select service provider"
                                    />
                                </Text>
                            </Row>
                        </Clickable>
                    }
                />
                <ScrollContainer withFloatingActions={false}>
                    <Column spacing={8} fill shrink alignY="stretch">
                        <Section aria-labelledby={moneriumLabelId}>
                            <Group variant="widget">
                                <GroupHeader
                                    left={({
                                        color,
                                        textWeight,
                                        textVariant,
                                    }) => (
                                        <Row spacing={4}>
                                            <UIAvatar size={20}>
                                                <Monerium size={20} />
                                            </UIAvatar>
                                            <Text
                                                id={moneriumLabelId}
                                                variant={textVariant}
                                                weight={textWeight}
                                                color={color}
                                            >
                                                Monerium
                                            </Text>
                                        </Row>
                                    )}
                                    right={null}
                                />
                                <OnChainAssetListItem
                                    currency={DEFAULT_MONERIUM_CRYPTO_CURRENCY}
                                    networkMap={networkMap}
                                    onClick={() =>
                                        onMsg({
                                            type: 'on_monerium_currency_selected',
                                            currency:
                                                DEFAULT_MONERIUM_CRYPTO_CURRENCY,
                                        })
                                    }
                                />
                            </Group>
                        </Section>
                        {(() => {
                            switch (mtPelerinEligibility.type) {
                                case 'not_eligible':
                                    return null
                                case 'eligible':
                                    return (
                                        <Section
                                            aria-labelledby={mtPelerinLabelId}
                                        >
                                            <Group variant="widget">
                                                <GroupHeader
                                                    left={({
                                                        color,
                                                        textWeight,
                                                        textVariant,
                                                    }) => (
                                                        <Row spacing={4}>
                                                            <UIAvatar size={20}>
                                                                <MtPelerin
                                                                    size={20}
                                                                />
                                                            </UIAvatar>
                                                            <Text
                                                                id={
                                                                    mtPelerinLabelId
                                                                }
                                                                variant={
                                                                    textVariant
                                                                }
                                                                weight={
                                                                    textWeight
                                                                }
                                                                color={color}
                                                            >
                                                                Mt Pelerin
                                                            </Text>
                                                        </Row>
                                                    )}
                                                    right={null}
                                                />
                                                <OnChainAssetListItem
                                                    currency={GNOSIS_ZCHF}
                                                    networkMap={networkMap}
                                                    onClick={() =>
                                                        onMsg({
                                                            type: 'on_mt_pelerin_currency_selected',
                                                            currency:
                                                                GNOSIS_ZCHF,
                                                            keyStore:
                                                                mtPelerinEligibility.keyStore,
                                                        })
                                                    }
                                                />
                                            </Group>
                                        </Section>
                                    )
                                default:
                                    return notReachable(mtPelerinEligibility)
                            }
                        })()}

                        <Section aria-labelledby={unblockLabelId}>
                            <Group variant="widget">
                                <GroupHeader
                                    left={({
                                        color,
                                        textWeight,
                                        textVariant,
                                    }) => (
                                        <Row spacing={4}>
                                            <UIAvatar size={20}>
                                                <Unblock size={20} />
                                            </UIAvatar>
                                            <Text
                                                id={unblockLabelId}
                                                variant={textVariant}
                                                weight={textWeight}
                                                color={color}
                                            >
                                                Unblock
                                            </Text>
                                        </Row>
                                    )}
                                    right={null}
                                />
                                {unblockSupportedCurrencies.cryptoCurrencies.map(
                                    (currency) => (
                                        <OnChainAssetListItem
                                            key={currency.id}
                                            currency={currency}
                                            networkMap={networkMap}
                                            onClick={() =>
                                                onMsg({
                                                    type: 'on_unblock_currency_selected',
                                                    currency,
                                                })
                                            }
                                        />
                                    )
                                )}
                            </Group>
                        </Section>
                    </Column>
                </ScrollContainer>
            </Column>
        </Screen>
    )
}

const OnChainAssetListItem = ({
    onClick,
    currency,
    networkMap,
}: {
    currency: CryptoCurrency
    networkMap: NetworkMap
    onClick: () => void
}) => {
    const network = findNetworkByHexChainId(
        currency.networkHexChainId,
        networkMap
    )

    return (
        <UIListItem
            size="large"
            onClick={onClick}
            aria-current={false}
            avatar={({ size }) => (
                <Avatar
                    rightBadge={({ size }) => (
                        <Badge network={network} size={size} />
                    )}
                    size={size}
                    currency={currency}
                />
            )}
            primaryText={currency.name}
            shortText={
                <Chain>
                    {currency.symbol} {network.name}
                </Chain>
            }
        />
    )
}
