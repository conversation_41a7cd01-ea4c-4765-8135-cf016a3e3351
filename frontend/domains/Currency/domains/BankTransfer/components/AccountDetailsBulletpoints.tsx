import { FormattedMessage } from 'react-intl'

import { Bridge } from '@zeal/uikit/Icon/Bridge'
import { LightDangerTriangle } from '@zeal/uikit/Icon/LightDangerTriangle'
import { BulletpointsListItem } from '@zeal/uikit/ListItem/BulletpointsListItem'

import { CryptoCurrency, FiatCurrency } from '@zeal/domains/Currency'
import { NetworkMap } from '@zeal/domains/Network'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'

type Props = {
    networkMap: NetworkMap
    inputFiatCurrency: FiatCurrency
    outputCryptoCurrency: CryptoCurrency
}
export const AccountDetailsBulletpoints = ({
    networkMap,
    inputFiatCurrency,
    outputCryptoCurrency,
}: Props) => {
    return (
        <>
            <BulletpointsListItem
                avatar={({ size }) => <Bridge size={size} color="blue30" />}
                text={
                    <FormattedMessage
                        id="monerium-deposit.account-details-info-popup.bullet-point-1"
                        defaultMessage="Any {fiatCurrencyCode} you send to this account will automatically convert into {cryptoCurrencyCode} tokens on {cryptoCurrencyChain} Chain and be sent to your wallet"
                        values={{
                            fiatCurrencyCode: inputFiatCurrency.code,
                            cryptoCurrencyCode: outputCryptoCurrency.code,
                            cryptoCurrencyChain: findNetworkByHexChainId(
                                outputCryptoCurrency.networkHexChainId,
                                networkMap
                            ).name,
                        }}
                    />
                }
                rightIcon={null}
            />
            <BulletpointsListItem
                avatar={({ size }) => (
                    <LightDangerTriangle size={size} color="orange30" />
                )}
                text={
                    <FormattedMessage
                        id="monerium-deposit.account-details-info-popup.bullet-point-2"
                        defaultMessage="ONLY SEND {fiatCurrencyCode} ({fiatCurrencySymbol}) to your account"
                        values={{
                            fiatCurrencyCode: inputFiatCurrency.code,
                            fiatCurrencySymbol: inputFiatCurrency.symbol,
                        }}
                    />
                }
                rightIcon={null}
            />
        </>
    )
}
