import { FormattedMessage } from 'react-intl'

import { ActionBar } from '@zeal/uikit/ActionBar'
import { Column } from '@zeal/uikit/Column'
import { Group } from '@zeal/uikit/Group'
import { Header } from '@zeal/uikit/Header'
import { BoldDiscount } from '@zeal/uikit/Icon/BoldDiscount'
import { BoldGeneralBank } from '@zeal/uikit/Icon/BoldGeneralBank'
import { CloseCross } from '@zeal/uikit/Icon/CloseCross'
import { Unblock } from '@zeal/uikit/Icon/Providers/Unblock'
import { ShieldDone } from '@zeal/uikit/Icon/ShieldDone'
import { IconButton } from '@zeal/uikit/IconButton'
import { BulletpointsListItem } from '@zeal/uikit/ListItem/BulletpointsListItem'
import { Popup } from '@zeal/uikit/Popup'
import { Tertiary } from '@zeal/uikit/Tertiary'
import { Text } from '@zeal/uikit/Text'

import { openExternalURL } from '@zeal/toolkit/Window'

import { UNBLOCK_URL } from '@zeal/domains/Currency/domains/BankTransfer/constants'

type Msg = { type: 'close' }

type Props = {
    onMsg: (msg: Msg) => void
}

export const UnblockProviderInfoPopup = ({ onMsg }: Props) => {
    return (
        <Popup.Layout onMsg={onMsg}>
            <Column spacing={0}>
                <ActionBar
                    right={
                        <IconButton
                            variant="on_light"
                            onClick={() => onMsg({ type: 'close' })}
                        >
                            {({ color }) => (
                                <CloseCross size={24} color={color} />
                            )}
                        </IconButton>
                    }
                />
                <Column spacing={24} fill>
                    <Header
                        icon={({ size }) => <Unblock size={size} />}
                        title="Unblock"
                    />
                    <Group variant="default">
                        <BulletpointsListItem
                            avatar={({ size }) => (
                                <BoldGeneralBank size={size} color="green30" />
                            )}
                            text={
                                <FormattedMessage
                                    id="unblockProviderInfo.registration"
                                    defaultMessage="Unblock is registered & authorised by FNTT to provide VASP exchange and custody services, and is a registered MSB provider with US FinCEN. <link>Learn more</link>"
                                    values={{
                                        link: (chunks) => (
                                            <Tertiary
                                                color="on_light"
                                                size="regular"
                                                onClick={() =>
                                                    openExternalURL(UNBLOCK_URL)
                                                }
                                            >
                                                {({
                                                    color,
                                                    textVariant,
                                                    textWeight,
                                                }) => (
                                                    <Text
                                                        textDecorationLine="underline"
                                                        color={color}
                                                        variant={textVariant}
                                                        weight={textWeight}
                                                    >
                                                        {chunks}
                                                    </Text>
                                                )}
                                            </Tertiary>
                                        ),
                                    }}
                                />
                            }
                            rightIcon={null}
                        />
                        <BulletpointsListItem
                            avatar={({ size }) => (
                                <BoldDiscount size={size} color="teal40" />
                            )}
                            text={
                                <FormattedMessage
                                    id="unblockProviderInfo.fees"
                                    defaultMessage="You get the lowest fees possible: 0% up to $5k per month and 0.2% above that."
                                />
                            }
                            rightIcon={null}
                        />
                        <BulletpointsListItem
                            avatar={({ size }) => (
                                <ShieldDone size={size} color="blue30" />
                            )}
                            text={
                                <FormattedMessage
                                    id="unblockProviderInfo.selfCustody"
                                    defaultMessage="The digital cash that you receive is self-custodied and no one else will have control over your assets"
                                />
                            }
                            rightIcon={null}
                        />
                    </Group>
                </Column>
            </Column>
        </Popup.Layout>
    )
}
