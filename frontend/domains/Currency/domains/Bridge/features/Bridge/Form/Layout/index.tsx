import { FormattedMessage, useIntl } from 'react-intl'

import { ActionBar as UIActionBar } from '@zeal/uikit/ActionBar'
import { Actions } from '@zeal/uikit/Actions'
import { Button } from '@zeal/uikit/Button'
import { Clickable } from '@zeal/uikit/Clickable'
import { Column } from '@zeal/uikit/Column'
import { FancyButton } from '@zeal/uikit/FancyButton'
import { CloseCross } from '@zeal/uikit/Icon/Actions/CloseCross'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { LightArrowDown2 } from '@zeal/uikit/Icon/LightArrowDown2'
import { SolidInterfacePlus } from '@zeal/uikit/Icon/SolidInterfacePlus'
import { IconButton } from '@zeal/uikit/IconButton'
import { AmountInput } from '@zeal/uikit/Input/AmountInput'
import { NextStepSeparator } from '@zeal/uikit/NextStepSeparator'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { ScrollContainer } from '@zeal/uikit/ScrollContainer'
import { Skeleton } from '@zeal/uikit/Skeleton'
import { Spacer } from '@zeal/uikit/Spacer'
import { Tertiary } from '@zeal/uikit/Tertiary'
import { Text } from '@zeal/uikit/Text'

import { noop, notReachable } from '@zeal/toolkit'
import { fromFixedWithFraction } from '@zeal/toolkit/BigInt'
import { LazyLoadableData } from '@zeal/toolkit/LoadableData/LazyLoadableData'
import { MsgOf } from '@zeal/toolkit/MsgOf'
import * as Web3 from '@zeal/toolkit/Web3'

import { AvatarWithoutBadge as AccountAvatar } from '@zeal/domains/Account/components/Avatar'
import { CurrenciesMatrix } from '@zeal/domains/Currency/api/fetchCurrenciesMatrix'
import { Avatar as CurrencyAvatar } from '@zeal/domains/Currency/components/Avatar'
import { MaxButton } from '@zeal/domains/Currency/components/MaxButton'
import {
    BridgePollable,
    BridgeRequest,
} from '@zeal/domains/Currency/domains/Bridge'
import { applyRate2 } from '@zeal/domains/FXRate/helpers/applyRate'
import { FiatMoney } from '@zeal/domains/Money'
import { FormattedMoneyPrecise } from '@zeal/domains/Money/components/FormattedMoneyPrecise'
import { useMoneyFormat } from '@zeal/domains/Money/hooks/useMoneyFormat'
import { NetworkMap } from '@zeal/domains/Network'
import { FancyButton as NetworkFancyButton } from '@zeal/domains/Network/components/FancyButton'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'
import { ServerPortfolio2 } from '@zeal/domains/Portfolio'
import { getTokenByCryptoCurrency3 } from '@zeal/domains/Portfolio/helpers/getTokenByCryptoCurrency'

import { ErrorMessageBanner } from './ErrorMessageBanner'
import { ErrorMessageButton } from './ErrorMessageButton'
import { Route } from './Route'
import {
    getBridgeRequest,
    getMaxBalance,
    MaxBalanceLoadable,
    validate,
} from './validation'

type Props = {
    serverPortfolio: ServerPortfolio2
    pollable: BridgePollable
    maxBalanceLoadable: MaxBalanceLoadable
    gasFeeLoadable: LazyLoadableData<BridgeRequest, unknown>
    currenciesMatrix: CurrenciesMatrix
    networkMap: NetworkMap
    installationId: string
    onMsg: (msg: Msg) => void
}

export type Msg =
    | { type: 'close' }
    | { type: 'on_from_network_click' }
    | { type: 'on_to_network_click' }
    | { type: 'on_from_currency_click' }
    | { type: 'on_to_currency_click' }
    | { type: 'on_refuel_add_click' }
    | { type: 'on_refuel_remove_click' }
    | { type: 'on_amount_change'; amount: string | null }
    | { type: 'on_bridge_continue_clicked'; route: BridgeRequest }
    | { type: 'on_fields_switch_clicked'; bridgeRequest: BridgeRequest | null }
    | { type: 'on_slippage_clicked' }
    | MsgOf<typeof Route>
    | MsgOf<typeof ErrorMessageBanner>

export const Layout = ({
    pollable,
    serverPortfolio,
    gasFeeLoadable,
    currenciesMatrix,
    networkMap,
    maxBalanceLoadable,
    installationId,
    onMsg,
}: Props) => {
    const { formattedCryptoMoneyPrecise } = useMoneyFormat()
    const { formatMessage, formatNumber } = useIntl()
    const { fromAccount, fromCurrency, toCurrency } = pollable.params

    const fromNetwork = findNetworkByHexChainId(
        fromCurrency.networkHexChainId,
        networkMap
    )
    const toNetwork = findNetworkByHexChainId(
        toCurrency.networkHexChainId,
        networkMap
    )

    const refuelAvailable =
        currenciesMatrix.currencies[fromNetwork.hexChainId]?.[
            toNetwork.hexChainId
        ]?.canRefuel || false

    const validationResult = validate({
        pollable,
        serverPortfolio,
        maxBalanceLoadable,
    })
    const errors = validationResult.getFailureReason() || {}

    const bridgeRequest = (() => {
        switch (pollable.type) {
            case 'loaded':
            case 'reloading':
            case 'subsequent_failed':
                return getBridgeRequest({
                    quote: pollable.data,
                    selectedRouteName: pollable.params.bridgeRouteName,
                })

            case 'loading':
            case 'error':
                return null

            default:
                return notReachable(pollable)
        }
    })()

    const toAmount = bridgeRequest
        ? formattedCryptoMoneyPrecise({
              money: {
                  amount: bridgeRequest.route.to.amount,
                  currency: toCurrency,
              },
              withSymbol: false,
              sign: null,
          })
        : null

    const fromToken = getTokenByCryptoCurrency3({
        currency: fromCurrency,
        serverPortfolio,
    })

    const fromAmountInDefaultCurrency: FiatMoney | null =
        fromToken.rate && pollable.params.fromAmount
            ? applyRate2({
                  baseAmount: {
                      amount: fromFixedWithFraction(
                          pollable.params.fromAmount,
                          fromCurrency.fraction
                      ),
                      currency: fromCurrency,
                  },
                  rate: fromToken.rate,
              })
            : null

    const refuelCurrency =
        (bridgeRequest && bridgeRequest.targetRefuelCurrency) || null

    const isRefuelAvailable =
        refuelAvailable &&
        refuelCurrency &&
        !pollable.params.refuel &&
        refuelCurrency.id !== toCurrency.id

    return (
        <Screen
            background="light"
            padding="form"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <UIActionBar
                top={
                    <Row spacing={8}>
                        <AccountAvatar size={24} account={fromAccount} />
                        <Text
                            variant="footnote"
                            color="textSecondary"
                            weight="regular"
                            ellipsis
                        >
                            {fromAccount.label}
                        </Text>

                        <Text
                            variant="footnote"
                            color="textSecondary"
                            weight="regular"
                        >
                            {Web3.address.format(fromAccount.address)}
                        </Text>
                    </Row>
                }
                left={
                    <Clickable onClick={() => onMsg({ type: 'close' })}>
                        <Row spacing={4}>
                            <BackIcon size={24} color="iconDefault" />
                            <Text
                                variant="title3"
                                weight="semi_bold"
                                color="textPrimary"
                            >
                                <FormattedMessage
                                    id="currency.bridge.title"
                                    defaultMessage="Bridge"
                                />
                            </Text>
                        </Row>
                    </Clickable>
                }
            />

            <Column spacing={16} fill>
                <Column spacing={12} fill>
                    <ScrollContainer contentFill withFloatingActions={false}>
                        <Column spacing={8} fill>
                            <Column spacing={4}>
                                <AmountInput
                                    label={formatMessage({
                                        id: 'currency.bridge.from',
                                        defaultMessage: 'From',
                                    })}
                                    state={
                                        errors.fromToken ? 'error' : 'normal'
                                    }
                                    top={
                                        <NetworkFancyButton
                                            fill
                                            rounded={false}
                                            network={fromNetwork}
                                            onClick={() =>
                                                onMsg({
                                                    type: 'on_from_network_click',
                                                })
                                            }
                                        />
                                    }
                                    content={{
                                        topLeft: (
                                            <IconButton
                                                variant="on_light"
                                                onClick={() => {
                                                    onMsg({
                                                        type: 'on_from_currency_click',
                                                    })
                                                }}
                                            >
                                                {({ color: _color }) => (
                                                    <Row spacing={4}>
                                                        <CurrencyAvatar
                                                            key={
                                                                fromCurrency.id
                                                            }
                                                            currency={
                                                                fromCurrency
                                                            }
                                                            size={24}
                                                            rightBadge={() =>
                                                                null
                                                            }
                                                        />
                                                        <Text
                                                            variant="title3"
                                                            color="textPrimary"
                                                            weight="medium"
                                                        >
                                                            {fromCurrency.code}
                                                        </Text>
                                                        <LightArrowDown2
                                                            size={18}
                                                            color="iconDefault"
                                                        />
                                                    </Row>
                                                )}
                                            </IconButton>
                                        ),
                                        topRight: ({ onBlur, onFocus }) => (
                                            <AmountInput.Input
                                                onBlur={onBlur}
                                                onFocus={onFocus}
                                                label={formatMessage({
                                                    id: 'currency.bridge.amount_label',
                                                    defaultMessage:
                                                        'Amount to bridge',
                                                })}
                                                amount={
                                                    pollable.params
                                                        .fromAmount || null
                                                }
                                                fraction={fromCurrency.fraction}
                                                onChange={(value) =>
                                                    onMsg({
                                                        type: 'on_amount_change',
                                                        amount: value,
                                                    })
                                                }
                                                autoFocus
                                                prefix=""
                                                onSubmitEditing={noop}
                                            />
                                        ),
                                        bottomLeft: (
                                            <MaxButton
                                                installationId={installationId}
                                                location="bridge"
                                                balance={getMaxBalance({
                                                    maxBalanceLoadable,
                                                    serverPortfolio,
                                                })}
                                                onMsg={onMsg}
                                                state={(() => {
                                                    switch (
                                                        maxBalanceLoadable.type
                                                    ) {
                                                        case 'loading':
                                                            return 'loading'
                                                        case 'error':
                                                        case 'loaded':
                                                            return errors.fromToken
                                                                ? 'error'
                                                                : 'normal'
                                                        default:
                                                            return notReachable(
                                                                maxBalanceLoadable
                                                            )
                                                    }
                                                })()}
                                            />
                                        ),
                                        bottomRight:
                                            fromAmountInDefaultCurrency && (
                                                <Text
                                                    variant="footnote"
                                                    color="textSecondary"
                                                    weight="regular"
                                                >
                                                    <FormattedMoneyPrecise
                                                        withSymbol
                                                        sign={null}
                                                        money={
                                                            fromAmountInDefaultCurrency
                                                        }
                                                    />
                                                </Text>
                                            ),
                                    }}
                                    bottom={
                                        pollable.params.refuel && (
                                            <RefuelFrom
                                                pollable={pollable}
                                                onRemoveRefuelClick={() =>
                                                    onMsg({
                                                        type: 'on_refuel_remove_click',
                                                    })
                                                }
                                            />
                                        )
                                    }
                                />

                                <NextStepSeparator
                                    onClick={() =>
                                        onMsg({
                                            type: 'on_fields_switch_clicked',
                                            bridgeRequest,
                                        })
                                    }
                                />

                                <AmountInput
                                    label={formatMessage({
                                        id: 'currency.bridge.to',
                                        defaultMessage: 'To',
                                    })}
                                    state="normal"
                                    top={
                                        <NetworkFancyButton
                                            fill
                                            rounded={false}
                                            network={toNetwork}
                                            onClick={() =>
                                                onMsg({
                                                    type: 'on_to_network_click',
                                                })
                                            }
                                        />
                                    }
                                    content={{
                                        topLeft: (
                                            <IconButton
                                                variant="on_light"
                                                onClick={() => {
                                                    onMsg({
                                                        type: 'on_to_currency_click',
                                                    })
                                                }}
                                            >
                                                {({ color }) => (
                                                    <>
                                                        <CurrencyAvatar
                                                            key={toCurrency.id}
                                                            currency={
                                                                toCurrency
                                                            }
                                                            size={24}
                                                            rightBadge={() =>
                                                                null
                                                            }
                                                        />
                                                        <Text
                                                            variant="title3"
                                                            color="textPrimary"
                                                            weight="medium"
                                                        >
                                                            {toCurrency.code}
                                                        </Text>

                                                        <LightArrowDown2
                                                            size={18}
                                                            color={color}
                                                        />
                                                    </>
                                                )}
                                            </IconButton>
                                        ),
                                        topRight: ({ onBlur, onFocus }) =>
                                            (() => {
                                                switch (pollable.type) {
                                                    case 'loading':
                                                    case 'reloading':
                                                        return (
                                                            <AmountInput.InputSkeleton />
                                                        )
                                                    case 'loaded':
                                                    case 'subsequent_failed':
                                                    case 'error':
                                                        return (
                                                            <AmountInput.Input
                                                                onBlur={onBlur}
                                                                onFocus={
                                                                    onFocus
                                                                }
                                                                label={formatMessage(
                                                                    {
                                                                        id: 'currency.swap.destination_amount',
                                                                        defaultMessage:
                                                                            'Destination amount',
                                                                    }
                                                                )}
                                                                amount={
                                                                    toAmount
                                                                }
                                                                fraction={
                                                                    toCurrency.fraction ??
                                                                    0
                                                                }
                                                                onChange={noop}
                                                                prefix=""
                                                                readOnly
                                                                onSubmitEditing={
                                                                    noop
                                                                }
                                                            />
                                                        )
                                                    /* istanbul ignore next */
                                                    default:
                                                        return notReachable(
                                                            pollable
                                                        )
                                                }
                                            })(),
                                        bottomRight: bridgeRequest?.route
                                            .toPriceInDefaultCurrency && (
                                            <Text
                                                variant="footnote"
                                                color="textSecondary"
                                                weight="regular"
                                            >
                                                <FormattedMoneyPrecise
                                                    withSymbol
                                                    sign={null}
                                                    money={
                                                        bridgeRequest?.route
                                                            .toPriceInDefaultCurrency
                                                    }
                                                />
                                            </Text>
                                        ),
                                    }}
                                    bottom={
                                        pollable.params.refuel && (
                                            <RefuelTo
                                                pollable={pollable}
                                                onRemoveRefuelClick={() =>
                                                    onMsg({
                                                        type: 'on_refuel_remove_click',
                                                    })
                                                }
                                            />
                                        )
                                    }
                                />
                            </Column>

                            {isRefuelAvailable && (
                                <Row spacing={8} alignX="end">
                                    <FancyButton
                                        right={null}
                                        color="secondary"
                                        rounded
                                        left={({
                                            color,
                                            textVariant,
                                            textWeight,
                                        }) => (
                                            <Row spacing={4}>
                                                <SolidInterfacePlus
                                                    size={16}
                                                    color={color}
                                                />
                                                <Text
                                                    color={color}
                                                    variant={textVariant}
                                                    weight={textWeight}
                                                >
                                                    <FormattedMessage
                                                        id="currency.bridge.topup"
                                                        defaultMessage="Top up {symbol}"
                                                        values={{
                                                            symbol: refuelCurrency.symbol,
                                                        }}
                                                    />
                                                </Text>
                                            </Row>
                                        )}
                                        onClick={() =>
                                            onMsg({
                                                type: 'on_refuel_add_click',
                                            })
                                        }
                                    />
                                </Row>
                            )}

                            <Spacer />
                            <Column spacing={8}>
                                <Row spacing={4} alignX="stretch">
                                    <Text
                                        variant="footnote"
                                        weight="regular"
                                        color="textSecondary"
                                    >
                                        <FormattedMessage
                                            id="currency.bridge.bridge_settings"
                                            defaultMessage="Bridge settings"
                                        />
                                    </Text>

                                    <Tertiary
                                        color="on_light"
                                        size="small"
                                        onClick={() =>
                                            onMsg({
                                                type: 'on_slippage_clicked',
                                            })
                                        }
                                    >
                                        {({
                                            color,
                                            textVariant,
                                            textWeight,
                                        }) => (
                                            <Row spacing={4}>
                                                <Text
                                                    color={color}
                                                    variant={textVariant}
                                                    weight={textWeight}
                                                >
                                                    <FormattedMessage
                                                        id="SelectRoute.slippage"
                                                        defaultMessage="Slippage {slippage}"
                                                        values={{
                                                            slippage:
                                                                formatNumber(
                                                                    pollable
                                                                        .params
                                                                        .slippagePercent /
                                                                        100,
                                                                    {
                                                                        // TODO @resetko-zeal use getFormattedPercentage
                                                                        style: 'percent',
                                                                        minimumFractionDigits: 0,
                                                                        maximumFractionDigits: 2,
                                                                    }
                                                                ),
                                                        }}
                                                    />
                                                </Text>
                                                <LightArrowDown2
                                                    size={16}
                                                    color={color}
                                                />
                                            </Row>
                                        )}
                                    </Tertiary>
                                </Row>
                                <Route pollable={pollable} onMsg={onMsg} />
                            </Column>
                        </Column>
                    </ScrollContainer>

                    <ErrorMessageBanner
                        currency={fromCurrency}
                        error={errors.submit}
                        gasFeeLoadable={gasFeeLoadable}
                        onMsg={onMsg}
                    />

                    <Actions variant="default">
                        <Button
                            variant="primary"
                            size="regular"
                            disabled={(() => {
                                if (!!errors.submit) {
                                    return true
                                }
                                switch (gasFeeLoadable.type) {
                                    case 'loading':
                                        return true
                                    case 'not_asked':
                                    case 'loaded':
                                    case 'error':
                                        return false

                                    /* istanbul ignore next */
                                    default:
                                        return notReachable(gasFeeLoadable)
                                }
                            })()}
                            onClick={() => {
                                switch (validationResult.type) {
                                    case 'Failure':
                                        break
                                    case 'Success':
                                        onMsg({
                                            type: 'on_bridge_continue_clicked',
                                            route: validationResult.data,
                                        })
                                        break
                                    /* istanbul ignore next */
                                    default:
                                        notReachable(validationResult)
                                }
                            }}
                        >
                            {(() => {
                                switch (validationResult.type) {
                                    case 'Failure':
                                        return (
                                            <ErrorMessageButton
                                                error={
                                                    validationResult.reason
                                                        .submit
                                                }
                                            />
                                        )
                                    case 'Success':
                                        return (
                                            <FormattedMessage
                                                id="action.continue"
                                                defaultMessage="Continue"
                                            />
                                        )
                                    /* istanbul ignore next */
                                    default:
                                        return notReachable(validationResult)
                                }
                            })()}
                        </Button>
                    </Actions>
                </Column>
            </Column>
        </Screen>
    )
}

const RefuelSkeleton = () => (
    <Row spacing={8} alignX="stretch">
        <Skeleton variant="default" width={35} height={16} />
        <Skeleton variant="default" width={55} height={16} />
    </Row>
)
const RefuelFrom = ({
    pollable,
    onRemoveRefuelClick,
}: {
    pollable: BridgePollable
    onRemoveRefuelClick: () => void
}) => {
    const { formatMessage } = useIntl()
    if (!pollable.params.refuel) {
        return null
    }

    switch (pollable.type) {
        case 'loaded': {
            const bridgeRequest = getBridgeRequest({
                quote: pollable.data,
                selectedRouteName: pollable.params.bridgeRouteName,
            })

            if (!bridgeRequest) {
                return null
            }

            const refuel = bridgeRequest.route.refuel

            if (!refuel) {
                return null
            }

            const currency = refuel.from.currency

            return (
                currency && (
                    <Row
                        spacing={8}
                        aria-labelledby="refuel-from-label"
                        aria-describedby="refuel-from-description"
                    >
                        <Row spacing={4}>
                            <CurrencyAvatar
                                rightBadge={() => null}
                                size={20}
                                currency={currency}
                            />

                            <Text
                                id="refuel-from-label"
                                color="textPrimary"
                                variant="paragraph"
                                weight="regular"
                            >
                                {currency.symbol}
                            </Text>
                        </Row>

                        <Spacer />

                        <Text
                            color="textPrimary"
                            variant="paragraph"
                            weight="regular"
                            id="refuel-from-description"
                        >
                            <FormattedMoneyPrecise
                                withSymbol={false}
                                sign="-"
                                money={refuel.from}
                            />
                        </Text>

                        <Tertiary
                            color="on_light"
                            size="regular"
                            onClick={onRemoveRefuelClick}
                            aria-label={formatMessage({
                                id: 'bridge.remove_topup',
                                defaultMessage: 'Remove top-up',
                            })}
                        >
                            {({ color }) => (
                                <CloseCross size={20} color={color} />
                            )}
                        </Tertiary>
                    </Row>
                )
            )
        }
        case 'reloading':
        case 'loading':
            return <RefuelSkeleton />

        case 'subsequent_failed':
        case 'error':
            return null

        /* istanbul ignore next */
        default:
            return notReachable(pollable)
    }
}

const RefuelTo = ({
    pollable,
    onRemoveRefuelClick,
}: {
    pollable: BridgePollable
    onRemoveRefuelClick: () => void
}) => {
    const { formatMessage } = useIntl()
    if (!pollable.params.refuel) {
        return null
    }

    switch (pollable.type) {
        case 'loaded': {
            const bridgeRequest = getBridgeRequest({
                quote: pollable.data,
                selectedRouteName: pollable.params.bridgeRouteName,
            })

            if (!bridgeRequest) {
                return null
            }

            const refuel = bridgeRequest.route.refuel

            if (!refuel) {
                return null
            }

            const currency = refuel.to.currency

            return (
                currency && (
                    <Row
                        spacing={8}
                        aria-labelledby="refuel-to-label"
                        aria-describedby="refuel-to-description"
                    >
                        <Row spacing={4}>
                            <CurrencyAvatar
                                rightBadge={() => null}
                                size={20}
                                currency={currency}
                            />

                            <Text
                                color="textPrimary"
                                variant="paragraph"
                                weight="regular"
                                id="refuel-to-label"
                            >
                                {currency.symbol}
                            </Text>
                        </Row>

                        <Spacer />

                        <Text
                            color="textPrimary"
                            variant="paragraph"
                            weight="regular"
                            id="refuel-to-description"
                        >
                            <FormattedMoneyPrecise
                                withSymbol={false}
                                sign="+"
                                money={refuel.to}
                            />
                        </Text>

                        <Tertiary
                            color="on_light"
                            size="regular"
                            aria-label={formatMessage({
                                id: 'bridge.remove_topup',
                                defaultMessage: 'Remove top-up',
                            })}
                            onClick={onRemoveRefuelClick}
                        >
                            {({ color }) => (
                                <CloseCross size={20} color={color} />
                            )}
                        </Tertiary>
                    </Row>
                )
            )
        }
        case 'reloading':
        case 'loading':
            return <RefuelSkeleton />

        case 'subsequent_failed':
        case 'error':
            return null

        /* istanbul ignore next */
        default:
            return notReachable(pollable)
    }
}
