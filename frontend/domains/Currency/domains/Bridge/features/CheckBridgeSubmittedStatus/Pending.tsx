import { useEffect } from 'react'
import { FormattedMessage, useIntl } from 'react-intl'

import { Actions } from '@zeal/uikit/Actions'
import { Button } from '@zeal/uikit/Button'
import { Column } from '@zeal/uikit/Column'
import { Content } from '@zeal/uikit/Content'
import { GroupHeader, Section } from '@zeal/uikit/Group'
import { BackIcon } from '@zeal/uikit/Icon/BackIcon'
import { ExternalLink } from '@zeal/uikit/Icon/ExternalLink'
import { IconButton } from '@zeal/uikit/IconButton'
import { Progress } from '@zeal/uikit/Progress'
import { Row } from '@zeal/uikit/Row'
import { Screen } from '@zeal/uikit/Screen'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { useCurrentTimestamp } from '@zeal/toolkit/Date/useCurrentTimestamp'
import { useReadableDistance } from '@zeal/toolkit/Date/useReadableDistance'
import { useLoadedPollableData } from '@zeal/toolkit/LoadableData/LoadedPollableData'
import { RangeInt } from '@zeal/toolkit/Range'
import { useLiveRef } from '@zeal/toolkit/React'

import { Account } from '@zeal/domains/Account'
import { ActionBar as AccountActionBar } from '@zeal/domains/Account/components/ActionBar'
import {
    BridgeSubmitted,
    BridgeSubmittedLoadedPollable,
    RequestState,
} from '@zeal/domains/Currency/domains/Bridge'
import { fetchBridgeRequestStatus } from '@zeal/domains/Currency/domains/Bridge/api/fetchBridgeRequestStatus'
import { BridgeRouteFromListItem } from '@zeal/domains/Currency/domains/Bridge/components/BridgeRouteFromListItem'
import {
    HeaderSubtitle,
    HeaderTitle,
} from '@zeal/domains/Currency/domains/Bridge/components/BridgeRouteHeader'
import { BridgeRouteToListItem } from '@zeal/domains/Currency/domains/Bridge/components/BridgeRouteToListItem'
import { openExplorerLink } from '@zeal/domains/Currency/domains/Bridge/helpers/openExplorerLink'
import { KeyStoreMap } from '@zeal/domains/KeyStore'
import { getKeyStore } from '@zeal/domains/KeyStore/helpers/getKeyStore'
import { NetworkMap } from '@zeal/domains/Network'
import { FancyButton } from '@zeal/domains/Network/components/FancyButton'
import { findNetworkByHexChainId } from '@zeal/domains/Network/constants'

type Props = {
    networkMap: NetworkMap
    account: Account
    keystoreMap: KeyStoreMap
    bridgeSubmitted: BridgeSubmitted
    onMsg: (msg: Msg) => void
}

type Msg = { type: 'close' } | { type: 'bridge_completed' }

export const Pending = ({
    bridgeSubmitted,
    keystoreMap,
    account,
    networkMap,
    onMsg,
}: Props) => {
    const { formatMessage } = useIntl()
    const formatHumanReadableDistance = useReadableDistance()
    const useLiveMsg = useLiveRef(onMsg)
    const [pollable] = useLoadedPollableData(
        fetchBridgeRequestStatus,
        {
            type: 'loaded',
            params: {
                request: bridgeSubmitted,
            },
            data: {
                refuel: bridgeSubmitted.route.refuel
                    ? { type: 'pending' as const }
                    : null,
                targetTransaction: { type: 'pending' as const },
            },
        },
        {
            pollIntervalMilliseconds: 10_000,
        }
    )

    const now = useCurrentTimestamp({ refreshIntervalMs: 1000 })

    const timePassed = now - bridgeSubmitted.submittedAtMS
    const progressPercentage =
        (timePassed / bridgeSubmitted.route.serviceTimeMs) * 100

    const roundedProgressPercentage = Math.round(progressPercentage)

    const timePassedPercentage: RangeInt<0, 100> = Math.min(
        100,
        Math.max(0, roundedProgressPercentage)
    ) as RangeInt<0, 100>

    useEffect(() => {
        switch (pollable.data.targetTransaction.type) {
            case 'pending':
            case 'not_started':
                return
            case 'completed':
                if (!bridgeSubmitted.route.refuel) {
                    useLiveMsg.current({ type: 'bridge_completed' })
                    return
                }
                if (!pollable.data.refuel) {
                    return
                }

                switch (pollable.data.refuel.type) {
                    case 'pending':
                    case 'not_started':
                        return
                    case 'completed':
                        useLiveMsg.current({ type: 'bridge_completed' })
                        return
                    /* istanbul ignore next */
                    default:
                        return notReachable(pollable.data.refuel.type)
                }

            /* istanbul ignore next */
            default:
                return notReachable(pollable.data.targetTransaction.type)
        }
    }, [pollable, bridgeSubmitted, useLiveMsg])

    const toCurrency = bridgeSubmitted.route.to.currency
    const fromCurrency = bridgeSubmitted.route.from.currency

    const fromNetwork = findNetworkByHexChainId(
        fromCurrency.networkHexChainId,
        networkMap
    )

    const toNetwork = findNetworkByHexChainId(
        toCurrency.networkHexChainId,
        networkMap
    )

    return (
        <Screen
            padding="form"
            background="light"
            onNavigateBack={() => onMsg({ type: 'close' })}
        >
            <AccountActionBar
                keystore={getKeyStore({
                    keyStoreMap: keystoreMap,
                    address: account.address,
                })}
                network={null}
                account={account}
                left={
                    <IconButton
                        variant="on_light"
                        onClick={() => onMsg({ type: 'close' })}
                        aria-label={formatMessage({
                            id: 'action.close',
                            defaultMessage: 'Close',
                        })}
                    >
                        {({ color }) => <BackIcon size={24} color={color} />}
                    </IconButton>
                }
            />
            <Content
                header={
                    <Content.Header
                        title={<HeaderTitle />}
                        subtitle={
                            <HeaderSubtitle
                                bridgeRoute={bridgeSubmitted.route}
                            />
                        }
                    />
                }
                footer={
                    <Column spacing={4}>
                        <Progress
                            variant="neutral"
                            title={
                                <FormattedMessage
                                    id="bridge.check_status.progress_text"
                                    defaultMessage="Bridging {from} to {to}"
                                    values={{
                                        from: fromCurrency.symbol,
                                        to: toCurrency.symbol,
                                    }}
                                />
                            }
                            right={
                                <Row spacing={4} alignX="center">
                                    <Text
                                        variant="paragraph"
                                        weight="regular"
                                        color="textStatusNeutralOnColor"
                                    >
                                        {`${formatHumanReadableDistance(
                                            now - bridgeSubmitted.submittedAtMS,
                                            'floor'
                                        )} / ${formatHumanReadableDistance(
                                            bridgeSubmitted.route.serviceTimeMs
                                        )}`}
                                    </Text>

                                    <IconButton
                                        variant="on_light"
                                        size="small"
                                        onClick={() =>
                                            openExplorerLink(bridgeSubmitted)
                                        }
                                    >
                                        {({ color }) => (
                                            <Row spacing={4} alignX="center">
                                                <Text
                                                    variant="paragraph"
                                                    weight="regular"
                                                    color="textStatusNeutralOnColor"
                                                >
                                                    0x
                                                </Text>

                                                <ExternalLink
                                                    size={14}
                                                    color="textStatusNeutralOnColor"
                                                />
                                            </Row>
                                        )}
                                    </IconButton>
                                </Row>
                            }
                            initialProgress={0}
                            progress={timePassedPercentage}
                        />
                        <Actions variant="default">
                            <Button
                                size="regular"
                                variant="primary"
                                onClick={() => onMsg({ type: 'close' })}
                            >
                                <FormattedMessage
                                    id="action.minimize"
                                    defaultMessage="Minimise"
                                />
                            </Button>
                        </Actions>
                    </Column>
                }
            >
                <Column spacing={16}>
                    <Section>
                        <GroupHeader
                            left={({ color, textVariant, textWeight }) => (
                                <Text
                                    color={color}
                                    variant={textVariant}
                                    weight={textWeight}
                                >
                                    <FormattedMessage
                                        id="currency.bridge.bridge_from"
                                        defaultMessage="From"
                                    />
                                </Text>
                            )}
                            right={() => (
                                <FancyButton
                                    rounded={true}
                                    network={fromNetwork}
                                    onClick={null}
                                />
                            )}
                        />
                        <BridgeRouteFromListItem
                            networkMap={networkMap}
                            bridgeRoute={bridgeSubmitted.route}
                            requestStatus={{ type: 'completed' }}
                        />
                    </Section>
                    <Section>
                        <GroupHeader
                            left={({ color, textVariant, textWeight }) => (
                                <Text
                                    color={color}
                                    variant={textVariant}
                                    weight={textWeight}
                                >
                                    <FormattedMessage
                                        id="currency.bridge.bridge_to"
                                        defaultMessage="To"
                                    />
                                </Text>
                            )}
                            right={() => (
                                <FancyButton
                                    rounded={true}
                                    network={toNetwork}
                                    onClick={null}
                                />
                            )}
                        />
                        <BridgeRouteToListItem
                            networkMap={networkMap}
                            bridgeRoute={bridgeSubmitted.route}
                            bridgeStatus={{
                                targetTransaction:
                                    pollable.data.targetTransaction,
                                refuel:
                                    bridgeSubmitted.route.refuel &&
                                    getRefuelStatus(pollable),
                            }}
                        />
                    </Section>
                </Column>
            </Content>
        </Screen>
    )
}

const getRefuelStatus = (
    pollable: BridgeSubmittedLoadedPollable
): RequestState => {
    if (!pollable.data.refuel) {
        return { type: 'pending' }
    }
    return pollable.data.refuel
}
