import { FormattedMessage } from 'react-intl'

import { BannerSolid } from '@zeal/uikit/BannerSolid'
import { Column } from '@zeal/uikit/Column'
import { Divider } from '@zeal/uikit/Divider'
import { BoldTickRound } from '@zeal/uikit/Icon/BoldTickRound'
import { ExternalLink } from '@zeal/uikit/Icon/ExternalLink'
import { IconButton } from '@zeal/uikit/IconButton'
import { Progress as UIKitProgress } from '@zeal/uikit/Progress'
import { Row } from '@zeal/uikit/Row'
import { Text } from '@zeal/uikit/Text'

import { notReachable } from '@zeal/toolkit'
import { useReadableDistance } from '@zeal/toolkit/Date/useReadableDistance'
import { openExternalURL } from '@zeal/toolkit/Window'

import { SwapsIOSwapRequest } from '@zeal/domains/Currency/domains/SwapsIO'
import { GNOSIS } from '@zeal/domains/Network/constants'
import { getExplorerLink } from '@zeal/domains/Transactions/domains/TransactionHash/helpers/getExplorerLink'

type Props = {
    now: number
    startedAt: number
    swapRequest: SwapsIOSwapRequest
}

const MAX_TRANSACTION_INTERVAL = 60_000

export const Footer = ({ now, startedAt, swapRequest }: Props) => {
    return (
        <Column spacing={0}>
            <Progress
                now={now}
                startedAt={startedAt}
                swapRequest={swapRequest}
            />

            <Divider variant="default" />

            <Banner swapRequest={swapRequest} />
        </Column>
    )
}

const Progress = ({
    swapRequest,
    now,
    startedAt,
}: {
    now: number
    startedAt: number
    swapRequest: SwapsIOSwapRequest
}) => {
    switch (swapRequest.state) {
        case 'completed_liq_sent':
            return (
                <UIKitProgress
                    variant="success"
                    initialProgress={60}
                    progress={100}
                    title={
                        <FormattedMessage
                            id="swapsIO.monitoring.completed.title"
                            defaultMessage="Completed"
                        />
                    }
                    right={
                        <BoldTickRound size={16} color="iconStatusSuccess" />
                    }
                />
            )
        case 'completed_sent':
            const url = getExplorerLink(
                {
                    transactionHash: swapRequest.toTransactionHash,
                },
                GNOSIS
            )
            return (
                <UIKitProgress
                    variant="success"
                    initialProgress={60}
                    progress={100}
                    title={
                        <FormattedMessage
                            id="swapsIO.monitoring.completed.title"
                            defaultMessage="Completed"
                        />
                    }
                    right={
                        <Row spacing={4}>
                            <BoldTickRound size={16} color="green30" />
                            <IconButton
                                variant="on_color"
                                size="small"
                                onClick={() => url && openExternalURL(url)}
                            >
                                {({ color }) => (
                                    <Row spacing={4} alignX="center">
                                        <Text
                                            variant="paragraph"
                                            weight="regular"
                                        >
                                            0x
                                        </Text>

                                        <ExternalLink size={14} />
                                    </Row>
                                )}
                            </IconButton>
                        </Row>
                    }
                />
            )

        case 'cancelled_no_slash':
            return (
                <UIKitProgress
                    variant="warning"
                    initialProgress={80}
                    progress={100}
                    title={
                        <FormattedMessage
                            id="swapsIO.monitoring.cancelledNoSlash.title"
                            defaultMessage="Tokens returned"
                        />
                    }
                />
            )

        case 'cancelled_awaiting_slash':
            return (
                <UIKitProgress
                    variant="warning"
                    initialProgress={60}
                    progress={80}
                    title={
                        <FormattedMessage
                            id="swapsIO.monitoring.cancelledAwaitingSlash.title"
                            defaultMessage="Returning tokens"
                        />
                    }
                />
            )

        case 'cancelled_slashed':
            return (
                <UIKitProgress
                    variant="warning"
                    initialProgress={80}
                    progress={100}
                    title={
                        <FormattedMessage
                            id="swapsIO.monitoring.cancelledSlashed.title"
                            defaultMessage="Tokens returned"
                        />
                    }
                />
            )

        case 'awaiting_liq_send':
            return (
                <UIKitProgress
                    variant="neutral"
                    initialProgress={60}
                    progress={60}
                    title={
                        <FormattedMessage
                            id="swapsIO.monitoring.awaitingLiqSend.title"
                            defaultMessage="Delayed"
                        />
                    }
                    right={<ProgressRight now={now} startedAt={startedAt} />}
                />
            )

        case 'awaiting_signature':
        case 'awaiting_receive':
            return (
                <UIKitProgress
                    variant="neutral"
                    initialProgress={0}
                    progress={30}
                    title={
                        <FormattedMessage
                            id="swapsIO.monitoring.awaitingRecive.title"
                            defaultMessage="Relaying"
                        />
                    }
                    right={<ProgressRight now={now} startedAt={startedAt} />}
                />
            )

        case 'awaiting_send':
            return (
                <UIKitProgress
                    variant="neutral"
                    initialProgress={30}
                    progress={60}
                    title={
                        <FormattedMessage
                            id="swapsIO.monitoring.awaitingSend.title"
                            defaultMessage="Queuing"
                        />
                    }
                    right={<ProgressRight now={now} startedAt={startedAt} />}
                />
            )

        /* istanbul ignore next */
        default:
            return notReachable(swapRequest)
    }
}

const ProgressRight = ({
    now,
    startedAt,
}: {
    now: number
    startedAt: number
}) => {
    const formatHumanReadableDistance = useReadableDistance()
    return (
        <Text>
            {`${formatHumanReadableDistance(
                now - startedAt,
                'floor'
            )} / ${formatHumanReadableDistance(MAX_TRANSACTION_INTERVAL)}`}
        </Text>
    )
}

const Banner = ({ swapRequest }: { swapRequest: SwapsIOSwapRequest }) => {
    switch (swapRequest.state) {
        case 'cancelled_no_slash':
            return (
                <BannerSolid
                    variant="warning"
                    title={null}
                    subtitle={
                        <FormattedMessage
                            id="swapsIO.monitoring.cancelledNoSlash.subtitle"
                            defaultMessage="Tokens have not been transferred due to an unknown error. Please try again."
                        />
                    }
                />
            )
        case 'cancelled_slashed':
            return (
                <BannerSolid
                    variant="warning"
                    title={null}
                    subtitle={
                        <FormattedMessage
                            id="swapsIO.monitoring.cancelledSlashed.subtitle"
                            defaultMessage="Tokens have been returned. Kinetex couldn’t complete the destination transaction."
                        />
                    }
                />
            )
        case 'cancelled_awaiting_slash':
            return (
                <BannerSolid
                    variant="neutral"
                    title={null}
                    subtitle={
                        <FormattedMessage
                            id="swapsIO.monitoring.cancelledAwaitingSlash.subtitle"
                            defaultMessage="Tokens were sent to Kinetex, but will be returned soon. Kinetex couldn’t complete the destination transaction."
                        />
                    }
                />
            )

        case 'awaiting_liq_send':
            return (
                <BannerSolid
                    variant="neutral"
                    title={null}
                    subtitle={
                        <FormattedMessage
                            id="swapsIO.monitoring.AwaitingLiqSend.subtitle"
                            defaultMessage="Deposit should complete soon. Kinetex is still processing your transaction."
                        />
                    }
                />
            )
        case 'completed_liq_sent':
        case 'awaiting_signature':
        case 'awaiting_send':
        case 'awaiting_receive':
        case 'completed_sent':
            return null

        /* istanbul ignore next */
        default:
            return notReachable(swapRequest)
    }
}
