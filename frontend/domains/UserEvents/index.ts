import { notReachable } from '@zeal/toolkit'
import { Hexadecimal } from '@zeal/toolkit/Hexadecimal'
import { RangeInt } from '@zeal/toolkit/Range'

import {
    CardSafeState,
    GnosisPayAccountConfigurationState,
    GnosisPayKYCApprovedState,
    GnosisPayOnboardedKycStatus,
    GnosisPayPostKYCApprovedState,
    GnosisPayPreKYCApprovedKycStatus,
} from '@zeal/domains/Card'
import { BReward } from '@zeal/domains/Card/domains/Reward'
import { Currency, CurrencyId, FiatCurrencyCode } from '@zeal/domains/Currency'
import { TakerType } from '@zeal/domains/Earn'
import { KeyStore } from '@zeal/domains/KeyStore'
import { Diagnostics } from '@zeal/domains/Storage'
import {
    CustomPresetRequestFee,
    EstimatedFee,
    PredefinedPreset,
} from '@zeal/domains/Transactions/domains/FeeForecast'
import { GasAbstractionTransactionFee } from '@zeal/domains/UserOperation'

type EventKeystoreType =
    | 'Ledger'
    | 'Trezor'
    | 'SecretPhrase'
    | 'PrivateKey'
    | 'Contact'
    | 'Safe'

// TODO @resetko-zeal check if this is needed
export type RPCType = 'rpc' | 'biconomyBundler' | 'pimlicoBundler'

export type GasPaymentMethod =
    | { type: 'native' }
    | { type: 'sponsored' }
    | { type: 'erc20'; token: string }

export type NotificationsEvent = {
    type:
        | 'NotificationsEnabledEvent'
        | 'NotificationsSkippedEvent'
        | 'NotificationsGoToSettingsPopupOpenedEvent'
        | 'NotificationsSettingsClickedEvent'
    location:
        | 'card_onboarding'
        | 'card_settings'
        | 'notifications_settings'
        | 'add_wallet'
        | 'bank_transfers_setup'
        | 'onboarding'
}

export type NotificationSentEvent = {
    type: 'NotificationSentEvent'
    notificationType: string
}

export type NotificationReceivedEvent = {
    type: 'NotificationReceivedEvent'
    notificationType: string
}

export type PasskeyAppAssociationCheckFailedEvent = {
    type: 'PasskeyAppAssociationCheckFailedEvent'
    network: string
    location: 'wallet_creation' | 'wallet_recovery' | 'transaction_signing'
}

export type PasskeyOperationCouldNotBePerformedEvent = {
    type: 'PasskeyOperationCouldNotBePerformedEvent'
    error: string
    location: 'wallet_creation' | 'wallet_recovery' | 'transaction_signing'
}

export type MoneriumSetupFlowEnteredEvent = {
    type: 'MoneriumSetupFlowEnteredEvent'
    location: 'card_tab_banner' | 'bank_transfer'
}

type MoneriumBankTransferEvents =
    | { type: 'MoneriumAddRecipientEnteredEvent' }
    | { type: 'MoneriumAddRecipientCompletedEvent' }
    | { type: 'MoneriumTransferInitiatedEvent' }
    | { type: 'MoneriumTransferCompletedEvent' }
    | { type: 'MoneriumTransferRejectedEvent' }

type TransactionEvents =
    | {
          type: 'TransactionInitiatedEvent'
          keystoreId: string
          network: string
          keystoreType: EventKeystoreType
          source: string | null
      }
    | {
          /**
           * @deprecated use TransactionInitiatedEvent instead when user clicks the button, and not this one when loadable is triggered
           */
          type: 'TransactionRequestedEvent'
          keystoreId: string
          network: string
          keystoreType: EventKeystoreType
          source: string | null
          rpcType: RPCType
      }
    | {
          type: 'TransactionSubmittedEvent'
          network: string
          keystoreId: string
          keystoreType: EventKeystoreType
          source: string | null
          gasPaymentMethod: GasPaymentMethod | null
      }
    | {
          type: 'TransactionReplacementRequestedEvent'
          network: string
          keystoreId: string
          keystoreType: EventKeystoreType
          source: string | null
          replacementType: 'cancel' | 'speedup' | null
      }
    | {
          type: 'TransactionTakesLongEvent'
          keystoreType: EventKeystoreType
          source: string | null
          network: string
          selectedFee:
              | EstimatedFee
              | CustomPresetRequestFee
              | GasAbstractionTransactionFee
          timestempt: number
      }
    | {
          type: 'TransactionFailedEvent'
          transactionHash: Hexadecimal | null
          keystoreType: EventKeystoreType
          state: 'failed' | 'rejected' | 'refunded' | 'cancelled' | 'expired'
          source: string | null
          network: string
          takesMS: number
      }
    | {
          type: 'TransactionCompletedEvent'
          keystoreType: EventKeystoreType
          source: string | null
          network: string
          takesMS: number
      }

type PortfolioEvents =
    | {
          type: 'PortfolioLoadingEvent'
          keystoreId: string
          keystoreType: EventKeystoreType
      }
    | {
          type: 'PortfolioLoadingFailedEvent'
          keystoreId: string
          keystoreType: EventKeystoreType
      }

type ActivityEvents = {
    type: 'ActivityDiscrepanciesDetectedEvent'
    indexerMissingTransactions: string[]
    debankMissingTransactions: string[]
}

export type HelpButtonLocation =
    | 'stripped_home'
    | 'home'
    | 'settings'
    | 'unblock_bank_deposit_screen'
    | 'monerium_bank_deposit_screen'
    | 'gnosis_card_order'
    | 'card_settings'
    | 'add_to_wallet'
    | 'select_earn_account'
    | 'referral_code_screen'
    | 'card_activation'
    | 'select_recharge_threshold'
    | 'mt_pelerin_monerium_bank_transfer_fork'

export type HelpButtonClickedEvent = {
    type: 'HelpButtonClickedEvent'
    supportVariant: 'intercom' | 'zendesk'
    location: HelpButtonLocation
}

type CardOrderFlowLocation =
    | 'virtual_card_order_flow'
    | 'physical_card_order_flow'

export type ConfigureCardSafeLocation =
    | CardOrderFlowLocation
    | 'physical_card_activation_flow'
    | 'card_tab_temporary' // FIXME :: @Nicvaniek remove

export type DeployCardSafeLocation =
    | CardOrderFlowLocation
    | 'physical_card_activation_flow'
    | 'monerium_bank_transfers'
    | 'onboarding'
    | 'card_tab_temporary' // FIXME :: @Nicvaniek remove

export type SetCardCurrencyLocation =
    | CardOrderFlowLocation
    | 'physical_card_activation_flow'
    | 'monerium_bank_transfers'
    | 'card_tab_temporary' // FIXME :: @Nicvaniek remove

export type PhysicalCardActivationLocation =
    | 'card_settings'
    | 'card_tab'
    | 'deep_link'

type CardTopupEvent =
    | { type: 'CardTopupFlowEnteredEvent' }
    | { type: 'CardTopupSelectSenderClickedEvent' }

export type UserEvent =
    | {
          type: 'VisibilityChangeToVisbleEvent'
          enviroment: string
          zealPlatform: string
      }
    | {
          type: 'AppsflyerAttributionDeepLinkReceivedEvent'
          campaign: string | null
          media_source: string | null
          utm_campaign: string | null
          utm_channel: string | null
      }
    | {
          type: 'AppsflyerAttributionDeepLinkMissingEvent'
          rawData: unknown
      }
    | { type: 'AppDeepLinkReceivedEvent'; feature: string }
    | PortfolioEvents
    | HelpButtonClickedEvent
    | { type: 'ConnectionRequestedEvent' }
    | { type: 'ConnectionToggledToMetamaskEvent' }
    | {
          type: 'AppsFetchedEvent'
          location: 'onboarding' | 'home'
          apps: string[]
      }
    | { type: 'WalletUninstalledEvent' }
    | { type: 'AddFundsFromAnyWalletEvent' }
    | { type: 'TopUpDappOpenedEvent' }
    | { type: 'TopUpTransactionSubmittedEvent'; network: string }
    | { type: 'TopUpTransactionSubmittedEvent'; network: string }
    | { type: 'DAppConnectedEvent'; host: string }
    | { type: 'DAppOpenedEvent'; host: string }
    | TransactionEvents
    | ActivityEvents
    | {
          type: 'PortfolioDiscrepanciesDetectedEvent'
          indexerMissingTokens: string[]
          debankMissingTokens: string[]
      }
    | {
          type: 'ActionBannerClickedEvent'

          action: string
      }
    | NotificationsEvent
    | NotificationSentEvent
    | NotificationReceivedEvent
    | CardEvent
    | CardTopupEvent
    | DefaultCurrencyEvents
    | MoneriumBankTransferEvents
    | { type: 'BankTransferCountrySelectorEnteredEvent' }
    | {
          type: 'BankTransferCountrySelectedEvent'
          country: string
          providerSupport:
              | 'monerium_only'
              | 'unblock_only'
              | 'monerium_and_unblock'
              | 'not_supported'
      }
    | {
          type: 'NotificationsDisabledEvent'
          location: 'card_settings'
      }
    | {
          type: 'CardNotificationsSettingsClickedEvent'
      }
    | {
          type: 'ActionBannerDismissedEvent'
          action: string
      }
    | {
          type: 'BankTransferFlowEnteredEvent'
      }
    | {
          type: 'BankTransferOffRampAccountChangedEvent'
          location: 'bank_withdrawal_screen' | 'bank_settings_screen'
          currency: string
          accountId: string
      }
    | {
          type: 'BankTransferOffRampSubmittedEvent'
          inputToken: string
          outputCurrency: string
      }
    | {
          type: 'TransferFromExchangeEnteredEvent'
      }
    | {
          type: 'PasswordCreationFlowEntered'
      }
    | {
          type: 'PasswordConfirmationFlowEnteredEvent'
      }
    | {
          type: 'PasswordConfirmedEvent'
      }
    | { type: 'ConnectionManagerOpenInNoWeb3SiteEvent' }
    | {
          type: 'PortfolioLoadedEvent'

          zealPlatform: string
          enviroment: string
          isFunded: boolean
          tokenCount: number
          dappCount: number
          nftCount: number
          keystoreType: EventKeystoreType
          keystoreId: string
          tz: string | null
      }
    | {
          type: 'RecoveryKitStartedEvent'
          action: 'googleDrive' | 'manual'
      }
    | {
          type: 'RecoveryKitCreatedEvent'
          action: 'googleDrive' | 'manual'
      }
    | {
          type: 'UserSurveyAnsweredEvent'
          survey: string
          answer: string
      }
    | {
          type: 'ZwidgetOpenedEvent'
          state: 'disconnected' | 'connected' | 'metamask'
          location: 'extension' | 'dapp'
      }
    | {
          type: 'ActivityEnteredEvent'
      }
    | {
          type: 'AppDisconnectedEvent'
          location: 'settings' | 'zwidget' | 'wallet_connect'
          scope: 'all' | 'single'
      }
    | {
          type: 'AssetHiddenEvent'
          assetType: 'token'
      }
    | {
          type: 'AssetStarredEvent'
          assetType: 'token'
      }
    | {
          type: 'AssetUnhiddenEvent'
          assetType: 'token'
      }
    | {
          type: 'AssetUnstarredEvent'
          assetType: 'token'
      }
    | { type: 'StrippedHomeScreenEnteredEvent' }
    | {
          type: 'BridgeFlowEnteredEvent'
          location:
              | 'actions_modal'
              | 'token_actions_modal'
              | 'portfolio_quick_actions'
      }
    | {
          type: 'BridgeFlowNoRoutesFoundCancelClickEvent'
          fromNetworkName: string
          toNetworkName: string
      }
    | {
          type: 'SwapFlowEnteredEvent'
          location:
              | 'actions_modal'
              | 'token_actions_modal'
              | 'portfolio_quick_actions'
      }
    | {
          type: 'ReceiveFlowEnteredEvent'
          location:
              | 'token_actions_modal'
              | 'actions_modal'
              | 'add_funds_modal'
              | 'wallet_details'
              | 'nba'
              | 'portfolio_quick_actions'
              | 'portfolio_tokens_widget_zero_state'
      }
    | { type: 'ConnectedNetworkSelectedEvent' }
    | { type: 'ConnectedNetworkSelectorEnteredEvent' }
    | {
          type: 'CopyAddress'
          location:
              | 'wallet_details'
              | 'portfolio'
              | 'wallet_list'
              | 'send'
              | 'card_wallet_details'
              | 'add_card_owner_guide'
              | 'settings'
      }
    | {
          type: 'WalletInstalledEvent'
          os: string
          userAgent: string
          tz: string | null
      }
    | {
          type: 'WalletAddedEvent'
          keystoreType: EventKeystoreType
          keystoreId: string
      }
    | {
          type: 'ConnectionAcceptedEvent'
          keystoreType: EventKeystoreType
          keystoreId: string
          network: string | null
      }
    | {
          type: 'SendFlowEnteredEvent'
          location:
              | 'actions_modal'
              | 'token_actions_modal'
              | 'nft_view'
              | 'portfolio_quick_actions'
          asset: 'nft' | 'token'
      }
    | {
          type: 'BuyFlowEnteredEvent'
          location: 'portfolio_quick_actions'
      }
    | { type: 'SendRecipientQRCodeSelectedEvent' }
    | {
          type: 'SendRecipientSelectedEvent'
          recipientType: 'bank' | 'wallet'
      }
    | {
          type: 'StoryFlowStartedEvent'
          name:
              | 'onboarding'
              | 'safe'
              | 'bank_transfers'
              | 'how_to_connect_to_metamask'
              | 'how_to_connect'
      }
    | {
          type: 'StoryFlowAdvancedEvent'
          name:
              | 'onboarding'
              | 'safe'
              | 'bank_transfers'
              | 'how_to_connect_to_metamask'
              | 'how_to_connect'
          slideNumber: number
      }
    | {
          type: 'StoryFlowFinishedEvent'
          name:
              | 'onboarding'
              | 'safe'
              | 'bank_transfers'
              | 'how_to_connect_to_metamask'
              | 'how_to_connect'
      }
    | {
          type: 'StoryFlowDismissedEvent'
          name:
              | 'onboarding'
              | 'safe'
              | 'bank_transfers'
              | 'how_to_connect_to_metamask'
              | 'how_to_connect'
      }
    | {
          type: 'ExistingWalletFlowEnteredEvent'
          name: 'onboarding'
      }
    | {
          type: 'CreateWalletClickedEvent'
          name: 'onboarding'
      }
    | { type: 'AppListEnteredEvent' }
    | { type: 'ConnectionListEnteredEvent' }
    | { type: 'ExpandedViewEnteredEvent'; location: 'settings' | 'portfolio' }
    | { type: 'SettingsEnteredEvent' }
    | { type: 'FilterFlowEnteredEvent'; location: FilterFlowLocation }
    | { type: 'FilterAppliedEvent' }
    | { type: 'NFTListEnteredEvent' }
    | { type: 'TokenListEnteredEvent'; variant: 'all_tokens' | 'cash_tokens' }
    | { type: 'BrowserOpenedEvent' }
    | {
          type: 'DappLinkClickedEvent'
          dapp: string
          location: 'portfolio' | 'browser' | 'defi'
      }
    | { type: 'GnosisHomepageEnteredEvent' }
    | { type: 'ClickJumperButtonEvent' }
    | { type: 'MobileBrowserFlowEnteredEvent' }
    | { type: 'MobileBrowserOpenedEvent' }
    | {
          type: 'UserExperiencedAppErrorEvent'
          errorType: string
          errorDetail: string
          source: string
      }
    | {
          type: 'UserExperiencedAppCrashEvent'
          errorDetail: string
          errorType: string
      }
    | { type: 'FetchPinDecryptionFailureEvent' }
    | { type: 'LockScreenBiometricsReEnrolledEvent' }
    | {
          type: 'BiometricRequestEvent'
          status: 'unavailable' | 'disabled' | 'enabled'
      }
    | { type: 'LabeledWalletEvent' }
    | { type: 'SmartWalletCreationFlowEnteredEvent' }
    | PasskeyOperationCouldNotBePerformedEvent
    | { type: 'PasskeyCreatedEvent' }
    | { type: 'VerifyPasskeyClickedEvent' }
    | { type: 'DeploySmartWalletEvent'; provider: 'pimlico' | 'biconomy' }
    | { type: 'SmartWalletDeployedEvent' }
    | { type: '1PasswordDetectedEvent' }
    | {
          type: 'EarnFlowEnteredEvent'
          location: 'portfolio_screen' | 'card_screen' | 'recharge_screen'
          earnStatus: 'enabled' | 'disabled'
          keystoreType: EventKeystoreType
      }
    | {
          type: 'EarnSelectAssetEnteredEvent'
          earnStatus: 'enabled' | 'disabled'
          keystoreType: EventKeystoreType
      }
    | {
          type: 'EarnAssetDetailsEnteredEvent'
          asset: 'usd' | 'eur' | 'eth' | 'chf'
          location: 'home_screen' | 'earn_screen' | 'card_order_flow'
      }
    | {
          type: 'EarnDepositButtonClickedEvent'
          asset: 'usd' | 'eur' | 'eth' | 'chf'
          location: 'investment_tile' | 'investment_details_screen'
      }
    | {
          type: 'EarnAssetDetailsMoreInfoClickedEvent'
          asset: 'usd' | 'eur' | 'eth' | 'chf'
          tooltip:
              | 'lending_operations'
              | 'market_making_operations'
              | 'treasuries_operations'
              | 'base_currency'
              | 'faq_how_is_backed'
              | 'faq_can_lose_principal'
              | 'faq_high_returns'
              | 'faq_ftx_difference'
              | 'faq_insurance'
      }
    | {
          type: 'EarnAccountCreatedEvent'
          asset: 'usd' | 'eur' | 'eth' | 'chf'
      }
    | {
          type: 'EarnDepositFlowEnteredEvent'
          location: EarnEventLocation

          asset: 'usd' | 'eur' | 'eth' | 'chf'
          source: 'zeal' | 'external'
      }
    | {
          type: 'EarnDepositInitiatedEvent'
          asset: 'usd' | 'eur' | 'eth' | 'chf'
          source: 'zeal' | 'external'
      }
    | {
          type: 'EarnDepositCompletedEvent'
          asset: 'usd' | 'eur' | 'eth' | 'chf'
          source: 'zeal' | 'external'
      }
    | { type: 'EarnDepositFromAccountChanged' }
    | {
          type: 'EarnWithdrawEarnFlowEnteredEvent'
          location: 'earn_asset_modal' | 'earn_screen'
          asset: 'usd' | 'eur' | 'eth' | 'chf'
      }
    | {
          type: 'EarnWithdrawInitiatedEvent'
          asset: 'usd' | 'eur' | 'eth' | 'chf'
      }
    | {
          type: 'EarnWithdrawCompletedEvent'
          asset: 'usd' | 'eur' | 'eth' | 'chf'
      }
    | {
          type: 'RechargeConfigFlowEnteredEvent'
          rechargeState: 'on' | 'off'
          location:
              | 'card_screen'
              | 'earn_screen'
              | 'portfolio_screen'
              | 'card_topup'
      }
    | {
          type: 'RechargeSaveInitiatedEvent'
          amount: string
          rechargeAssets: TakerType[]
      }
    | {
          type: 'GasSettingUpdatedEvent'
          oldPredefinedPreset: PredefinedPreset | { type: 'Custom' }
          newPredefinedPreset: PredefinedPreset | { type: 'Custom' }
      }
    | {
          type: 'GasFlowEnteredEvent'
      }
    | {
          type: 'RechargeSaveCompletedEvent'
          amount: string
          rechargeAssets: TakerType[]
      }
    | { type: 'RechargeDisabledEvent' }
    | PasskeyAppAssociationCheckFailedEvent
    | {
          type: 'CashbackFlowEnteredEvent'
          cashBackStatus: 'has_cashback' | 'no_cashback'
      }
    | {
          type: 'CashbackDepositFlowEnteredEvent'
          location: 'cashback_screen' | 'cashback_asset_modal'
          asset: 'gno' | 'usd'
      }
    | {
          type: 'CashbackDepositInitiatedEvent'
          asset: 'gno' | 'usd'
      }
    | {
          type: 'CashbackDepositCompletedEvent'
          asset: 'gno' | 'usd'
      }
    | {
          type: 'CashbackWithdrawalFlowEnteredEvent'
          location: 'cashback_screen' | 'cashback_asset_modal'
      }
    | {
          type: 'CashbackWithdrawalInitiatedEvent'
      }
    | {
          type: 'CashbackWithdrawalOwnerNotLinkedEvent'
      }
    | {
          type: 'TransactionSimulationBackClickedEvent'
          keystoreType: EventKeystoreType
          network: string
          keystoreId: string
      }
    | {
          type: 'UserClickedCashbackWidgetWhileLoadingEvent'
          location: 'card_screen'
      }
    | {
          type: 'BankSettingsFlowEnteredEvent'
      }
    | {
          type: 'BankSettingsChangeDepositReceiverEnteredEvent'
          location: 'bank_deposit_screen' | 'bank_settings_screen'
      }
    | {
          type: 'BankSettingsChangeWithdrawalBankAccountEnteredEvent'
          location: 'bank_withdrawal_screen' | 'bank_settings_screen'
      }
    | { type: 'AppUpdateSkippedEvent' }
    | {
          type: 'PortfolioLoadedStaticCurrencies'
          scam: number
          total: number
          fetch: number
      }
    | { type: 'DiagnosticsSentEvent'; diagnostics: Diagnostics }
    | {
          type: 'PortfolioLoadedCurrenciesWithNullableRates'
          currenciesIds: CurrencyId[]
      }
    | { type: 'MoneriumCardNotSetupEvent' }
    | { type: 'MoneriumCardNotSetupEvent' }
    | { type: 'MoneriumSetupCardEvent' }
    | { type: 'MoneriumSetupCardLoginEvent' }
    | { type: 'MoneriumSetupCardCreateAccountEvent' }
    | { type: 'UserNotEligibleForMoneriumEvent' }
    | { type: 'MoneriumBankDetailsOpenedEvent'; token: string }
    | { type: 'MoneriumDepositCompletedEvent'; token: string }
    | { type: 'MoneriumSetupEvent' }
    | MoneriumSetupFlowEnteredEvent
    | { type: 'MoneriumReceiverSwitchEvent' }
    | {
          type: 'MoneriumExistingAccountEvent'
          keystoreType: EventKeystoreType
          hasCardSafeSignature: boolean
      }
    | AddFundsEvents
    | {
          type: 'DepositRedirectInitiatedEvent'
          earnAsset: 'usd' | 'eur' | 'chf' | 'eth'
          toEarn: string
          toCard: string
      }
    | {
          type: 'DepositRedirectTargetUpdatedEvent'
          previousAsset: 'usd' | 'eur' | 'chf' | 'eth'
          newAsset: 'usd' | 'eur' | 'chf' | 'eth'
      }
    | {
          type: 'BuyFlowAssetSelectedEvent'
          asset: {
              id: string
              code: string
          }
      }
    | MaxButtonClickedEvent
    | { type: 'InAppReviewNotAvailableEvent' }
    | { type: 'InAppReviewOpenedEvent'; location: ZealRatingLocation }
    | { type: 'InAppReviewFailedToOpenEvent'; location: ZealRatingLocation }
    | { type: 'InAppReviewErrorEvent'; location: ZealRatingLocation }
    | {
          type: 'LeaveFeedbackEvent'
          location: ZealRatingLocation | HelpButtonLocation
      }
    | ReferralUserAEvents
    | ReferralUserBEvents

export type ReferralUserAEvents =
    | { type: 'RewardsConfigurationEnteredEvent' }
    | { type: 'RewardsConfiguredEvent' }
    | { type: 'RewardsConfigurationFailedEvent' }
    | { type: 'RewardsEnteredEvent' }
    | {
          type: 'RewardsStateUpdated'
          prevRewardState: BReward
          newRewardState: BReward
      }
    | { type: 'ReferralLinkCopiedEvent' }
    | { type: 'ReferralShareModalOpenedEvent' }
    | { type: 'ReferralShareModalCompletedEvent'; activityType: string | null }
    | { type: 'ReferralShareModalCancelledEvent' }
    | { type: 'ReferralShareModalErrorEvent' }
    | {
          type: 'ReferralRewardCelebrationEvent'
          location: 'push_notification' | 'rewards_tab'
      }

type ReferralUserBEvents =
    | {
          type: 'ReferralLinkOpenedEvent'
          referralCode: string
          referrerInstallationId: string | null
          utm_channel: string | null
      }
    | { type: 'ReferralAttributionMissingEvent' }
    | {
          type: 'ActivationRewardCelebrationEvent'
          currency: string
          amount: string
          installCampaign: 'dappcon' | 'referralBase' | 'activationBase' | null // TODO @resetko-zeal store initial and reattributed campaigns separately https://linear.app/zeal/issue/ZEAL-4074
          rewardCampaign: 'dappcon' | 'referralBase' | 'activationBase' | null
      }

export type EarnEventLocation =
    | 'earn_screen'
    | 'earn_asset_modal'
    | 'configure_recharge'
    | 'portfolio_screen'
    | 'redirect_money_to_card'

export type FilterFlowLocation =
    | 'swap_form'
    | 'bridge_form_from'
    | 'bridge_form_to'
    | 'activity_screen'
    | 'token_list'
    | 'nft_list'
    | 'dapp_connection'
    | 'select_currency_and_network'
    | 'card_top_up'
    | 'apps_list'

export type MaxButtonClickedEvent = {
    type: 'MaxButtonClickedEvent'
    location:
        | 'send'
        | 'swap'
        | 'bridge'
        | 'earn_deposit'
        | 'earn_withdrawal'
        | 'cashback_withdrawal'
        | 'card_withdrawal'
        | 'monerium_withdrawal'
        | 'unblock_withdrawal'
        | 'card_add_cash'
        | 'buy'
    asset: string
}

export type SwapsioEventSource = 'buy' | 'earn' | 'card'

type CardEvent =
    | {
          type: 'CardEnteredEvent'
          cardOnboardedStatus: 'enabled' | 'disabled' | 'pending'
          location: 'navbar' | 'portfolio_screen'
          keystoreType: EventKeystoreType
      }
    | {
          type: 'AddCashToCardEvent'
          action:
              | 'select_wallet'
              | 'show_card_address'
              | 'swaps_entered'
              | 'send_to_card_entered'
      }
    | {
          type: 'SwapsioCompletedEvent'
          source: SwapsioEventSource
      }
    | {
          type: 'SwapsioFailedEvent'
          source: SwapsioEventSource
      }
    | {
          type: 'SwapsioSignatureEvent'
          action: 'accept' | 'cancel'
          source: SwapsioEventSource
      }
    | { type: 'CardOrderFlowEnteredEvent'; state: 'new' | 'existing' }
    | {
          type: 'CardImportSuccessEvent'
          keystoreType: EventKeystoreType
          keystoreId: string
      }
    | {
          type: 'ClickOrderCardButtonEvent'
          location:
              | 'card_tab_actions'
              | 'gnosis_pay_kyc_approved_screen'
              | 'card_settings'
      }
    | { type: 'ClickActivateCardButtonEvent' }
    | { type: 'ClickActivateCardBannerEvent' }
    | {
          type: 'ClickCardQuickActionsButtonEvent'
          action: 'add_cash' | 'details' | 'freeze' | 'withdraw'
      }
    | { type: 'CardWithdrawSuccessfullyRecivedEvent' }
    | {
          type: 'GnosisPaySignupEmailFormEnteredEvent'
          location: GnosisPaySignupLocation
      }
    | {
          type: 'GnosisPaySignupOTPFormEnteredEvent'
          location: GnosisPaySignupLocation
      }
    | {
          type: 'GnosisPaySignupAccountCreatedEvent'
          location: GnosisPaySignupLocation
      }
    | {
          type: 'GnosisPayKycFlowEnteredEvent'
          location: GnosisPayOnboardingLocation
          gnosisAccountState:
              | {
                    type: 'not_onboarded'
                    userId: string
                    status:
                        | GnosisPayPreKYCApprovedKycStatus
                        | 'terms_not_accepted'
                }
              | {
                    type: 'onboarded'
                    userId: string
                    kycStatus: GnosisPayOnboardedKycStatus
                }
      }
    | {
          type: 'GnosisPayKYCStepCompletedEvent'
          step: string
          location: GnosisPayOnboardingLocation
      }
    | {
          type: 'GnosisPayKYCCompletedEvent'
          flow: 'onboarding' | 're_kyc'
          result: 'kyc_approved' | 'kyc_failed'
          location: GnosisPayOnboardingLocation | GnosisPaySignupLocation
      }
    | { type: 'GnosisPayAcceptTermsEvent'; terms: string[] }

    // Card Settings section
    | { type: 'ClickCardSettingButtonEvent' }
    | { type: 'ClickCardSetSpendLimitButtonEvent' }
    | { type: 'CardSetSpendLimitChangedEvent' }
    | { type: 'ChangeCardOwnerClickedEvent' }
    | { type: 'AddCardOwnerClickedEvent' }
    | { type: 'RemoveCardOwnerClickedEvent' }
    | { type: 'CardReadonlySignerIsInUseEvent' }

    // Celebration
    | {
          type: 'CashbackCelebrationEvent'
          totalRewardsBalance: string
          currencyCode: Currency['code']
      }
    | {
          type: 'EarnCelebrationEvent'
          totalTakerEarningsInUserCurrency: string
          takerType: TakerType
          currencyCode: Currency['code']
          location: 'portfolio' | 'earn_details'
      }
    | {
          type: 'ZealReviewSubmittedEvent'
          location: ZealRatingLocation
          rating: RangeInt<1, 5>
      }
    | { type: 'ZealReviewSkippedEvent'; location: ZealRatingLocation }

    // Card order flows
    | {
          type: 'CardOnboardedModulesNotDeployedEvent' // FIXME :: @Nicvaniek remove
          userId: string
          cardSafe: string
      }
    | { type: 'VirtualCardOrderFlowEnteredEvent' }
    | {
          type: 'PhysicalCardOrderFlowEnteredEvent'
      }
    | { type: 'PhysicalCardOrderingStartedEvent' }
    | { type: 'PhysicalCardOrderedEvent' }
    | { type: 'PhysicalCardConfirmPaymentStartedEvent' }
    | { type: 'PhysicalCardConfirmPaymentFinishedEvent' }
    | {
          type: 'PrepareCardSafeFlowEnteredEvent'
          accountConfiguration: GnosisPayAccountConfigurationState
          cardSafeType: CardSafeState['type']
      }
    | {
          type: 'CardSafeDeploymentStartedEvent'
          location: DeployCardSafeLocation
      }
    | { type: 'CardSafeDeployedEvent'; location: DeployCardSafeLocation }
    | {
          type: 'CardCurrencySettingStartedEvent'
          location: SetCardCurrencyLocation
      }
    | { type: 'CardCurrencySetEvent'; location: SetCardCurrencyLocation }
    | {
          type: 'PhoneNumberEntryDisplayedEvent'
          location: ConfigureCardSafeLocation
          gnosisPayAccountState:
              | GnosisPayKYCApprovedState['state']
              | GnosisPayPostKYCApprovedState['state']
      }
    | {
          type: 'PhoneOtpEntryDisplayedEvent'
          location: ConfigureCardSafeLocation
      }
    | {
          type: 'SourceOfFundsFlowEnteredEvent'
          location: ConfigureCardSafeLocation
      }
    | {
          type: 'SourceOfFundsFlowLoadedEvent'
          location: ConfigureCardSafeLocation
          questionsCount: number
      }
    | {
          type: 'SourceOfFundsQuestionAnsweredEvent'
          question: string
          location: ConfigureCardSafeLocation
      }
    | {
          type: 'CardSafeConfigurationInitiatedEvent'
          location: ConfigureCardSafeLocation
      }
    | {
          type: 'CardSafeConfiguredEvent'
          location: ConfigureCardSafeLocation
      }
    | {
          type: 'PhysicalCardActivationFlowEnteredEvent'
          location: PhysicalCardActivationLocation
      }
    | {
          type: 'PhysicalCardActivationInitiatedEvent'
          location: PhysicalCardActivationLocation
      }
    | {
          type: 'PhysicalCardActivationCompletedEvent'
          location: PhysicalCardActivationLocation
      }
    | {
          type: 'VirtualCardCreationEnteredEvent'
          location: 'card_settings' | 'card_tab'
      }
    | {
          type: 'VirtualCardCreatedEvent'
          location: 'card_settings' | 'card_tab'
      }
    | {
          type: 'AddToPayFlowEnteredEvent'
          location: 'card_settings' | 'portfolio' | 'card_tab'
      }
    | AddToPaySystemFlowOpenedEvent
    | {
          type: 'AddToPaySkippedEvent'
          location: 'card_settings' | 'portfolio' | 'card_tab'
      }

export type AddToPaySystemFlowOpenedEvent = {
    type: 'AddToPaySystemFlowOpenedEvent'
    location: 'card_settings' | 'portfolio' | 'card_tab'
}

type DefaultCurrencyEvents =
    | {
          type: 'DefaultCurrencySettingsEnteredEvent'
      }
    | {
          type: 'DefaultCurrencySetEvent'
          currency: FiatCurrencyCode
      }

type AddFundsEvents =
    | {
          type: 'FundFlowEnteredEvent'
      }
    | {
          type: 'TopUpDappOpenInitiatedEvent'
      }
    | {
          type: 'TopUpSendFlowEnteredEvent'
      }

export const keystoreToUserEventType = (
    keystore: KeyStore
): EventKeystoreType => {
    switch (keystore.type) {
        case 'safe_4337':
            return 'Safe'
        case 'track_only':
            return 'Contact'
        case 'private_key_store':
            return 'PrivateKey'
        case 'ledger':
            return 'Ledger'
        case 'secret_phrase_key':
            return 'SecretPhrase'
        case 'trezor':
            return 'Trezor'
        /* istanbul ignore next */
        default:
            return notReachable(keystore)
    }
}

export type GnosisPaySignupLocation =
    | 'card_tab'
    | 'bank_transfers'
    | 'onboarding'

export type GnosisPayOnboardingLocation = GnosisPaySignupLocation | 'portfolio'
export type GnosisPayReKycLocation = 'portfolio' | 'card_tab'
export type ZealRatingLocation =
    | 'cashback_celebration'
    | 'earn_celebration'
    | 'breward_celebration'
    | 'areward_celebration'
