import {
    getLocalHost,
    isLocalBackendEnabled,
    isLocalIndexerEnabled,
    isLocalProxyEnabled,
} from '@zeal/toolkit/Environment/getEnvironment'
import { isLocal } from '@zeal/toolkit/Environment/isLocal'
import { Hexadecimal } from '@zeal/toolkit/Hexadecimal'
import { stringify } from '@zeal/toolkit/JSON'
import { joinURL } from '@zeal/toolkit/URL/joinURL'
import * as Web3 from '@zeal/toolkit/Web3'
import { Address } from '@zeal/toolkit/Web3/address'

import { Auth, getAuthHeaders } from './Auth'
import { processFetchFailure, processFetchResponse } from './interceptors'
import { SubscribeToNotificationsBody } from './types'

export const BASE_URL =
    'https://nmbgrcr7f6.execute-api.eu-west-1.amazonaws.com/prod'

export const prepareBaseUrl = ({ path }: { path: string }) => {
    const url = joinURL(BASE_URL, path)

    if (isLocalProxyEnabled() && isLocal()) {
        return url.replace(
            joinURL(BASE_URL, '/proxy'),
            `http://${getLocalHost()}:8833`
        )
    }

    if (isLocalBackendEnabled() && isLocal()) {
        return url.replace(
            joinURL(BASE_URL, '/api'),
            `http://${getLocalHost()}:3311`
        )
    }

    if (isLocalIndexerEnabled() && isLocal()) {
        return url.replace(
            joinURL(BASE_URL, '/indexer'),
            `http://${getLocalHost()}:4411`
        )
    }

    return url
}

export type Paths = {
    post: Record<`/proxy/bundler/${string}`, { query: undefined; body: any }> & // TODO @resetko-zeal proper type for API body, string to 0x{string}
        Record<`/proxy/paymaster/${string}`, { query: undefined; body: any }> & // TODO @max how to make opaque type from domains be param in the api?
        Record<
            `/proxy/plc/${string}`,
            {
                query: undefined
                body: {
                    id: number | string
                    jsonrpc: '2.0'
                    method: string
                    params: unknown[]
                }
            }
        > &
        Record<
            `/api/referrals/${string}/code`,
            {
                query: never
                body: undefined
            }
        > &
        Record<
            `/api/metrics`,
            {
                query: never
                body: unknown
            }
        > &
        Record<
            `/api/referrals/${string}/claim`,
            {
                query: never
                body: undefined
            }
        > &
        Record<
            '/api/monerium/auth',
            {
                query: never
                body: {
                    message: string
                    signature: string
                }
            }
        > &
        Record<
            `/api/notifications/subscribe`,
            {
                query: never
                body: SubscribeToNotificationsBody
            }
        > &
        Record<
            `/api/brewards/claim`,
            {
                query: never
                body: {
                    gnosisToken: string
                    ownerAddress: Web3.address.Address
                    firstCardActivatedTimestampMS: number
                    installationCampaign: string | null
                    referralCode: string | null
                }
            }
        > &
        Record<
            `/api/gnosispay/signup`,
            {
                query: never
                body: {
                    token: string
                    email: string
                    otp: string
                    installationCampaign: string | null
                }
            }
        > &
        Record<
            `/proxy/rpc/${string}`,
            {
                query: never
                body: unknown
            }
        > &
        Record<
            `/proxy/unblock/user/bank-account/remote`,
            {
                query?: undefined
                body: {
                    main_beneficiary: true
                    account_name: string
                    account_details:
                        | {
                              currency: string
                              account_number: string
                              sort_code: string
                          }
                        | { currency: string; iban: string }
                }
            }
        > &
        Record<
            `/proxy/unblock/user/bank-account/unblock`,
            {
                query?: undefined
                body: { currency: string }
            }
        > &
        Record<
            `/proxy/unblock/user/kyc/applicant`,
            {
                query?: undefined
                body: {
                    date_of_birth: string
                    address: {
                        address_line_1: string
                        post_code: string
                        city: string
                        country: string
                    }
                    source_of_funds: string
                    source_of_funds_description?: string
                }
            }
        > &
        Record<
            `/proxy/unblock/auth/login`,
            {
                query: never
                body: {
                    message: string
                    signature: string
                }
            }
        > &
        Record<
            `/proxy/unblock/user`,
            {
                query: never
                body: {
                    first_name: string
                    last_name: string
                    target_address: string
                    target_address_type: 'SELF_HOSTED'
                    email: string
                    country: string
                    tokenPreferences: {
                        currency: 'EUR' | 'GBP'
                        chain: 'polygon' | 'optimism' | 'arbitrum' | 'base'
                        token: string
                    }[]
                }
            }
        > &
        Record<
            `/proxy/simulator/simulate`,
            {
                query: never
                body: {
                    network_id: number
                    from: string
                    to?: string
                    input: string
                    save: boolean
                    value?: string
                    simulation_type: 'quick'
                    block_number?: number
                }
            }
        > &
        Record<
            `/proxy/simulator/simulate-bundle`,
            {
                query: never
                body: {
                    simulations: {
                        network_id: number
                        from: string
                        to?: string
                        input: string
                        save: boolean
                        simulation_type: 'quick'
                        block_number?: number
                        value?: string
                    }[]
                }
            }
        > &
        Record<
            `/proxy/ba/${string}/v0/validate/json-rpc`,
            {
                query: never
                body: {
                    options: ['validation']
                    account_address: string
                    metadata: { domain: string } | { non_dapp: true }
                    data: {
                        method: string
                        params: string[]
                    }
                }
            }
        > &
        Record<
            `/proxy/ba/${string}/v0/validate/transaction`,
            {
                query: never
                body: {
                    options: ['validation']
                    metadata: { domain: string } | { non_dapp: true }
                    data: {
                        from: string
                        data: string
                        value?: string
                        to?: string
                    }
                }
            }
        >

    get: Record<`/proxy/cgv3/exchange_rates`, { query: undefined }> &
        Record<`/api/referrals/${string}`, { query: never }> &
        Record<
            `/proxy/dappr/dapps`,
            {
                query: {
                    page: number
                    resultsPerPage: number
                }
            }
        > &
        Record<
            `/proxy/cgv3/simple/token_price/${string}`,
            {
                query: {
                    contract_addresses: string
                    vs_currencies: string
                    include_24hr_change: boolean
                }
            }
        > &
        Record<
            `/proxy/cgv3/coins/list`,
            {
                query: {
                    include_platform: true
                }
            }
        > &
        Record<
            `/api/gnosispay/is-zeal/${string}`,
            {
                query: never
            }
        > &
        Record<
            `/api/unblock/webhook/${string}`, // TODO @resetko-zeal use Hexadecimal.Hexadecimal, but need to fix matching in tests
            { query: never }
        > &
        Record<
            `/indexer/erc20-tokens/${string}/${string}`,
            { query: undefined }
        > &
        Record<
            `/indexer/transactions/${string}/${string}`,
            {
                query: {
                    afterTimestampMs?: number
                    beforeTimestampMs?: number
                    logsForAddresses?: string
                }
            }
        > &
        Record<
            `/indexer/block-number/${Hexadecimal}/${number}`,
            { query: undefined }
        > &
        Record<'/indexer/card-safe', { query: { owners: Address[] } }> &
        Record<
            `/indexer/contract-stats/${string}`,
            { query: { addresses: Address[] } }
        > &
        Record<
            `/indexer/cashback-reward-info/${Hexadecimal}/${Web3.address.Address}`,
            { query?: undefined }
        > &
        Record<
            `/proxy/unblock/user/bank-account/remote`,
            { query?: undefined; body?: undefined }
        > &
        Record<
            `/proxy/unblock/user/bank-account/remote/${string}`,
            { query?: undefined; body?: undefined }
        > &
        Record<`/proxy/unblock/user/token-preferences`, { query?: undefined }> &
        Record<
            `/proxy/unblock/user/kyc/applicant/token`,
            { query?: undefined }
        > &
        Record<`/proxy/unblock/user/transactions`, { query?: undefined }> &
        Record<
            `/proxy/unblock/user/bank-account/unblock/${string}`,
            { query?: undefined }
        > &
        Record<
            `/proxy/unblock/user/bank-account/unblock`,
            { query?: undefined }
        > &
        Record<
            `/proxy/unblock/user/wallet/${string}`,
            { query?: undefined; body?: undefined }
        > &
        Record<
            `/proxy/unblock/fees`,
            {
                query: {
                    payment_method: 'bank_transfer'
                    direction: 'cryptoToFiat' | 'fiatToCrypto'
                    input_currency: string
                    output_currency: string
                    amount: string
                }
            }
        > &
        Record<
            `/proxy/unblock/exchange-rates`,
            {
                query: {
                    base_currency: string
                    target_currency: string
                }
            }
        > &
        Record<
            `/proxy/unblock/user`,
            {
                query: never
            }
        > &
        Record<
            `/proxy/cba/sky/usds/total-supply`,
            {
                query: never
            }
        > &
        Record<
            `/proxy/cba/spark/savings/usds/backed/`,
            {
                query: never
            }
        > &
        Record<
            `/proxy/unblock/user/kyc/applicant`,
            {
                query: never
            }
        > &
        Record<
            `/proxy/dbk/user/all_history_list`,
            {
                query: {
                    id: string
                    start_time: number
                    page_count: number
                    chain_ids?: string[]
                }
            }
        > &
        Record<
            `/proxy/dbk/user/used_chain_list`,
            {
                query: {
                    id: string
                }
            }
        > &
        Record<
            `/proxy/dbk/user/all_token_list`,
            {
                query: {
                    id: string
                    chain_ids?: string[]
                }
            }
        > &
        Record<
            `/proxy/dbk/user/all_nft_list`,
            {
                query: {
                    id: string
                    is_all: boolean
                    chain_ids?: string[]
                }
            }
        > &
        Record<
            `/proxy/dbk/user/all_complex_protocol_list`,
            {
                query: {
                    id: string
                    chain_ids?: string[]
                }
            }
        > &
        Record<`/api/campaigns/${string}/active`, { query: never }>

    delete: Record<
        `/proxy/unblock/user/bank-account/remote/${string}`,
        { query?: undefined; body?: undefined }
    >

    patch: Record<
        `/proxy/unblock/user/bank-account/remote`,
        {
            query?: undefined
            body: {
                remote_bank_account_uuid: string
            }
        }
    > &
        Record<
            `/proxy/unblock/user/token-preferences`,
            {
                query?: undefined
                body: {
                    currency: 'EUR' | 'GBP'
                    chain: 'polygon' | 'optimism' | 'arbitrum' | 'base'
                    token: string
                }
            }
        > &
        Record<
            `/proxy/unblock/user`,
            {
                query?: undefined
                body:
                    | {
                          sign_in_crypto_address: string
                      }
                    | {
                          target_address: string
                          target_address_type: 'SELF_HOSTED'
                      }
                    | {
                          first_name: string
                          last_name: string
                          date_of_birth: string
                          address: {
                              address_line_1: string
                              post_code: string
                              city: string
                              country: string
                          }
                      }
                    | {
                          address: {
                              address_line_1: string
                              post_code: string
                              city: string
                              country: string
                          }
                      }
            }
        >
}

export const post = <T extends keyof Paths['post']>(
    path: T,
    params: {
        query?: Paths['post'][T]['query']
        body: Paths['post'][T]['body']
        auth?: Auth
    },
    signal?: AbortSignal
): Promise<unknown> => {
    const method = 'POST'
    const url = prepareBaseUrl({ path })
    const query = params.query
        ? `?${new URLSearchParams(params.query as Record<string, string>)}`
        : ''

    const body = JSON.stringify(params.body)
    // eslint-disable-next-line no-restricted-globals
    return fetch(`${url}${query}`, {
        method,
        body,
        headers: {
            'Content-Type': 'application/json',
            ...(params.auth ? getAuthHeaders(params.auth) : {}),
        },
        signal,
    })
        .catch((error: unknown) =>
            processFetchFailure({
                error,
                info: { url: `${url}${query}`, method, requestBody: body },
            })
        )
        .then((response) =>
            processFetchResponse({ params, method, response, url })
        )
        .then((response) => response.text())
}

export const patch = <T extends keyof Paths['patch']>(
    path: T,
    params: {
        query?: Paths['patch'][T]['query']
        body: Paths['patch'][T]['body']
        auth?: Auth
    },
    signal?: AbortSignal
): Promise<unknown> => {
    const method = 'PATCH'
    const url = prepareBaseUrl({ path })
    const query = params.query
        ? `?${new URLSearchParams(params.query as Record<string, string>)}`
        : ''
    const body = JSON.stringify(params.body)

    // eslint-disable-next-line no-restricted-globals
    return fetch(`${url}${query}`, {
        method,
        body,
        headers: {
            'Content-Type': 'application/json',
            ...(params.auth ? getAuthHeaders(params.auth) : {}),
        },
        signal,
    })
        .catch((error: unknown) =>
            processFetchFailure({
                error,
                info: {
                    url: `${url}${query}`,
                    method,
                    requestBody: body,
                },
            })
        )
        .then((response) =>
            processFetchResponse({ params, method, response, url })
        )
        .then((response) => response.text())
}

export const del = <T extends keyof Paths['delete']>(
    path: T,
    params: {
        query?: Paths['delete'][T]['query']
        body: Paths['delete'][T]['body']
        auth?: Auth
    },
    signal: AbortSignal | undefined
): Promise<unknown> => {
    const method = 'DELETE'
    const url = prepareBaseUrl({ path })
    const query = params.query
        ? `?${new URLSearchParams(params.query as Record<string, string>)}`
        : ''

    const body = params.body ? stringify(params.body) : undefined

    // eslint-disable-next-line no-restricted-globals
    return fetch(`${url}${query}`, {
        credentials: 'omit',
        method,
        headers: {
            'Content-Type': 'application/json',
            'Accept-Encoding': 'gzip',
            ...(params.auth ? getAuthHeaders(params.auth) : {}),
        },
        body,
        signal,
    })
        .catch((error: unknown) =>
            processFetchFailure({
                error,
                info: { url: `${url}${query}`, method, requestBody: body },
            })
        )
        .then((response) =>
            processFetchResponse({ params, method, response, url })
        )
        .then((response) => response.text())
}

export const get = <T extends keyof Paths['get']>(
    path: T,
    params: {
        query?: Paths['get'][T]['query']
        auth?: Auth
    },
    signal: AbortSignal | undefined
): Promise<unknown> => {
    const method = 'GET'
    const url = prepareBaseUrl({ path })
    const query = params.query
        ? `?${new URLSearchParams(params.query as Record<string, string>)}`
        : ''

    // eslint-disable-next-line no-restricted-globals
    return fetch(`${url}${query}`, {
        method,
        headers: {
            ...(params.auth ? getAuthHeaders(params.auth) : {}),
        },
        signal,
    })
        .catch((error: unknown) =>
            processFetchFailure({
                error,
                info: { url: `${url}${query}`, method, requestBody: null },
            })
        )
        .then((response) =>
            processFetchResponse({ params, method, response, url })
        )
        .then((response) => response.json())
}
