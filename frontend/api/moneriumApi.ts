import { joinURL } from '@zeal/toolkit/URL/joinURL'

import { Auth, getAuthHeaders } from './Auth'
import { processFetchFailure, processFetchResponse } from './interceptors'

export const MONERIUM_BASE_URL = 'https://api.monerium.app'

export type Paths = {
    get: Record<`/addresses`, { query: never }> &
        Record<`/orders/${string}`, { query: never }>

    post: Record<
        '/addresses',
        {
            query: never
            body: {
                address: string
                chain: string
                message: string
                signature: string
            }
        }
    > &
        Record<
            `/orders`,
            {
                query: never
                body: {
                    address: string
                    currency: string
                    chain: string
                    kind: string
                    amount: string
                    counterpart: {
                        identifier: {
                            standard: string
                            iban: string
                        }
                        details: {
                            firstName: string
                            lastName: string
                            country: string
                        }
                    }
                    message: string
                    signature: string
                    memo?: string
                }
            }
        >
}

export const get = async <T extends keyof Paths['get']>(
    path: T,
    params: { query?: Paths['get'][T]['query'] } & { auth?: Auth },
    signal: AbortSignal | undefined
): Promise<unknown> => {
    const url = joinURL(MONERIUM_BASE_URL, path)
    const query = params.query
        ? `?${new URLSearchParams(params.query as Record<string, string>)}`
        : ''
    const urlWithQuery = `${url}${query}`

    // eslint-disable-next-line no-restricted-globals
    return fetch(urlWithQuery, {
        method: 'GET',
        headers: {
            ...(params.auth ? getAuthHeaders(params.auth) : {}),
        },
        signal,
    })
        .catch((error: unknown) =>
            processFetchFailure({
                error,
                info: { url: urlWithQuery, method: 'GET', requestBody: null },
            })
        )
        .then((response) =>
            processFetchResponse({ params: {}, method: 'GET', response, url })
        )
        .then((response) => response.text())
}

export const post = <T extends keyof Paths['post']>(
    path: T,
    params: {
        query?: Paths['post'][T]['query']
        auth?: Auth
        body: Paths['post'][T]['body']
    },
    signal: AbortSignal | undefined
): Promise<unknown> => {
    const url = joinURL(MONERIUM_BASE_URL, path)
    const query = params.query
        ? `?${new URLSearchParams(params.query as Record<string, string>)}`
        : ''
    const urlWithQuery = `${url}${query}`
    const body = JSON.stringify(params.body)

    // eslint-disable-next-line no-restricted-globals
    return fetch(urlWithQuery, {
        method: 'POST',
        body,
        headers: {
            ...(params.auth ? getAuthHeaders(params.auth) : {}),
            'Content-Type': 'application/json',
        },
        signal,
    })
        .catch((error: unknown) =>
            processFetchFailure({
                error,
                info: {
                    url: urlWithQuery,
                    method: 'POST',
                    requestBody: body,
                },
            })
        )
        .then((response) =>
            processFetchResponse({ params, method: 'POST', response, url })
        )
        .then((response) => response.text())
}
