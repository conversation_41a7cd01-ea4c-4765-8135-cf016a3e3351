import { joinURL } from '@zeal/toolkit/URL/joinURL'

import { processFetchFailure, processFetchResponse } from './interceptors'

const SWAPSIO_BASE_URL = 'https://crypto.prod.swaps.io/api/v0/'

type Paths = {
    get: Record<`/contracts`, { query: undefined }>
}

export const get = <T extends keyof Paths['get']>(
    path: T,
    params: { query?: Paths['get'][T]['query'] }, // TODO @resetko-zeal fix query is not mandatory even if its there in Paths
    signal?: AbortSignal
): Promise<unknown> => {
    const url = joinURL(SWAPSIO_BASE_URL, path)
    const query = params.query
        ? `?${new URLSearchParams(params.query as Record<string, string>)}`
        : ''
    const urlWithQuery = `${url}${query}`

    // eslint-disable-next-line no-restricted-globals
    return fetch(urlWithQuery, {
        method: 'GET',
        headers: {},
        signal,
    })
        .catch((error: unknown) =>
            processFetchFailure({
                error,
                info: { url, method: 'GET', requestBody: null },
            })
        )
        .then((response) =>
            processFetchResponse({ params, method: 'GET', response, url })
        )
        .then((response) => response.json())
}
