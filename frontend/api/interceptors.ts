import { AxiosError, CanceledError } from 'axios'

import { ImperativeError } from '@zeal/toolkit/Error'
import { joinURL } from '@zeal/toolkit/URL/joinURL'

// TODO @resetko-zeal @mike think where to put these types
// eslint-disable-next-line zeal-domains/secure-api-folder
import { ConnectivityError, HttpError } from '@zeal/domains/Error'
// eslint-disable-next-line zeal-domains/secure-api-folder
import { getReplacedStack } from '@zeal/domains/Error/helpers/joinStacks'

export const processFetchFailure = async ({
    error,
    info,
}: {
    error: unknown
    info: {
        url: string
        method: string
        requestBody: unknown
    } | null
}): Promise<never> => {
    // if it is fetch's native abort error, do not touch it
    if (error instanceof Error && error.name === 'AbortError') {
        throw error
    }

    // TODO @resetko-zeal see parser, should we parse it by message instead, since it's easier and more precise? Then this "interceptor" won't be needed
    const newError = new ConnectivityError({ error, info })

    throw newError
}

export const processFetchResponse = async ({
    params,
    response,
    url,
    method,
    requestStackError,
}: {
    params: unknown
    url: string
    method: 'GET' | 'POST' | 'DELETE' | 'PATCH' | 'PUT'
    response: Response
    requestStackError?: ImperativeError // TODO @resetko-zeal this is experimental, see if we're getting correct stack on mobile. Then - make mandatory.
}): Promise<Response> => {
    if (response.ok) {
        return response
    }

    const status = response.status
    const errorData = await response.text().then((data) => {
        try {
            return JSON.parse(data)
        } catch {
            return data
        }
    })

    const requestBody =
        typeof params === 'object' && params !== null && 'body' in params
            ? params.body
            : null

    const errorToThrow = new HttpError({
        method,
        requestBody,
        responseBody: errorData,
        responseHeaders: HttpError.collectHeaders(response.headers),
        status,
        url,
    })

    errorToThrow.stack = getReplacedStack(errorToThrow, requestStackError)

    throw errorToThrow
}

/**
 * @deprecated not needed if `fetch` based fetchers are used
 */
export const convertToHttpErrorToPreserverStack = (error: unknown) => {
    if (error instanceof CanceledError) {
        return Promise.reject(error)
    }

    if (error instanceof AxiosError) {
        const status = error.response?.status || null
        const { url, method, baseURL } = error.config

        const fullUrl = joinURL(baseURL || '', url || '')

        if (url && method && status) {
            const newError = new HttpError({
                method,
                requestBody: error.config.data,
                responseBody: error.response?.data,
                responseHeaders: HttpError.collectHeaders(
                    error.response?.headers
                ),
                status,
                url: fullUrl,
            })

            newError.stack = getReplacedStack(newError, error)

            // We return customized error if we were able to collect data for it
            return Promise.reject(newError)
        }

        if (url && method && !status) {
            const newError = new ConnectivityError({
                error,
                info: {
                    method,
                    requestBody: error.config.data,
                    url: fullUrl,
                },
            })

            newError.stack = getReplacedStack(newError, error)

            return Promise.reject(newError)
        }

        return Promise.reject(error)
    }

    return Promise.reject(error)
}
