import { processFetchFailure, processFetchResponse } from './interceptors'

// eslint-disable-next-line no-restricted-globals
export const externalFetch = fetch

export const externalFetchWithFallback = async (
    urls: string[],
    initRequest?: Omit<RequestInit, 'method'> & {
        method?: 'GET' | 'POST' | 'DELETE' | 'PATCH'
    }
): Promise<Response> => {
    const [url, ...rest] = urls
    if (!url) {
        throw new Error(
            'external fetch with fallback called with empty url list'
        )
    }
    try {
        return await externalFetch(url, initRequest)
            .catch((error: unknown) =>
                processFetchFailure({
                    error,
                    info: {
                        url,
                        method: initRequest?.method ?? 'GET',
                        requestBody: initRequest?.body ?? null,
                    },
                })
            )
            .then((response) =>
                processFetchResponse({
                    params: initRequest,
                    method: initRequest?.method ?? 'GET',
                    response,
                    url,
                })
            )
    } catch (e) {
        if (rest.length === 0) {
            throw e
        }
        return externalFetchWithFallback(rest, initRequest)
    }
}
