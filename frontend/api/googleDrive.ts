import { joinURL } from '@zeal/toolkit/URL/joinURL'

import { Auth, getAuthHeaders } from './Auth'
import { processFetchFailure, processFetchResponse } from './interceptors'

const GAPI_BASE_URL = 'https://www.googleapis.com/drive/v3/'
const GAPI_UPLOAD_BASE_URL = 'https://www.googleapis.com/upload/drive/v3/'

type UploadPaths = {
    post: {
        '/files': {
            query: { uploadType: 'multipart'; fields: 'id,modifiedTime' }
            body: FormData
        }
    }
}

type Paths = {
    get: {
        '/files': {
            query: {
                alt?: 'media'
                q?: string
                fields?: string
            }
        }
    }

    download: Record<
        `/files/${string}`,
        {
            query: {
                alt?: 'media'
                q?: string
                fields?: string
            }
        }
    >

    post: {
        '/files': {
            query: {
                uploadType: string
                fields: string
            }
            body: {
                mimeType: string
                name: string
                parents: string[]
            }
        }
    }
}

export const get = <T extends keyof Paths['get']>(
    path: T,
    params: { query: Paths['get'][T]['query']; auth?: Auth },
    signal: AbortSignal | undefined
): Promise<unknown> => {
    const url = joinURL(GAPI_BASE_URL, path)
    const query = params.query
        ? `?${new URLSearchParams(params.query as Record<string, string>)}`
        : ''
    const urlWithQuery = `${url}${query}`

    // eslint-disable-next-line no-restricted-globals
    return fetch(urlWithQuery, {
        method: 'GET',
        headers: {
            ...(params.auth ? getAuthHeaders(params.auth) : {}),
        },
        signal,
    })
        .catch((error: unknown) =>
            processFetchFailure({
                error,
                info: { url: urlWithQuery, method: 'GET', requestBody: null },
            })
        )
        .then((response) =>
            processFetchResponse({ params, method: 'GET', response, url })
        )
        .then((response) => response.json())
}
export const getDownload = <T extends keyof Paths['download']>(
    path: T,
    params: { query: Paths['download'][T]['query']; auth?: Auth },
    signal: AbortSignal | undefined
): Promise<unknown> => {
    const url = joinURL(GAPI_BASE_URL, path)
    const query = params.query
        ? `?${new URLSearchParams(params.query as Record<string, string>)}`
        : ''
    const urlWithQuery = `${url}${query}`

    // eslint-disable-next-line no-restricted-globals
    return fetch(urlWithQuery, {
        method: 'GET',
        headers: {
            ...(params.auth ? getAuthHeaders(params.auth) : {}),
        },
        signal,
    })
        .catch((error: unknown) =>
            processFetchFailure({
                error,
                info: { url: urlWithQuery, method: 'GET', requestBody: null },
            })
        )
        .then((response) =>
            processFetchResponse({ params, method: 'GET', response, url })
        )
        .then((response) => response.text())
}

export const post = <T extends keyof Paths['post']>(
    path: T,
    params: {
        query?: Paths['post'][T]['query']
        auth?: Auth
        body: Paths['post'][T]['body']
    },
    signal: AbortSignal | undefined
): Promise<unknown> => {
    const url = joinURL(GAPI_BASE_URL, path)
    const query = params.query
        ? `?${new URLSearchParams(params.query as Record<string, string>)}`
        : ''
    const urlWithQuery = `${url}${query}`
    const body = JSON.stringify(params.body)

    // eslint-disable-next-line no-restricted-globals
    return fetch(urlWithQuery, {
        method: 'POST',
        body,
        headers: {
            'Content-Type': 'application/json',
            ...(params.auth ? getAuthHeaders(params.auth) : {}),
        },
        signal,
    })
        .catch((error: unknown) =>
            processFetchFailure({
                error,
                info: {
                    url: urlWithQuery,
                    method: 'POST',
                    requestBody: body,
                },
            })
        )
        .then((response) =>
            processFetchResponse({ params, method: 'POST', response, url })
        )
        .then((response) => response.json())
}

export const postUpload = <T extends keyof Paths['post']>(
    path: T,
    params: {
        query?: UploadPaths['post'][T]['query']
        auth?: Auth
        body: UploadPaths['post'][T]['body']
    },
    signal: AbortSignal | undefined
): Promise<unknown> => {
    const url = joinURL(GAPI_UPLOAD_BASE_URL, path)
    const query = params.query
        ? `?${new URLSearchParams(params.query as Record<string, string>)}`
        : ''
    const urlWithQuery = `${url}${query}`

    // eslint-disable-next-line no-restricted-globals
    return fetch(urlWithQuery, {
        method: 'POST',
        body: params.body,
        headers: {
            ...(params.auth ? getAuthHeaders(params.auth) : {}),
        },
        signal,
    })
        .catch((error: unknown) =>
            processFetchFailure({
                error,
                info: {
                    url: urlWithQuery,
                    method: 'POST',
                    requestBody: params.body,
                },
            })
        )
        .then((response) =>
            processFetchResponse({ params, method: 'POST', response, url })
        )
        .then((response) => response.json())
}
