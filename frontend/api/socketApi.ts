import { Hexadecimal } from '@zeal/toolkit/Hexadecimal'
import { joinURL } from '@zeal/toolkit/URL/joinURL'

import { processFetchFailure, processFetchResponse } from './interceptors'

const API_KEY = '1d7bfc31-6018-4876-b7c2-980ce0e903de'

export const SOCKET_BASE_URL = 'https://api.socket.tech/v2'

export type Paths = {
    post: Record<
        '/build-tx',
        {
            query: never
            body: {
                route: unknown
                refuel?: unknown
            }
        }
    >
    get: Record<
        '/bridge-status',
        {
            query: {
                transactionHash: string
                fromChainId: number
                toChainId: number
                bridgeName: string
            }
        }
    > &
        Record<
            '/quote',
            {
                query: {
                    bridgeWithGas?: boolean
                    defaultBridgeSlippage: string
                    defaultSwapSlippage: string
                    fromAmount: string
                    fromChainId: string
                    fromTokenAddress: string
                    recipient: string
                    singleTxOnly?: true
                    sort: 'output'
                    toChainId: string
                    toTokenAddress: string
                    userAddress: string
                    isContractCall?: boolean // TODO @resetko-zeal should not be optional
                }
            }
        > &
        Record<
            '/token-lists/from-token-list',
            {
                query: {
                    fromChainId: Hexadecimal
                    toChainId: Hexadecimal
                    singleTxOnly: boolean
                    isShortList: boolean
                }
            }
        > &
        Record<
            '/token-lists/to-token-list',
            {
                query: {
                    fromChainId: Hexadecimal
                    toChainId: Hexadecimal
                    singleTxOnly: boolean
                    isShortList: boolean
                }
            }
        > &
        Record<'/supported/chains', { query: never }>
}

export const get = <T extends keyof Paths['get']>(
    path: T,
    params: { query?: Paths['get'][T]['query'] }, // TODO @resetko-zeal fix query is not mandatory even if its there in Paths
    signal: AbortSignal | undefined
): Promise<unknown> => {
    const url = joinURL(SOCKET_BASE_URL, path)
    const query = params.query
        ? `?${new URLSearchParams(Object.entries(params.query))}`
        : ''
    const urlWithQuery = `${url}${query}`

    // eslint-disable-next-line no-restricted-globals
    return fetch(urlWithQuery, {
        method: 'GET',
        headers: {
            'api-key': API_KEY,
        },
        signal,
    })
        .catch((error: unknown) =>
            processFetchFailure({
                error,
                info: { url, method: 'GET', requestBody: null },
            })
        )
        .then((response) =>
            processFetchResponse({ params, method: 'GET', response, url })
        )
        .then((response) => response.json())
        .then((response) => {
            if (response.success) {
                return response.result
            }

            throw response
        })
}

export const post = <T extends keyof Paths['post']>(
    path: T,
    params: {
        query?: Paths['post'][T]['query']
        body: Paths['post'][T]['body']
    },
    signal: AbortSignal | undefined
): Promise<unknown> => {
    const url = joinURL(SOCKET_BASE_URL, path)
    const query = params.query
        ? `?${new URLSearchParams(Object.entries(params.query))}`
        : ''
    const urlWithQuery = `${url}${query}`
    const body = JSON.stringify(params.body)

    // eslint-disable-next-line no-restricted-globals
    return fetch(urlWithQuery, {
        method: 'POST',
        body,
        headers: {
            'api-key': API_KEY,
            'Content-Type': 'application/json',
        },
        signal,
    })
        .catch((error: unknown) =>
            processFetchFailure({
                error,
                info: { url: urlWithQuery, method: 'POST', requestBody: body },
            })
        )
        .then((response) =>
            processFetchResponse({ params, method: 'POST', response, url })
        )
        .then((response) => response.json())
        .then((response) => {
            if (response.success) {
                return response.result
            }

            throw response
        })
}
