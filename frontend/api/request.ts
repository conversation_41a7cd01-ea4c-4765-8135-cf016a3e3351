import axios from 'axios'

import { isProduction } from '@zeal/toolkit/Environment/isProduction'
import { joinURL } from '@zeal/toolkit/URL/joinURL'

import { Auth, getAuthHeaders } from './Auth'
import {
    convertToHttpErrorToPreserverStack,
    processFetchFailure,
    processFetchResponse,
} from './interceptors'
import { paths } from './portfolio'

export type APIPaths = Omit<paths, '/wallet/unblock/'>

const BASE_URL = isProduction()
    ? 'https://rdwdvjp8j5.execute-api.eu-west-1.amazonaws.com/'
    : 'https://iw8i6d52oi.execute-api.eu-west-2.amazonaws.com/'

export const request = axios.create({
    baseURL: BASE_URL,
})

request.interceptors.response.use(
    (response) => response,
    convertToHttpErrorToPreserverStack
)

type OpResponseTypes<OP> = OP extends {
    responses: infer R
}
    ? {
          [S in keyof R]: R[S] extends { schema?: infer S } // openapi 2
              ? S
              : R[S] extends { content: { 'application/json': infer C } } // openapi 3
                ? C
                : S extends 'default'
                  ? R[S]
                  : unknown
      }
    : never

type _OpReturnType<T> = 200 extends keyof T
    ? T[200]
    : 201 extends keyof T
      ? T[201]
      : 'default' extends keyof T
        ? T['default']
        : unknown

type OpReturnType<OP> = _OpReturnType<OpResponseTypes<OP>>

type OpArgType<OP> = (OP extends {
    requestBody: { content: { 'application/json': infer RB } }
}
    ? { body: RB }
    : { body?: unknown }) &
    (OP extends { parameters?: { body?: infer B } }
        ? { body: B }
        : { body?: unknown }) &
    (OP extends { parameters?: { query: infer Q } }
        ? { query: Q }
        : { query?: unknown })

type KeysMatching<T, V> = {
    [K in keyof T]-?: T[K] extends V ? K : never
}[keyof T]

export const post = <Key extends KeysMatching<APIPaths, { post: unknown }>>(
    path: Key,
    params: OpArgType<APIPaths[Key]['post']> & {
        auth?: Auth
        requestSource?: string
    },
    signal?: AbortSignal
): Promise<OpReturnType<APIPaths[Key]['post']>> =>
    request
        .post(path, params.body, {
            params: params.query,
            signal,
            withCredentials: false,
            headers: {
                ...(params.auth ? getAuthHeaders(params.auth) : {}),
                ...(params.requestSource
                    ? { 'x-requested-for': params.requestSource }
                    : {}),
            },
        })
        .then((r) => r.data)

export const get = <Key extends KeysMatching<APIPaths, { get: unknown }>>(
    path: Key,
    params: OpArgType<APIPaths[Key]['get']> & { auth?: Auth },
    signal?: AbortSignal
): Promise<OpReturnType<APIPaths[Key]['get']>> => {
    const url = joinURL(BASE_URL, path)
    const query = params.query
        ? `?${new URLSearchParams(params.query as Record<string, string>)}`
        : ''
    const urlWithQuery = `${url}${query}`

    // eslint-disable-next-line no-restricted-globals
    return fetch(urlWithQuery, {
        credentials: 'omit',
        method: 'GET',
        headers: {
            'Content-Type': 'application/json',
            'Accept-Encoding': 'gzip',
            'Cache-Control': 'max-age=0', // cache not working on ios without cache control header
            ...(params.auth ? getAuthHeaders(params.auth) : {}),
        },
        signal,
    })
        .catch((error: unknown) =>
            processFetchFailure({
                error,
                info: { url: urlWithQuery, method: 'GET', requestBody: null },
            })
        )
        .then((response) =>
            processFetchResponse({ params, method: 'GET', response, url })
        )
        .then((response) => response.json())
}
