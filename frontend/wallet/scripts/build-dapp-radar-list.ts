import { get } from '@zeal/api/requestBackend'
import * as fs from 'fs'

import {
    arrayOf,
    boolean,
    nullableOf,
    number,
    object,
    Result,
    shape,
    string,
} from '@zeal/toolkit/Result'

type DappRadarInfoDTO = {
    isActive: boolean
} & DappRadarInfo

type DappRadarInfo = {
    name: string
    logo: null | string
    website: string
}

type DappRadarResponse = {
    pageCount: number
    page: number
    results: DappRadarInfoDTO[]
}

const parseDappRadarInfoDTO = (
    input: unknown
): Result<unknown, DappRadarInfoDTO> =>
    object(input).andThen((input) => {
        return shape({
            isActive: boolean(input.isActive),
            name: string(input.name),
            logo: nullableOf(input.logo, string),
            website: string(input.website),
        })
    })

const parseDappRadarResponseDTO = (
    input: unknown
): Result<unknown, DappRadarResponse> => {
    return object(input).andThen((obj) => {
        return shape({
            pageCount: number(obj.pageCount),
            page: number(obj.page),
            results: arrayOf(obj.results, parseDappRadarInfoDTO),
        })
    })
}

const fetchDapps = async ({
    page,
    data,
}: {
    page: number
    data: DappRadarInfo[]
}): Promise<DappRadarInfo[]> => {
    const res = await get(
        '/proxy/dappr/dapps',
        {
            query: {
                page,
                resultsPerPage: 50,
            },
        },
        undefined
    )
    const parsed = parseDappRadarResponseDTO(res).getSuccessResultOrThrow(
        'cannot parse dapp radar response'
    )
    const combinedData = data.concat(
        parsed.results.filter((dapp) => dapp.isActive)
    )

    if (page < parsed.pageCount) {
        // eslint-disable-next-line no-console
        console.log('fetched page ', page, ' of ', parsed.pageCount)
        return fetchDapps({ page: page + 1, data: combinedData })
    }

    return combinedData
}

const main = async (): Promise<void> => {
    const res = await fetchDapps({ page: 1, data: [] })
    const data: DappRadarInfo[] = res.map((dapp) => ({
        name: dapp.name,
        logo: dapp.logo,
        website: dapp.website,
    }))

    fs.writeFileSync(
        '../mobile/assets/data/dappradar.json',
        JSON.stringify(data)
    )
}

// eslint-disable-next-line no-console
main().catch(console.error)
