import { matchPath } from '@remix-run/router'
import {
    GNOSIS_PAY_BASE_URL,
    Paths as GnosisApiPaths,
} from '@zeal/api/gnosisApi'
import { APIPaths, request as axiosInstance } from '@zeal/api/request'
import {
    BASE_URL as API_MOCK_V2_BASE_URL,
    Paths as ApiMockV2Paths,
} from '@zeal/api/requestBackend'
import { Paths as SocketApiPaths, SOCKET_BASE_URL } from '@zeal/api/socketApi'
import {
    Paths as SwapsIOApiPaths,
    SWAPSIO_BASE_URL,
} from '@zeal/api/swapsIOApi'
import { AxiosAdapter, AxiosRequestConfig, AxiosResponse } from 'axios'

import { keys } from '@zeal/toolkit/Object'

import { unblockGBPtoUSDRate } from '@zeal/domains/Currency/domains/BankTransfer/api/fixtures/exchangeRate'
import { unblockNoFee } from '@zeal/domains/Currency/domains/BankTransfer/api/fixtures/fees'
import { successfulUnblockLogin } from '@zeal/domains/Currency/domains/BankTransfer/api/fixtures/login'
import { unblockRemoteGBPandEURAccounts } from '@zeal/domains/Currency/domains/BankTransfer/api/fixtures/remoteBankAccounts'
import { unblockGBPandEURaccounts } from '@zeal/domains/Currency/domains/BankTransfer/api/fixtures/unblockBankAccounts'
import { unblockFullUser } from '@zeal/domains/Currency/domains/BankTransfer/api/fixtures/user'
import { HttpError } from '@zeal/domains/Error'
import { usdRates } from '@zeal/domains/FXRate/api/fixtures/usdRates'
import { initialPortfolio } from '@zeal/domains/Portfolio/api/fixtures/portfolio'
import { RPCRequest } from '@zeal/domains/RPCRequest'
import { ethBlockNumber } from '@zeal/domains/RPCRequest/api/fixtures/ethBlockNumber'
import { ethGetBlockByNumber } from '@zeal/domains/RPCRequest/api/fixtures/ethGetBlockByNumber'
import { ethGetTransactionCount } from '@zeal/domains/RPCRequest/api/fixtures/ethGetTransactionCount'
import { ethSendRawTransactionResponse } from '@zeal/domains/RPCRequest/api/fixtures/ethSendRawTransaction'
import { safetyChecksPassed } from '@zeal/domains/SafetyCheck/api/fixtures/passed'
import { ethBalance } from '@zeal/domains/Transactions/api/fixtures/ethBalance'
import { gnosisFeeHistory } from '@zeal/domains/Transactions/api/fixtures/gnosisFeeHistory'
import { initialHistory } from '@zeal/domains/Transactions/api/fixtures/history'
import { approvalSimulation } from '@zeal/domains/Transactions/domains/SimulatedTransaction/api/fixtures/approval'

// eslint-disable-next-line no-restricted-globals
type Fetch = typeof fetch

type ResponseFunction = (config: AxiosRequestConfig) => [number, unknown]
type FetchResponseFunction = (
    ...params: Parameters<Fetch>
) => Promise<[number, unknown]> | [number, unknown]

export type ApiMockV2 = {
    [Method in keyof ApiMockV2Paths]: {
        [Path in keyof ApiMockV2Paths[Method]]: FetchResponseFunction
    }
}

type MockApiPaths = APIPaths

export type GnosisApiMock = {
    [Method in keyof GnosisApiPaths]: {
        [Path in keyof GnosisApiPaths[Method]]: FetchResponseFunction
    }
}

export type SocketApiMock = {
    [Method in keyof SocketApiPaths]: {
        [Path in keyof SocketApiPaths[Method]]: FetchResponseFunction
    }
}

export type ApiMock = Omit<
    {
        [Property in keyof MockApiPaths]: {
            [Meth in keyof MockApiPaths[Property]]: ResponseFunction
        }
    },
    '/wallet/rpc/' | '/wallet/unblock/' | '/wallet/smart-wallet/unblock/'
> & {
    '/wallet/rpc/': { post: ResponseFunction }
}

export type SwapsIOApiMock = {
    [Method in keyof SwapsIOApiPaths]: {
        [Path in keyof SwapsIOApiPaths[Method]]: FetchResponseFunction
    }
}

// TODO @resetko-zeal Wrap mock functions with jest.fn() [jest.Mock<T, P> type]

export type RPCMocks = Record<RPCRequest['method'], FetchResponseFunction>

let apiMock: ApiMock
let gnosisApiMock: GnosisApiMock
let socketApiMock: SocketApiMock
let apiMock2: ApiMockV2
let rpcMocks: RPCMocks
let swapsIOApiMock: SwapsIOApiMock

    //
;(global.fetch as unknown) = async (
    url: string,
    options: { method: 'GET' }
): ReturnType<Fetch> => {
    const method = options.method.toLowerCase()

    const parsedURL = new URL(url)

    if (new RegExp(SOCKET_BASE_URL).test(url)) {
        return await getSocketApiResponse(url, options)
    }

    if (new RegExp(GNOSIS_PAY_BASE_URL).test(url)) {
        return await getGnosisApiResponse(url, options)
    }

    if (new RegExp(API_MOCK_V2_BASE_URL).test(url)) {
        return await getApiMockV2Response(url, options)
    }

    if (new RegExp(SWAPSIO_BASE_URL).test(url)) {
        return await getSwapsIOApiResponse(url, options)
    }

    const config = {
        method,
        url: parsedURL.pathname,
        params: Object.fromEntries(parsedURL.searchParams.entries()),
    }

    const { data, headers, status } = await getResponse(config)

    return Promise.resolve({
        ok: !isFailedStatus(status),
        headers: { get: (key: string) => headers[key] },
        json: () => Promise.resolve(data),
        text: () => Promise.resolve(JSON.stringify(data)),
    } as Response)
}

const isFailedStatus = (status: number): boolean => status > 299

const getMockResponder = (config: AxiosRequestConfig): ResponseFunction => {
    const requestUrl = config.url || ''
    const requestMethod = config.method || ''
    const mockPaths = keys(apiMock)

    const matchedMock =
        apiMock[requestUrl as keyof typeof apiMock] ||
        apiMock[
            mockPaths.find((path) =>
                matchPath(path, requestUrl)
            ) as keyof typeof apiMock
        ]

    if (!matchedMock) {
        // eslint-disable-next-line no-console
        console.error(`Failed to find mock for URL[${requestUrl}]`)
        process.exit(-1)
    }

    const responder: ResponseFunction | null =
        (matchedMock as Record<string, ResponseFunction>)[requestMethod] || null

    if (!responder) {
        // eslint-disable-next-line no-console
        console.error(
            `Failed to find mock for URL[${requestUrl}] method [${requestMethod}]`
        )
        process.exit(-1)
    }

    return responder
}

const cleanupGnosisUrl = (input: string): string => {
    const cleanedUrl = input.replace(GNOSIS_PAY_BASE_URL, '')

    return cleanedUrl.replace(/\cards\/.*\//g, '/cards/:cardId/') // TODO @resetko-zeal remove replace, use path matching instead
}

const cleanupProxyUrl = (input: string): string => {
    const cleanedUrl = input.replace(API_MOCK_V2_BASE_URL, '')
    const urlWithoutQuery = cleanedUrl.split('?')[0]
    return urlWithoutQuery.replace(/\/token_price\/[^/]+/, '/token_price/:id') // TODO @resetko-zeal remove replace, use path matching instead
}

const cleanupSwapsIOUrl = (input: string): string => {
    const cleanedUrl = input.replace(SWAPSIO_BASE_URL, '')
    const urlWithoutQuery = cleanedUrl.split('?')[0]
    return urlWithoutQuery
}

const cleanupSocketUrl = (input: string): string => {
    const cleanedUrl = input.replace(SOCKET_BASE_URL, '')
    const urlWithoutQuery = cleanedUrl.split('?')[0]
    return urlWithoutQuery
}

const getSocketApiResponse = async (
    url: string,
    init: Parameters<Fetch>[1]
): ReturnType<Fetch> => {
    const requestMethod = (init?.method?.toLowerCase() ||
        '') as keyof SocketApiMock
    const requestUrl = cleanupSocketUrl(url)

    const matchedMethod = socketApiMock[requestMethod]
    if (!matchedMethod) {
        // eslint-disable-next-line no-console
        console.error(
            `Failed to find socket api mocked method[${requestMethod}]`
        )
        process.exit(-1)
    }

    const responder: FetchResponseFunction | null =
        (matchedMethod as Record<string, FetchResponseFunction>)[requestUrl] ||
        null

    if (!responder) {
        // eslint-disable-next-line no-console
        console.error(
            `Failed to find socket api mock for method[${requestMethod}] URL[${requestUrl}]`
        )
        process.exit(-1)
    }

    const [status, data] = await Promise.resolve(responder(url, init))

    if (isFailedStatus(status)) {
        throw new HttpError({
            method: requestMethod,
            requestBody: init?.body,
            responseBody: data,
            responseHeaders: {},
            status,
            url: requestUrl,
        })
    }

    return Promise.resolve({
        ok: !isFailedStatus(status),
        json: () => Promise.resolve(data),
        text: () => Promise.resolve(JSON.stringify(data)),
    } as Response)
}

const getGnosisApiResponse = async (
    url: string,
    init: Parameters<Fetch>[1]
): ReturnType<Fetch> => {
    const requestMethod = (init?.method?.toLowerCase() ||
        '') as keyof GnosisApiMock
    const requestUrl = cleanupGnosisUrl(url)

    const matchedMethod = gnosisApiMock[requestMethod]
    if (!matchedMethod) {
        // eslint-disable-next-line no-console
        console.error(
            `Failed to find gnosis api mocked method[${requestMethod}]`
        )
        process.exit(-1)
    }

    const responder: FetchResponseFunction | null =
        (matchedMethod as Record<string, FetchResponseFunction>)[requestUrl] ||
        null

    if (!responder) {
        // eslint-disable-next-line no-console
        console.error(
            `Failed to find gnosis api mock for method[${requestMethod}] URL[${requestUrl}]`
        )
        process.exit(-1)
    }

    const [status, data] = await Promise.resolve(responder(url, init))

    if (isFailedStatus(status)) {
        throw new HttpError({
            method: requestMethod,
            requestBody: init?.body,
            responseBody: data,
            responseHeaders: {},
            status,
            url: requestUrl,
        })
    }

    return Promise.resolve({
        ok: !isFailedStatus(status),
        json: () => Promise.resolve(data),
        text: () => Promise.resolve(JSON.stringify(data)),
    } as Response)
}

const getRPCMockResponse = (
    url: string,
    init: Parameters<Fetch>[1]
): FetchResponseFunction | null => {
    const requestMethod = (init?.method?.toLowerCase() || '') as keyof ApiMockV2
    const requestUrl = cleanupProxyUrl(url)
    const rawBody = init?.body || null

    if (
        !requestUrl.match(/\/proxy\/rpc\/0x[0-9a-f]+/gi) ||
        requestMethod !== 'post' ||
        !rawBody ||
        typeof rawBody !== 'string'
    ) {
        return null
    }

    const body = JSON.parse(rawBody)

    if (Array.isArray(body)) {
        const responders: {
            request: unknown
            responder: FetchResponseFunction | null
        }[] = body.map((request) => ({
            responder: rpcMocks[request.method as RPCRequest['method']] || null,
            request,
        }))

        if (responders.find((responder) => responder.responder === null)) {
            return null
        }

        const nonNullResponders: {
            request: unknown
            responder: FetchResponseFunction
        }[] = responders.filter(
            (responder): responder is (typeof nonNullResponders)[0] =>
                !!responder.responder
        )

        return async (...params: Parameters<Fetch>) => {
            const responses = await Promise.all(
                nonNullResponders.map(async (responder) => {
                    const [_, resp] = await responder.responder(params[0], {
                        ...params[1],
                        body: JSON.stringify(responder.request),
                    })

                    if (
                        typeof responder.request === 'object' &&
                        responder.request !== null &&
                        'id' in responder.request &&
                        typeof resp === 'object' &&
                        resp !== null
                    ) {
                        return {
                            ...resp,
                            id: responder.request.id,
                        }
                    }

                    return resp
                })
            )

            return [200, responses]
        }
    }

    return rpcMocks[body.method as RPCRequest['method']] || null
}

const getApiMockV2Response = async (
    url: string,
    init: Parameters<Fetch>[1]
): ReturnType<Fetch> => {
    const requestMethod = (init?.method?.toLowerCase() || '') as keyof ApiMockV2
    const requestUrl = cleanupProxyUrl(url)

    const matchedMethod: Record<string, FetchResponseFunction> =
        apiMock2[requestMethod]

    if (!matchedMethod) {
        // eslint-disable-next-line no-console
        console.error(`Failed to find api mocked method[${requestMethod}]`)
        process.exit(-1)
    }

    const mockPaths = keys(matchedMethod)

    const responder: FetchResponseFunction | null =
        matchedMethod[requestUrl] ||
        matchedMethod[
            mockPaths.find((path) => matchPath(path, requestUrl)) || ''
        ] ||
        getRPCMockResponse(url, init) ||
        null

    if (!responder) {
        // eslint-disable-next-line no-console
        console.error(
            `Failed to find api mock for method[${requestMethod}] URL[${requestUrl}]`
        )
        process.exit(-1)
    }

    const [status, data] = await Promise.resolve(responder(url, init))

    return {
        ok: !isFailedStatus(status),
        status,
        headers: new Headers(),
        json: () => Promise.resolve(data),
        text: () => Promise.resolve(JSON.stringify(data)),
    } as Response
}

const getSwapsIOApiResponse = async (
    url: string,
    init: Parameters<Fetch>[1]
): ReturnType<Fetch> => {
    const requestMethod = (init?.method?.toLowerCase() ||
        '') as keyof SwapsIOApiPaths
    const requestUrl = cleanupSwapsIOUrl(url)

    const matchedMethod: Record<string, FetchResponseFunction> =
        swapsIOApiMock[requestMethod]

    if (!matchedMethod) {
        // eslint-disable-next-line no-console
        console.error(`Failed to find api mocked method[${requestMethod}]`)
        process.exit(-1)
    }

    const mockPaths = keys(matchedMethod)

    const responder: FetchResponseFunction | null =
        matchedMethod[requestUrl] ||
        matchedMethod[
            mockPaths.find((path) => matchPath(path, requestUrl)) || ''
        ] ||
        getRPCMockResponse(url, init) ||
        null

    if (!responder) {
        // eslint-disable-next-line no-console
        console.error(
            `Failed to find api mock for method[${requestMethod}] URL[${requestUrl}]`
        )
        process.exit(-1)
    }

    const [status, data] = await Promise.resolve(responder(url, init))

    return {
        ok: !isFailedStatus(status),
        status,
        headers: new Headers(),
        json: () => Promise.resolve(data),
        text: () => Promise.resolve(JSON.stringify(data)),
    } as Response
}

const getResponse = async (
    config: AxiosRequestConfig
): Promise<AxiosResponse<unknown>> => {
    const requestUrl = config.url || ''
    const requestMethod = config.method || ''

    const responder = getMockResponder(config)

    const [status, data] = responder(config)

    if (isFailedStatus(status)) {
        throw new HttpError({
            method: requestMethod,
            requestBody: '',
            responseBody: data,
            responseHeaders: {},
            status,
            url: requestUrl,
        })
    }

    return {
        config,
        data,
        status,
        headers: {},
        statusText: 'OK',
    }
}

const mockAdapter: AxiosAdapter = (config) => getResponse(config)

export const getMocks = () => {
    axiosInstance.defaults.adapter = mockAdapter

    apiMock = {
        '/wallet/rate/crypto': {
            post: () => [500, null],
        },
        '/wallet/rpc/': {
            post: () => [500, null],
        },
        '/wallet/rpc/paymaster/': {
            post: () => [500, null],
        },

        '/wallet/metrics': {
            post: () => [200, null],
        },

        '/wallet/safetychecks/connection/': {
            post: () => [200, safetyChecksPassed],
        },

        '/wallet/transaction/simulate/': {
            post: () => [200, approvalSimulation],
        },

        /* Template string URLs (not auto-requested by type check, will fail during test runtime) */
        '/wallet/portfolio/:address/': {
            get: () => [200, initialPortfolio],
        },

        '/wallet/transaction/activity/:address/': {
            get: () => [200, initialHistory],
        },

        '/wallet/transaction/:hash/result': {
            get: () => [500, null],
        },

        '/wallet/rpc-sign-message/simulate/': {
            post: () => [500, null],
        },
        '/wallet/user-ops-transaction/simulate': {
            post: () => [500, null],
        },
        '/wallet/user-ops-transaction/result': {
            post: () => [500, null],
        },
        '/wallet/transaction/:address/last-card-topup/': {
            get: () => [500, null],
        },
    }

    return apiMock
}

export const getSocketApiMocks = () => {
    socketApiMock = {
        get: {
            '/bridge-status': () => [500, null],
            '/quote': () => [500, null],
            '/supported/chains': () => [500, null],
            '/token-lists/from-token-list': () => [500, null],
            '/token-lists/to-token-list': () => [500, null],
        },
        post: {
            '/build-tx': () => [500, null],
        },
    }

    return socketApiMock
}

export const getSwapsIOApiMocks = () => {
    swapsIOApiMock = {
        get: {
            '/swaps/:hash/': () => [500, null],
            '/swaps/:hash/data': () => [500, null],
            '/quote': () => [500, null],
            '/users/:hash/swaps': () => [500, null],
        },
        post: {
            '/swaps': () => [500, null],
            '/swaps/:address/submit': () => [500, null],
        },
    }

    return swapsIOApiMock
}

export const getGnosisApiMocks = () => {
    gnosisApiMock = {
        get: {
            '/user': () => [500, null],
            '/auth/nonce': () => [500, null],
            '/transactions': () => [500, null],
            '/account-balances': () => [500, null],
            '/cards/:cardId/details': () => [500, null],
            '/cards/:cardId/pin': () => [500, null],
            '/cards': () => [500, null],
            '/cards/:cardId/status': () => [500, null],
            '/ibans/available': () => [500, null],
            '/eoa-accounts': () => [500, null],
            '/kyc/integration/sdk': () => [500, null],
            '/account/signature-payload': () => [500, null],
            '/safe-config': () => [500, null],
            '/source-of-funds': () => [500, null],
            '/order': () => [500, null],
            '/user/terms': () => [500, null],
        },
        post: {
            '/safe/set-currency': () => [500, null],
            '/delay-relay': () => [500, null],
            '/auth/challenge': () => [500, null],
            '/auth/signup/otp': () => [500, null],
            '/auth/signup': () => [500, null],
            '/user/terms': () => [500, null],
            '/cards/:cardId/freeze': () => [500, null],
            '/cards/:cardId/unfreeze': () => [500, null],
            '/external/ibans/transfer': () => [500, null],
            '/external/ibans': () => [500, null],
            '/eoa-accounts': () => [500, null],
            '/source-of-funds': () => [500, null],
            '/order/create': () => [500, null],
            '/verification/check': () => [500, null],
            '/account': () => [500, null],
            '/verification': () => [500, null],
        },
        patch: {
            '/account/deploy-safe-modules': () => [500, null],
        },
        delete: {
            '/eoa-accounts/:id': () => [500, null],
        },
        put: {
            '/order/:id/confirm-payment': () => [500, null],
        },
    }

    return gnosisApiMock
}

export const getApiMocksV2 = () => {
    apiMock2 = {
        get: {
            '/api/gnosispay/is-zeal/:id': () => [200, true],
            '/api/unblock/webhook/:user_id_hash': () => [500, null],
            '/indexer/card-safe': () => [500, null],
            '/indexer/erc20-tokens/:network/:address': () => [500, null],
            '/indexer/transactions/:network/:address': () => [500, null],
            '/proxy/unblock/user/bank-account/remote': () => [
                200,
                unblockRemoteGBPandEURAccounts,
            ],
            '/proxy/cgv3/simple/token_price/:network': () => [500, null],
            '/proxy/cgv3/exchange_rates': () => [200, usdRates],
            '/proxy/cgv3/coins/list': () => [500, null],
            '/proxy/cgv3/simple/token_price/:id': () => [
                200,
                {
                    '0x0d500b1d8e8ef31e21c99d1db9a6444d3adf1270': {
                        usd: 0.257467,
                    },
                    '0xc02aaa39b223fe8d0a0e5c4f27ead9083c756cc2': {
                        usd: 2783.27,
                    },
                    '0xbb4cdb9cbd36b01bd1cbaebf2de08d9173bc095c': {
                        usd: 581.94,
                    },
                    '0x4200000000000000000000000000000000000006': {
                        usd: 2734.18,
                    },
                },
            ],
            '/proxy/unblock/user/token-preferences': () => [200, null],
            '/proxy/unblock/user/transactions': () => [200, []],
            '/proxy/unblock/user/bank-account/unblock': () => [
                200,
                unblockGBPandEURaccounts,
            ],
            '/proxy/dappr/dapps': () => [500, null],
            '/proxy/unblock/user/kyc/applicant/token': () => [500, null],
            '/proxy/unblock/fees': () => [200, unblockNoFee],
            '/proxy/unblock/exchange-rates': () => [200, unblockGBPtoUSDRate],
            '/proxy/unblock/user': () => [200, unblockFullUser],
            '/proxy/unblock/user/kyc/applicant': () => [500, null],
            '/proxy/cba/sky/usds/total-supply': () => [500, null],
            '/proxy/cba/spark/savings/usds/backed/': () => [500, null],
            '/proxy/dbk/user/all_history_list': () => [500, null],
            '/proxy/dbk/user/used_chain_list': () => [500, null],
            '/proxy/dbk/user/all_token_list': () => [500, null],
            '/proxy/dbk/user/all_nft_list': () => [500, null],
            '/proxy/dbk/user/all_complex_protocol_list': () => [500, null],
        },
        post: {
            '/api/metrics': () => [200, null],
            '/api/gnosispay/signup': () => [500, null],
            '/proxy/ba/:network/v0/validate/transaction': () => [500, null],
            '/proxy/ba/:network/v0/validate/json-rpc': () => [500, null],
            '/api/monerium/auth': () => [500, null],
            '/api/notifications/subscribe': () => [500, null],
            '/api/brewards/claim': () => [500, null],
            '/proxy/unblock/user/bank-account/remote': () => [500, null],
            '/proxy/unblock/user/bank-account/unblock': () => [500, null],
            '/proxy/unblock/user/kyc/applicant': () => [500, null],
            '/proxy/unblock/auth/login': () => [200, successfulUnblockLogin],
            '/proxy/unblock/user': () => [500, null],
            '/proxy/simulator/simulate': () => [500, null],
            '/proxy/simulator/simulate-bundle': () => [500, null],
        },
        delete: {
            '/proxy/unblock/user/bank-account/remote/:uuid': () => [500, null],
        },
        patch: {
            '/proxy/unblock/user/bank-account/remote': () => [500, null],
            '/proxy/unblock/user/token-preferences': () => [204, null],
            '/proxy/unblock/user': () => [500, null],
        },
    }

    return apiMock2
}

export const getRpcMocks = () => {
    rpcMocks = {
        metamask_getProviderState: jest.fn(() => [500, null]),
        eth_getFilterChanges: jest.fn(() => [500, null]),
        eth_uninstallFilter: jest.fn(() => [500, null]),
        eth_newFilter: jest.fn(() => [500, null]),
        net_listening: jest.fn(() => [500, null]),
        wallet_getSnaps: jest.fn(() => [500, null]),
        wallet_requestSnaps: jest.fn(() => [500, null]),
        wallet_invokeSnap: jest.fn(() => [500, null]),
        eth_getStorageAt: jest.fn(() => [500, null]),
        wallet_watchAsset: jest.fn(() => [500, null]),
        web3_clientVersion: jest.fn(() => [500, null]),
        debug_traceTransaction: jest.fn(() => [500, null]),
        eth_accounts: jest.fn(() => [500, null]),
        eth_blockNumber: jest.fn(() => [200, ethBlockNumber]),
        eth_call: jest.fn(() => [500, null]),
        eth_chainId: jest.fn(() => [500, null]),
        eth_coinbase: jest.fn(() => [500, null]),
        eth_estimateGas: jest.fn(() => [500, null]),
        eth_gasPrice: jest.fn(() => [500, null]),
        eth_feeHistory: jest.fn(() => [200, gnosisFeeHistory]),
        eth_getBalance: jest.fn(() => [200, ethBalance]),
        eth_getBlockByNumber: jest.fn(() => [200, ethGetBlockByNumber]),
        eth_getCode: jest.fn(() => [500, null]),
        eth_getLogs: jest.fn(() => [500, null]),
        eth_maxPriorityFeePerGas: jest.fn(() => [500, null]),
        eth_getBlockReceipts: jest.fn(() => [500, null]),
        eth_getTransactionByHash: jest.fn(() => [500, null]),
        eth_getTransactionCount: jest.fn(() => [200, ethGetTransactionCount]),
        eth_getTransactionReceipt: jest.fn(() => [500, null]),
        eth_requestAccounts: jest.fn(() => [500, null]),
        eth_sendRawTransaction: jest.fn(() => [
            200,
            ethSendRawTransactionResponse,
        ]),
        eth_sendTransaction: jest.fn(() => [500, null]),
        eth_signTypedData_v4: jest.fn(() => [500, null]),
        eth_signTypedData_v3: jest.fn(() => [500, null]),
        eth_signTypedData: jest.fn(() => [500, null]),
        net_version: jest.fn(() => [500, null]),
        personal_ecRecover: jest.fn(() => [500, null]),
        personal_sign: jest.fn(() => [500, null]),
        wallet_addEthereumChain: jest.fn(() => [500, null]),
        wallet_switchEthereumChain: jest.fn(() => [500, null]),
        wallet_requestPermissions: jest.fn(() => [500, null]),
        wallet_getPermissions: jest.fn(() => [500, null]),
        debug_getBadBlocks: jest.fn(() => [500, null]),
        debug_getRawBlock: jest.fn(() => [500, null]),
        debug_getRawHeader: jest.fn(() => [500, null]),
        debug_getRawReceipts: jest.fn(() => [500, null]),
        debug_getRawTransaction: jest.fn(() => [500, null]),
        debug_storageRangeAt: jest.fn(() => [500, null]),
        debug_getTrieFlushInterval: jest.fn(() => [500, null]),
        debug_traceBlock: jest.fn(() => [500, null]),
        debug_traceBlockByHash: jest.fn(() => [500, null]),
        debug_traceBlockByNumber: jest.fn(() => [500, null]),
        debug_traceCall: jest.fn(() => [500, null]),
        trace_block: jest.fn(() => [500, null]),
        trace_call: jest.fn(() => [500, null]),
        trace_callMany: jest.fn(() => [500, null]),
        trace_filter: jest.fn(() => [500, null]),
        trace_rawTransaction: jest.fn(() => [500, null]),
        trace_replayBlockTransactions: jest.fn(() => [500, null]),
        trace_replayTransaction: jest.fn(() => [500, null]),
        trace_transaction: jest.fn(() => [500, null]),
        txpool_status: jest.fn(() => [500, null]),
        txpool_content: jest.fn(() => [500, null]),
        txpool_inspect: jest.fn(() => [500, null]),
        txpool_contentFrom: jest.fn(() => [500, null]),
    }

    return rpcMocks
}
