import React from 'react'
import { StyleSheet, View } from 'react-native'

import { InternalTile, Props as InternalTileProps } from './InternalTile'
import { SvgLabel } from './SvgLabel'

import { Column } from '../Column'
import {
    Color as TextColor,
    Text,
    Variant as TextVariant,
    Weight as TextWeight,
} from '../Text'

const styles = StyleSheet.create({
    disabled: {
        opacity: 0.5,
    },

    text_container: {
        height: 144,
        padding: 16,
        width: '100%',
    },
})

type Props = {
    variant: InternalTileProps['variant']
    label: (props: {
        variant: Extract<TextVariant, 'title3'>
        color: TextColor
        weight: TextWeight
    }) => React.ReactNode
    title: React.ReactNode
    subtitle?: (props: {
        variant: Extract<TextVariant, 'paragraph'>
        color: TextColor
        weight: TextWeight
    }) => React.ReactNode
    onClick: (() => void) | null
    disabled: boolean
}

export const Tile = ({
    variant,
    title,
    label,
    disabled,
    subtitle,
    onClick,
}: Props) => {
    return (
        <InternalTile variant={variant} disabled={disabled} onClick={onClick}>
            {({ colorVariant }) => (
                <>
                    <View style={styles.text_container}>
                        <Column
                            spacing={2}
                            alignX="start"
                            alignY={!!subtitle ? 'stretch' : 'center'}
                            fill={!!subtitle ? true : undefined}
                        >
                            {label({
                                color: colorVariant.secondary,
                                variant: 'title3',
                                weight: 'medium',
                            })}
                            <Column spacing={8}>
                                <Text
                                    variant="title1"
                                    weight="medium"
                                    color={colorVariant.primary}
                                >
                                    {title}
                                </Text>
                                {subtitle
                                    ? subtitle({
                                          color: colorVariant.secondary,
                                          variant: 'paragraph',
                                          weight: 'medium',
                                      })
                                    : null}
                            </Column>
                        </Column>
                    </View>
                </>
            )}
        </InternalTile>
    )
}

Tile.SvgLabel = SvgLabel
