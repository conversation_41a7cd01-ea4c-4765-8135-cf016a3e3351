import Big from 'big.js'
// eslint-disable-next-line no-restricted-imports
import { maxUint256 as viemMaxUint256 } from 'viem'

export const maxUint256 = viemMaxUint256

export const div = (value: bigint, divisor: bigint): number =>
    Big(value.toString(10))
        .div(Big(divisor.toString(10)))
        .toNumber()

export const mulByNumber = (value: bigint, factor: number): bigint =>
    BigInt(Big(value.toString()).mul(factor).toFixed(0))

export const fromFixedWithFraction = (
    amount: string | null,
    fraction: number
): bigint => {
    const value = amount?.replace(/[^\d.]/gim, '') || '0' // We remove everything which is not digit or dot so Big can parse it
    return BigInt(Big(value).mul(Big(10).pow(fraction)).toFixed(0))
}

export const toFixedWithFraction = (value: bigint, fraction: number): string =>
    Big(value.toString(10))
        .div(Big(10).pow(fraction))
        .toFixed(fraction)
        .replace(/(\.0+|0+)$/, '')

/**
 * it's unsafe to turn bigint to number, since it can overflow
 */
export const unsafe_toNumberWithFraction = (
    value: bigint,
    fraction: number
): number => Big(value.toString(10)).div(Big(10).pow(fraction)).toNumber()

/**
 * it's unsafe to turn number to bigint, since it can be unprecise for given fraction
 */
export const unsafe_fromNumberWithFraction = (
    value: number,
    fraction: number
): bigint => BigInt(Big(value).mul(Big(10).pow(fraction)).toFixed(0))

export const getDecimalsWithFraction = (
    value: bigint,
    fraction: number
): number => {
    const fractionalUnits = Big(value.toString(10))
        .mod(Big(10).pow(fraction))
        .abs()
    const fractionalPart = fractionalUnits.div(Big(10).pow(fraction))
    return fractionalPart.toNumber()
}

export const abs = (value: bigint): bigint =>
    BigInt(Big(value.toString()).abs().toFixed(0))

export const min = (...values: bigint[]): bigint =>
    values.reduce((acc, value) => (value < acc ? value : acc), values[0])

export const max = (...values: bigint[]): bigint =>
    values.reduce((acc, value) => (value > acc ? value : acc), values[0])

export const sum = (values: bigint[]): bigint =>
    values.reduce((acc, value) => acc + value, 0n)

export const floor = (value: bigint, fraction: number): bigint =>
    BigInt(
        Big(value.toString(10))
            .div(Big(10).pow(fraction))
            .round(0, Big.roundDown)
            .mul(Big(10).pow(fraction))
            .toFixed(0)
    )

export const median = (values: bigint[]): bigint => {
    const sorted = values.toSorted((a, b) => (a > b ? 1 : -1))
    const midIndex = Math.floor(sorted.length / 2)

    return sorted.length % 2
        ? sorted[midIndex]
        : (sorted[midIndex - 1] + sorted[midIndex]) / 2n
}

export const truncateFractionDigits = (
    value: bigint,
    fraction: number,
    dp: number
): bigint =>
    BigInt(
        Big(value.toString(10))
            .div(Big(10).pow(fraction))
            .round(dp, Big.roundDown)
            .mul(Big(10).pow(fraction))
            .toFixed(0)
    )
