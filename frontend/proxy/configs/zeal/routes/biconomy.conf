location ~ /bundler/(?<biconomy_network_hex_id>0x[0-9a-fA-F]+) {
    limit_except POST OPTIONS {
        deny all;
    }

    set $api_keys_secret_name "BICONOMY_BUNDLER_API_KEY";
    proxy_pass https://bundler.biconomy.io/api/v2/$biconomy_network_number/$api_keys_secret_value/;

    include zeal/kill-cache.conf;
    include zeal/add-cors-headers.conf;
    include zeal/kill-client-headers.conf;
    include zeal/kill-response-headers.conf;
}

location = /paymaster/0x1 {
    limit_except POST OPTIONS {
        deny all;
    }

    set $api_keys_secret_name "BICONOMY_PAYMSTER_ETHEREUM_API_KEY";
    proxy_pass https://paymaster.biconomy.io/api/v1/0x1/$api_keys_secret_value/;

    include zeal/kill-cache.conf;
    include zeal/add-cors-headers.conf;
    include zeal/kill-client-headers.conf;
    include zeal/kill-response-headers.conf;
}

location = /paymaster/0x89 {
    limit_except POST OPTIONS {
        deny all;
    }

    set $api_keys_secret_name "BICONOMY_PAYMSTER_POLYGON_API_KEY";
    proxy_pass https://paymaster.biconomy.io/api/v1/0x89/$api_keys_secret_value/;

    include zeal/kill-cache.conf;
    include zeal/add-cors-headers.conf;
    include zeal/kill-client-headers.conf;
    include zeal/kill-response-headers.conf;
}

location = /paymaster/0xe708 {
    limit_except POST OPTIONS {
        deny all;
    }

    set $api_keys_secret_name "BICONOMY_PAYMSTER_LINEA_API_KEY";
    proxy_pass https://paymaster.biconomy.io/api/v1/0xe708/$api_keys_secret_value/;

    include zeal/kill-cache.conf;
    include zeal/add-cors-headers.conf;
    include zeal/kill-client-headers.conf;
    include zeal/kill-response-headers.conf;
}

location = /paymaster/0xa4b1 {
    limit_except POST OPTIONS {
        deny all;
    }

    set $api_keys_secret_name "BICONOMY_PAYMSTER_ARBITRUM_API_KEY";
    proxy_pass https://paymaster.biconomy.io/api/v1/0xa4b1/$api_keys_secret_value/;

    include zeal/kill-cache.conf;
    include zeal/add-cors-headers.conf;
    include zeal/kill-client-headers.conf;
    include zeal/kill-response-headers.conf;
}

location = /paymaster/0xa86a {
    limit_except POST OPTIONS {
        deny all;
    }

    set $api_keys_secret_name "BICONOMY_PAYMSTER_AVALANCHE_API_KEY";
    proxy_pass https://paymaster.biconomy.io/api/v1/0xa86a/$api_keys_secret_value/;

    include zeal/kill-cache.conf;
    include zeal/add-cors-headers.conf;
    include zeal/kill-client-headers.conf;
    include zeal/kill-response-headers.conf;
}
location = /paymaster/0x38 {
    limit_except POST OPTIONS {
        deny all;
    }

    set $api_keys_secret_name "BICONOMY_PAYMSTER_BSC_API_KEY";
    proxy_pass https://paymaster.biconomy.io/api/v1/0x38/$api_keys_secret_value/;

    include zeal/kill-cache.conf;
    include zeal/add-cors-headers.conf;
    include zeal/kill-client-headers.conf;
    include zeal/kill-response-headers.conf;
}

location = /paymaster/0x64 {
    limit_except POST OPTIONS {
        deny all;
    }

    set $api_keys_secret_name "BICONOMY_PAYMSTER_GNOSIS_API_KEY";
    proxy_pass https://paymaster.biconomy.io/api/v1/0x64/$api_keys_secret_value/;

    include zeal/kill-cache.conf;
    include zeal/add-cors-headers.conf;
    include zeal/kill-client-headers.conf;
    include zeal/kill-response-headers.conf;
}
location = /paymaster/0xa {
    limit_except POST OPTIONS {
        deny all;
    }

    set $api_keys_secret_name "BICONOMY_PAYMSTER_OPTIMISM_API_KEY";
    proxy_pass https://paymaster.biconomy.io/api/v1/0xa/$api_keys_secret_value/;

    include zeal/kill-cache.conf;
    include zeal/add-cors-headers.conf;
    include zeal/kill-client-headers.conf;
    include zeal/kill-response-headers.conf;
}
location = /paymaster/0x2105 {
    limit_except POST OPTIONS {
        deny all;
    }

    set $api_keys_secret_name "BICONOMY_PAYMSTER_BASE_API_KEY";
    proxy_pass https://paymaster.biconomy.io/api/v1/0x2105/$api_keys_secret_value/;

    include zeal/kill-cache.conf;
    include zeal/add-cors-headers.conf;
    include zeal/kill-client-headers.conf;
    include zeal/kill-response-headers.conf;
}
location = /paymaster/0x13e31 {
    limit_except POST OPTIONS {
        deny all;
    }

    set $api_keys_secret_name "BICONOMY_PAYMSTER_BLAST_API_KEY";
    proxy_pass https://paymaster.biconomy.io/api/v1/0x13e31/$api_keys_secret_value/;

    include zeal/kill-cache.conf;
    include zeal/add-cors-headers.conf;
    include zeal/kill-client-headers.conf;
    include zeal/kill-response-headers.conf;
}

location = /paymaster/0xaa36a7 {
    limit_except POST OPTIONS {
        deny all;
    }

    set $api_keys_secret_name "BICONOMY_PAYMSTER_ETHEREUM_SEPOLIA_API_KEY";
    proxy_pass https://paymaster.biconomy.io/api/v1/0xaa36a7/$api_keys_secret_value/;

    include zeal/kill-cache.conf;
    include zeal/add-cors-headers.conf;
    include zeal/kill-client-headers.conf;
    include zeal/kill-response-headers.conf;
}
