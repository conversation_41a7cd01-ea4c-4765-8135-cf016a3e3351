location /dappr/ {
    limit_except GET OPTIONS {
        deny all;
    }

    set $api_keys_secret_name "DAPPRADAR_API_KEY";
    proxy_set_header x-api-key $api_keys_secret_value;
    
    proxy_ssl_server_name on;
    proxy_pass https://apis.dappradar.com/v2/;


    include zeal/kill-cache.conf;
    include zeal/add-cors-headers.conf;
    include zeal/kill-client-headers.conf;
    include zeal/kill-response-headers.conf;
}
