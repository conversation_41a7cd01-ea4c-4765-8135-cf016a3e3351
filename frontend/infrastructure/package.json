{"name": "infrastructure", "main": "index.ts", "scripts": {"deploy": "PULUMI_CONFIG_PASSPHRASE=$(aws secretsmanager get-secret-value --secret-id 'arn:aws:secretsmanager:eu-west-1:603699802407:secret:zeal-be/pulumi-passphrase-HxD8IY' --query SecretString --output text | jq -r '.\"pulumi-passphrase\"') && pulumi up"}, "devDependencies": {"@pulumi/command": "0.0.1-testwindows.signing", "@types/node": "20.11.29", "typescript": "5.5.3"}, "dependencies": {"@pulumi/aws": "6.58.0", "@pulumi/databricks": "1.71.0", "@pulumi/pulumi": "3.138.0"}}