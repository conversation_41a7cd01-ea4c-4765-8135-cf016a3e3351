import * as aws from '@pulumi/aws'
import * as pulumi from '@pulumi/pulumi'

import {
    GHCR_SECRET_ID,
    INFRA_SECRET_ID,
    WALLET_API_CONFIG_SECRET_ID,
} from '../constants'
import { HealthyEC2Instance } from '../helpers/healthy-ec2-instance'

type Params = {
    name: string
    az: string

    subnet: aws.ec2.Subnet
    vpc: aws.ec2.Vpc
    jumperSecurityGroup: aws.ec2.SecurityGroup
    allowSSM: boolean
}

const NGINX_PORT = 8833 // Fancy port just for some "security", because bots immediately try to knock the door on 80
const AMI = 'ami-08c208c3af7f04012' // Amazon Linux 2023 AMI arm64

export const ALLOWED_SECRETS = [
    'BICONOMY_BUNDLER_API_KEY',
    'BLOCKAID_API_KEY',
    'COIN_GECKO_API_KEY',
    'DEBANK_API_KEY',
    'PIMLICO_API_KEY',
    'TENDERLY_API_KEY',
    'UNBLOCK_API_KEY',

    'QUICKNODE_ARBITRUM_API_KEY',
    'QUICKNODE_AVALANCHE_API_KEY',
    'QUICKNODE_AVALANCHE_FUJI_API_KEY',
    'QUICKNODE_BASE_API_KEY',
    'QUICKNODE_BLAST_API_KEY',
    'QUICKNODE_BSC_API_KEY',
    'QUICKNODE_BSC_TESTNET_API_KEY',
    'QUICKNODE_CELO_API_KEY',
    'QUICKNODE_ETHEREUM_API_KEY',
    'QUICKNODE_ETHEREUM_SEPOLIA_API_KEY',
    'QUICKNODE_FANTOM_API_KEY',
    'QUICKNODE_GNOSIS_API_KEY',
    'QUICKNODE_LINEA_API_KEY',
    'QUICKNODE_MANTLE_API_KEY',
    'QUICKNODE_OPTIMISM_API_KEY',
    'QUICKNODE_POLYGON_API_KEY',
    'QUICKNODE_POLYGON_ZKEVM_API_KEY',
    'QUICKNODE_ZKSYNC_API_KEY',

    'CHAINSTACK_AURORA_API_KEY',
    'CHAINSTACK_AURORA_TESTNET_API_KEY',
    'CHAINSTACK_BSC_API_KEY',
    'CHAINSTACK_FANTOM_TESTNET_API_KEY',
    'CHAINSTACK_GNOSIS_API_KEY',
    'CHAINSTACK_OPTIMISM_API_KEY',
    'CHAINSTACK_POLYGON_API_KEY',
    'CHAINSTACK_ZKSYNC_API_KEY',

    'GETBLOCK_CRONOS_API_KEY',
    'GETBLOCK_OPBNB_API_KEY',

    'DRPC_API_KEY',
    'BICONOMY_PAYMSTER_ETHEREUM_API_KEY',
    'BICONOMY_PAYMSTER_POLYGON_API_KEY',
    'BICONOMY_PAYMSTER_LINEA_API_KEY',
    'BICONOMY_PAYMSTER_ARBITRUM_API_KEY',
    'BICONOMY_PAYMSTER_AVALANCHE_API_KEY',
    'BICONOMY_PAYMSTER_BSC_API_KEY',
    'BICONOMY_PAYMSTER_GNOSIS_API_KEY',
    'BICONOMY_PAYMSTER_OPTIMISM_API_KEY',
    'BICONOMY_PAYMSTER_BASE_API_KEY',
    'BICONOMY_PAYMSTER_BLAST_API_KEY',
    'BICONOMY_PAYMSTER_ETHEREUM_SEPOLIA_API_KEY',
    'DAPPRADAR_API_KEY',
]

export const proxyInstance = ({
    name,
    az,
    subnet,
    vpc,
    jumperSecurityGroup,
    allowSSM,
}: Params): { instance: HealthyEC2Instance; port: number } => {
    const securityGroup = new aws.ec2.SecurityGroup(
        `zeal-be-${name}-sec-group`,
        {
            vpcId: vpc.id,
            ingress: [
                {
                    protocol: 'tcp',
                    fromPort: 22,
                    toPort: 22,
                    securityGroups: [jumperSecurityGroup.id],
                },
                {
                    protocol: 'tcp',
                    fromPort: NGINX_PORT,
                    toPort: NGINX_PORT,
                    cidrBlocks: ['0.0.0.0/0'],
                },
            ],
            egress: [
                {
                    protocol: '-1',
                    fromPort: 0,
                    toPort: 0,
                    cidrBlocks: ['0.0.0.0/0'],
                },
            ],
        }
    )

    const role = new aws.iam.Role(`zeal-be-${name}-role`, {
        assumeRolePolicy: aws.iam.assumeRolePolicyForPrincipal({
            Service: 'ec2.amazonaws.com',
        }),
    })

    const secretPolicy = new aws.iam.Policy(
        `zeal-be-${name}-secret-access-policy`,
        {
            policy: JSON.stringify({
                Version: '2012-10-17',
                Statement: [
                    {
                        Effect: 'Allow',
                        Action: ['secretsmanager:GetSecretValue'],
                        Resource: [
                            GHCR_SECRET_ID,
                            INFRA_SECRET_ID,
                            WALLET_API_CONFIG_SECRET_ID,
                        ],
                    },
                ],
            }),
        }
    )

    new aws.iam.RolePolicyAttachment(
        `zeal-be-${name}-infra-secret-policy-attachment`,
        {
            policyArn: secretPolicy.arn,
            role: role.name,
        }
    )

    if (allowSSM) {
        new aws.iam.RolePolicyAttachment(`zeal-be-${name}-ssm-policy`, {
            policyArn: 'arn:aws:iam::aws:policy/AmazonSSMManagedInstanceCore',
            role: role.name,
        })
    }

    const userData = pulumi.interpolate`#!/bin/bash
yum update -y
yum install -y docker

#!/bin/bash

cat > /etc/sysctl.d/99-conntrack.conf << EOF
# Increase connection tracking table size
net.netfilter.nf_conntrack_max = 32768
net.netfilter.nf_conntrack_buckets = 8192

# Reduce timeouts to clear connections faster
net.netfilter.nf_conntrack_tcp_timeout_time_wait = 10
net.netfilter.nf_conntrack_tcp_timeout_established = 7200
EOF
modprobe nf_conntrack
sysctl -p /etc/sysctl.d/99-conntrack.conf

usermod -a -G docker ec2-user

systemctl enable docker
systemctl start docker

aws secretsmanager get-secret-value --secret-id ${INFRA_SECRET_ID} --query 'SecretString' --output text | jq -r '."jumper-public-key"' >> /home/<USER>/.ssh/authorized_keys

docker swarm init

secret=$(aws secretsmanager get-secret-value --secret-id "${GHCR_SECRET_ID}" --query SecretString --output text)
echo $(echo $secret | jq -r '.password') | docker login ghcr.io -u $(echo $secret | jq -r '.password') --password-stdin

aws secretsmanager get-secret-value --secret-id "${WALLET_API_CONFIG_SECRET_ID}" --query SecretString --output text | \\
jq -r '{${ALLOWED_SECRETS.map((secret) => `"${secret}"`).join(', ')}}' | \\
jq -r '"map $api_keys_secret_name $api_keys_secret_value {default \\"\\";" + (to_entries | map(.key + " \\"" + .value + "\\";") | join("")) + "}"' | \\
docker secret create proxy_nginx_api_keys.conf -

docker service create \\
    --secret proxy_nginx_api_keys.conf \\
    --update-order start-first \\
    --with-registry-auth \\
    --name zeal-be-proxy \\
    --log-driver json-file \\
    --log-opt max-size=100m \\
    --log-opt max-file=3 \\
    --dns ******* \\
    --replicas 1 \\
    --publish ${NGINX_PORT}:${NGINX_PORT} \\
    ghcr.io/zealwallet/zeal-be-proxy:latest

docker logout ghcr.io

cat > /tmp/subscript.sh << EOF
EOF

chown ec2-user:ec2-user /tmp/subscript.sh && chmod a+x /tmp/subscript.sh
sleep 1; su - ec2-user -c "/tmp/subscript.sh"

touch /tmp/instance-ready
`

    const instance = new HealthyEC2Instance(
        `zeal-be-${name}-instance`,
        {
            instanceType: 't4g.nano',
            availabilityZone: az,
            ami: AMI,
            subnetId: subnet.id,
            iamInstanceProfile: new aws.iam.InstanceProfile(
                `zeal-be-${name}-instance-profile`,
                { role: role.name }
            ).name,
            vpcSecurityGroupIds: [securityGroup.id],
            creditSpecification: {
                cpuCredits: 'standard',
            },
            tags: { Name: `zeal-be-${name}-instance` },
            userData,

            healthCheckPath: '/health',
            healthCheckPort: NGINX_PORT,
        },
        {
            replaceOnChanges: ['userData'],
        }
    )

    return { instance, port: NGINX_PORT }
}
